package food

import (
	"errors"
	"gorm.io/gorm"
	"shikeyinxiang/internal/entities"
	"shikeyinxiang/internal/pkg/errs"
)

// IFoodRepo 定义了食物仓库需要实现的所有方法
type IFoodRepo interface {
	Create(food *entities.Food) error
	GetByID(id int) (*entities.Food, error)
	GetByIDWithCategory(id int) (*entities.Food, error)
	Update(food *entities.Food) error
	UpdateImageURL(id int, imageURL string) error
	Delete(id int) error
	ListWithCategory(offset, limit int) ([]*entities.Food, int64, error)
	SearchWithCategory(keyword string, categoryID *int, offset, limit int) ([]*entities.Food, int64, error)
	ExistsByName(name string) (bool, error)
	GetByCategory(categoryID int, offset, limit int) ([]*entities.Food, int64, error)
	CountByCategory(categoryID int) (int64, error)
}

// foodRepository 食物仓储
type foodRepository struct {
	db *gorm.DB
}

// NewFoodRepository 创建食物仓储实例
func NewFoodRepository(db *gorm.DB) IFoodRepo {
	return &foodRepository{
		db: db,
	}
}

// 确保 foodRepository 实现了 IFoodRepo 接口
var _ IFoodRepo = &foodRepository{}

// Create 创建新食物
func (r *foodRepository) Create(food *entities.Food) error {
	if err := r.db.Create(food).Error; err != nil {
		// 检查是否是重复键错误
		if errs.IsDuplicateKeyError(err) {
			if errs.ContainsField(err.Error(), "food_name") {
				return errs.New(errs.CodeFoodAlreadyExists, "food name already exists")
			}
		}
		return errs.Wrap(errs.CodeDatabaseError, "failed to create food", err)
	}
	return nil
}

// GetByID 根据ID获取食物
func (r *foodRepository) GetByID(id int) (*entities.Food, error) {
	var food entities.Food
	if err := r.db.Where("id = ?", id).First(&food).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errs.New(errs.CodeFoodNotFound, "food not found")
		}
		return nil, errs.Wrap(errs.CodeDatabaseError, "failed to query food by id", err)
	}
	return &food, nil
}

// GetByIDWithCategory 根据ID获取食物（包含分类信息）
func (r *foodRepository) GetByIDWithCategory(id int) (*entities.Food, error) {
	var food entities.Food
	if err := r.db.Preload("Category").Where("id = ?", id).First(&food).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errs.New(errs.CodeFoodNotFound, "food not found")
		}
		return nil, errs.Wrap(errs.CodeDatabaseError, "failed to query food with category by id", err)
	}
	return &food, nil
}

// Update 更新食物信息
func (r *foodRepository) Update(food *entities.Food) error {
	if err := r.db.Save(food).Error; err != nil {
		return errs.Wrap(errs.CodeDatabaseError, "failed to update food", err)
	}
	return nil
}

// UpdateImageURL 更新食物图片URL
func (r *foodRepository) UpdateImageURL(id int, imageURL string) error {
	result := r.db.Model(&entities.Food{}).Where("id = ?", id).Update("image_url", imageURL)
	if result.Error != nil {
		return errs.Wrap(errs.CodeDatabaseError, "failed to update food image URL", result.Error)
	}
	if result.RowsAffected == 0 {
		return errs.New(errs.CodeFoodNotFound, "food not found")
	}
	return nil
}

// Delete 删除食物
func (r *foodRepository) Delete(id int) error {
	if err := r.db.Delete(&entities.Food{}, id).Error; err != nil {
		return errs.Wrap(errs.CodeDatabaseError, "failed to delete food", err)
	}
	return nil
}

// ListWithCategory 获取食物列表（分页，包含分类信息）
func (r *foodRepository) ListWithCategory(offset, limit int) ([]*entities.Food, int64, error) {
	var foods []*entities.Food
	var total int64

	// 获取总数
	if err := r.db.Model(&entities.Food{}).Count(&total).Error; err != nil {
		return nil, 0, errs.Wrap(errs.CodeDatabaseError, "failed to count foods", err)
	}

	// 获取分页数据（包含分类信息）
	if err := r.db.Preload("Category").Offset(offset).Limit(limit).Order("id DESC").Find(&foods).Error; err != nil {
		return nil, 0, errs.Wrap(errs.CodeDatabaseError, "failed to list foods with category", err)
	}

	return foods, total, nil
}

// SearchWithCategory 搜索食物（支持关键词和分类筛选，包含分类信息）
func (r *foodRepository) SearchWithCategory(keyword string, categoryID *int, offset, limit int) ([]*entities.Food, int64, error) {
	var foods []*entities.Food
	var total int64

	query := r.db.Model(&entities.Food{})

	// 添加关键词搜索条件
	if keyword != "" {
		query = query.Where("food_name LIKE ?", "%"+keyword+"%")
	}

	// 添加分类筛选条件
	if categoryID != nil {
		query = query.Where("category_id = ?", *categoryID)
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, errs.Wrap(errs.CodeDatabaseError, "failed to count search foods with category", err)
	}

	// 获取分页数据（包含分类信息）
	if err := query.Preload("Category").Offset(offset).Limit(limit).Order("id DESC").Find(&foods).Error; err != nil {
		return nil, 0, errs.Wrap(errs.CodeDatabaseError, "failed to search foods with category", err)
	}

	return foods, total, nil
}

// ExistsByName 检查食物名称是否已存在
func (r *foodRepository) ExistsByName(name string) (bool, error) {
	var count int64
	if err := r.db.Model(&entities.Food{}).Where("food_name = ?", name).Count(&count).Error; err != nil {
		return false, errs.Wrap(errs.CodeDatabaseError, "failed to check food name exists", err)
	}
	return count > 0, nil
}

// GetByCategory 根据分类获取食物列表
func (r *foodRepository) GetByCategory(categoryID int, offset, limit int) ([]*entities.Food, int64, error) {
	var foods []*entities.Food
	var total int64

	query := r.db.Model(&entities.Food{}).Where("category_id = ?", categoryID)

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, errs.Wrap(errs.CodeDatabaseError, "failed to count foods by category", err)
	}

	// 获取分页数据
	if err := query.Offset(offset).Limit(limit).Order("id DESC").Find(&foods).Error; err != nil {
		return nil, 0, errs.Wrap(errs.CodeDatabaseError, "failed to get foods by category", err)
	}

	return foods, total, nil
}

// CountByCategory 统计指定分类下的食物数量
func (r *foodRepository) CountByCategory(categoryID int) (int64, error) {
	var count int64
	if err := r.db.Model(&entities.Food{}).Where("category_id = ?", categoryID).Count(&count).Error; err != nil {
		return 0, errs.Wrap(errs.CodeDatabaseError, "failed to count foods by category", err)
	}
	return count, nil
}
