package errs

import (
	"fmt"
	"strings"
)

// CodeError 统一的业务错误类型
type CodeError struct {
	Code    string // 业务错误码，如 "FILE.INVALID_PARAMS"
	Message string // 可定制的用户级友好提示
	Err     error  // 原始错误，可选，用于链路追踪/日志
}

func (e *CodeError) Error() string {
	if e.Err != nil {
		return fmt.Sprintf("%s: %s | %v", e.Code, e.Message, e.Err)
	}
	return fmt.Sprintf("%s: %s", e.Code, e.Message)
}

// New 创建新的 CodeError
func New(code, message string) *CodeError {
	return &CodeError{Code: code, Message: message}
}

// Wrap 包装原始错误为 CodeError
func Wrap(code, message string, err error) *CodeError {
	return &CodeError{Code: code, Message: message, Err: err}
}

// IsDuplicateKeyError 检测是否是重复键错误
func IsDuplicateKeyError(err error) bool {
	errStr := err.Error()
	return ContainsField(errStr, "Duplicate entry") ||
		ContainsField(errStr, "UNIQUE constraint failed") ||
		ContainsField(errStr, "duplicate key")
}

// ContainsField 检查错误信息中是否包含指定字段
func ContainsField(errStr, field string) bool {
	return strings.Contains(errStr, field)
}
