package errs

// 文件模块错误码
const (
	CodeFileUnsupportedType = "FILE.UNSUPPORTED_TYPE"
	CodeFileNotFound        = "FILE.NOT_FOUND"
	CodeFileStorageError    = "FILE.STORAGE_ERROR"
)

// 用户模块错误码
const (
	CodeUserNotFound              = "USER.NOT_FOUND"
	CodeUserAlreadyExists         = "USER.ALREADY_EXISTS"
	CodeUserDisabled              = "USER.DISABLED"
	CodeUserEmailExists           = "USER.EMAIL_EXISTS"
	CodeUserWeakPassword          = "USER.WEAK_PASSWORD"
	CodeUserCreateFailed          = "USER.CREATE_FAILED"
	CodeUserUpdateFailed          = "USER.UPDATE_FAILED"
	CodeUserQueryFailed           = "USER.QUERY_FAILED"
	CodeUserNutritionGoalNotFound = "USER.NUTRITION_GOAL_NOT_FOUND"
)

// 认证模块错误码
const (
	CodeAuthInvalidCredentials      = "AUTH.INVALID_CREDENTIALS"
	CodeAuthUserNotFound            = "AUTH.USER_NOT_FOUND"
	CodeAuthInvalidPassword         = "AUTH.INVALID_PASSWORD"
	CodeAuthTokenExpired            = "AUTH.TOKEN_EXPIRED"
	CodeAuthTokenInvalid            = "AUTH.TOKEN_INVALID"
	CodeAuthPermissionDenied        = "AUTH.PERMISSION_DENIED"
	CodeAuthInsufficientPermissions = "AUTH.INSUFFICIENT_PERMISSIONS"
	CodeAuthAccountDisabled         = "AUTH.ACCOUNT_DISABLED"
	CodeAuthTokenGenerationFailed   = "AUTH.TOKEN_GENERATION_FAILED"
	CodeAuthTokenBlacklistFailed    = "AUTH.TOKEN_BLACKLIST_FAILED"
	CodeAuthPasswordHashFailed      = "AUTH.PASSWORD_HASH_FAILED"
	CodeAuthLoginFailed             = "AUTH.LOGIN_FAILED"
	CodeAuthExternalServiceError    = "AUTH.EXTERNAL_SERVICE_ERROR"
)

// 食物模块错误码
const (
	CodeFoodNotFound         = "FOOD.NOT_FOUND"
	CodeFoodAlreadyExists    = "FOOD.ALREADY_EXISTS"
	CodeFoodCategoryNotFound = "FOOD.CATEGORY_NOT_FOUND"
)

// 饮食记录模块错误码
const (
	CodeDietRecordNotFound   = "DIET.RECORD_NOT_FOUND"
	CodeDietInvalidMealType  = "DIET.INVALID_MEAL_TYPE"
	CodeDietDuplicateRecord  = "DIET.DUPLICATE_RECORD"
	CodeDietInvalidDateRange = "DIET.INVALID_DATE_RANGE"
)

// 营养模块错误码
const (
	CodeNutritionNotFound         = "NUTRITION.NOT_FOUND"
	CodeNutritionAlreadyExists    = "NUTRITION.ALREADY_EXISTS"
	CodeNutritionGoalNotFound     = "NUTRITION.GOAL_NOT_FOUND"
	CodeNutritionInvalidDateRange = "NUTRITION.INVALID_DATE_RANGE"
	CodeNutritionCalculationError = "NUTRITION.CALCULATION_ERROR"
)

// 系统错误码
const (
	CodeInternal             = "SYSTEM.INTERNAL_ERROR"
	CodeDatabaseError        = "SYSTEM.DATABASE_ERROR"
	CodeExternalServiceError = "SYSTEM.EXTERNAL_SERVICE_ERROR"
	CodeSystemNotFound       = "SYSTEM.NOT_FOUND"
	CodeSystemInvalidParams  = "SYSTEM.INVALID_PARAMS"
)
