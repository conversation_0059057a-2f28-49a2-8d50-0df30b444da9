-- MySQL dump 10.13  Distrib 8.0.36, for Win64 (x86_64)
--
-- Host: localhost    Database: dubbo_demo
-- ------------------------------------------------------
-- Server version	8.0.36

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8mb4 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `diet_record_foods`
--

DROP TABLE IF EXISTS `diet_record_foods`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `diet_record_foods` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `diet_record_id` bigint NOT NULL COMMENT '关联饮食记录ID',
  `food_id` bigint NOT NULL COMMENT '食物ID',
  `food_name` varchar(50) NOT NULL COMMENT '食物名称',
  `amount` decimal(8,2) NOT NULL COMMENT '食物数量',
  `unit` varchar(20) NOT NULL COMMENT '计量单位',
  `calories` decimal(8,2) NOT NULL COMMENT '热量(千卡)',
  `protein` decimal(8,2) NOT NULL COMMENT '蛋白质(g)',
  `fat` decimal(8,2) NOT NULL COMMENT '脂肪(g)',
  `carbs` decimal(8,2) NOT NULL COMMENT '碳水化合物(g)',
  `grams` decimal(8,2) NOT NULL COMMENT '食物克数',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_record_id` (`diet_record_id`) COMMENT '记录ID索引',
  CONSTRAINT `diet_record_foods_ibfk_1` FOREIGN KEY (`diet_record_id`) REFERENCES `diet_records` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=352 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='饮食记录食物明细表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `diet_record_foods`
--

LOCK TABLES `diet_record_foods` WRITE;
/*!40000 ALTER TABLE `diet_record_foods` DISABLE KEYS */;
INSERT INTO `diet_record_foods` VALUES (1,1,1,'牛奶',2.00,'1夸脱',1320.00,64.00,80.00,96.00,1952.00,'2025-04-21 17:03:36'),(2,1,2,'脱脂牛奶',984.00,'克',360.00,36.00,0.00,52.00,984.00,'2025-04-21 17:03:36'),(3,2,43,'咸猪肉',1.00,'2盎司',470.00,3.00,55.00,0.00,60.00,'2025-04-21 17:09:20'),(4,2,248,'大米',208.00,'克',748.00,15.00,3.00,154.00,208.00,'2025-04-21 17:09:20'),(5,3,43,'咸猪肉',1.00,'2盎司',470.00,3.00,55.00,0.00,60.00,'2025-04-22 10:39:21'),(6,4,223,'黑麦面包',2.00,'1片',110.00,4.00,2.00,24.00,46.00,'2025-04-23 10:45:01'),(7,4,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-04-23 10:45:01'),(8,5,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-04-24 15:21:57'),(9,5,27,'生鸡蛋',1.00,'2个',150.00,12.00,12.00,0.00,100.00,'2025-04-24 15:21:57'),(10,6,45,'牛肉',1.00,'3盎司',245.00,23.00,16.00,0.00,85.00,'2025-04-24 15:22:51'),(11,6,102,'西兰花',1.00,'1杯',45.00,5.00,0.00,8.00,150.00,'2025-04-24 15:22:51'),(12,7,44,'培根',2.00,'2片',190.00,8.00,16.00,2.00,32.00,'2025-04-24 16:25:11'),(13,7,106,'胡萝卜',2.00,'1杯',90.00,2.00,0.00,20.00,300.00,'2025-04-24 16:25:11'),(14,7,33,'氢化食用油',2.00,'1/2杯',1330.00,0.00,200.00,0.00,200.00,'2025-04-24 16:25:11'),(15,8,187,'葡萄',1.00,'1杯',70.00,1.00,0.00,16.00,153.00,'2025-04-24 16:27:41'),(16,9,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-04-26 15:43:37'),(17,9,27,'生鸡蛋',2.00,'2个',300.00,24.00,24.00,0.00,200.00,'2025-04-26 15:43:37'),(18,10,48,'烤牛肉',1.00,'3盎司',390.00,16.00,36.00,0.00,85.00,'2025-04-27 19:40:47'),(19,10,100,'甜菜叶',1.00,'1杯',27.00,2.00,0.00,6.00,100.00,'2025-04-27 19:40:47'),(20,11,47,'瘦牛肉末',1.00,'3盎司',185.00,24.00,10.00,0.00,85.00,'2025-04-28 15:12:18'),(21,12,54,'肉馅饼',1.00,'1个',480.00,18.00,28.00,32.00,227.00,'2025-04-28 16:06:21'),(22,13,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-04-28 16:09:50'),(23,14,223,'黑麦面包',1.00,'1片',55.00,2.00,1.00,12.00,23.00,'2025-04-28 16:19:17'),(24,15,48,'烤牛肉',1.00,'3盎司',390.00,16.00,36.00,0.00,85.00,'2025-04-28 16:45:47'),(25,16,212,'葡萄干',1.00,'1/2杯',230.00,2.00,0.00,82.00,88.00,'2025-04-28 16:59:54'),(26,16,324,'啤酒',1.00,'2杯',228.00,0.00,0.00,8.00,480.00,'2025-04-28 16:59:54'),(27,17,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-04-29 17:53:39'),(28,18,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-04-30 10:50:28'),(29,19,43,'咸猪肉',1.00,'2盎司',470.00,3.00,55.00,0.00,60.00,'2025-04-30 11:20:01'),(30,20,45,'牛肉',1.00,'3盎司',245.00,23.00,16.00,0.00,85.00,'2025-04-30 16:10:18'),(31,21,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-04-30 19:57:18'),(32,22,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-04 14:58:24'),(33,23,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-04 14:58:24'),(34,24,43,'咸猪肉',1.00,'2盎司',470.00,3.00,55.00,0.00,60.00,'2025-05-04 15:54:22'),(35,25,1,'牛奶',2.00,'1夸脱',1320.00,64.00,80.00,96.00,1952.00,'2025-05-06 21:14:12'),(36,26,31,'黄油',1.00,'1/2杯',113.00,114.00,115.00,118.00,112.00,'2025-05-07 21:24:23'),(37,27,20,'无奶油干酪',1.00,'1杯',195.00,38.00,0.00,6.00,225.00,'2025-05-11 10:30:33'),(38,28,44,'培根',1.00,'2片',95.00,4.00,8.00,1.00,16.00,'2025-05-11 11:34:07'),(39,29,47,'瘦牛肉末',1.00,'3盎司',185.00,24.00,10.00,0.00,85.00,'2025-05-11 15:04:02'),(40,30,51,'咸牛肉',1.00,'3盎司',185.00,22.00,10.00,0.00,85.00,'2025-05-13 22:35:06'),(41,30,73,'蛤蜊',1.00,'3盎司',87.00,12.00,1.00,2.00,85.00,'2025-05-13 22:35:06'),(42,31,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-13 22:40:18'),(43,32,44,'培根',1.00,'2片',95.00,4.00,8.00,1.00,16.00,'2025-05-13 22:44:12'),(44,33,5,'强化牛奶',1.00,'6杯',0.00,89.00,42.00,119.00,0.00,'2025-05-14 09:47:50'),(45,34,5,'强化牛奶',1.00,'6杯',0.00,89.00,42.00,119.00,0.00,'2025-05-14 09:47:50'),(46,35,43,'咸猪肉',1.00,'2盎司',470.00,3.00,55.00,0.00,60.00,'2025-05-14 09:48:55'),(47,36,50,'牛排, 瘦肉, 如后腿肉',1.00,'3盎司',220.00,24.00,12.00,0.00,85.00,'2025-05-14 09:52:40'),(48,37,92,'洋蓟',1.00,'1个大',0.00,2.00,0.00,10.00,100.00,'2025-05-14 10:22:18'),(49,38,20,'无奶油干酪',1.00,'1杯',195.00,38.00,0.00,6.00,225.00,'2025-05-14 10:23:07'),(50,39,16,'冰牛奶 (Ice milk)',1.00,'1杯',275.00,9.00,10.00,32.00,190.00,'2025-05-14 10:25:34'),(51,40,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-14 10:39:06'),(52,41,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-14 11:06:36'),(53,42,220,'饼干',1.00,'1个',130.00,3.00,4.00,18.00,38.00,'2025-05-16 14:52:49'),(54,43,275,'苹果派(Apple Betty)',1.00,'1份',150.00,1.00,4.00,29.00,100.00,'2025-05-16 14:56:27'),(55,44,43,'咸猪肉',1.00,'2盎司',470.00,3.00,55.00,0.00,60.00,'2025-05-16 15:05:38'),(56,45,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-17 09:44:24'),(57,46,43,'咸猪肉',1.00,'2盎司',470.00,3.00,55.00,0.00,60.00,'2025-05-17 09:57:20'),(58,47,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-17 10:43:20'),(59,48,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-17 10:59:54'),(60,49,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-17 10:59:55'),(61,50,2,'脱脂牛奶',1.00,'1夸脱',360.00,36.00,0.00,52.00,984.00,'2025-05-17 11:14:08'),(62,51,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-17 14:59:16'),(63,52,2,'脱脂牛奶',1.00,'1夸脱',360.00,36.00,0.00,52.00,984.00,'2025-05-17 17:07:18'),(64,53,46,'汉堡肉饼',1.00,'3盎司',245.00,21.00,17.00,0.00,85.00,'2025-05-17 17:13:11'),(65,54,32,'黄油',1.00,'1/4磅',113.00,114.00,115.00,118.00,112.00,'2025-05-17 17:13:46'),(66,55,29,'蛋黄',1.00,'2个',120.00,6.00,10.00,0.00,34.00,'2025-05-17 17:14:12'),(67,56,73,'蛤蜊',1.00,'3盎司',87.00,12.00,1.00,2.00,85.00,'2025-05-17 19:24:02'),(68,57,29,'蛋黄',1.00,'2个',120.00,6.00,10.00,0.00,34.00,'2025-05-17 19:41:46'),(69,58,43,'咸猪肉',1.00,'2盎司',470.00,3.00,55.00,0.00,60.00,'2025-05-17 19:43:59'),(70,59,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-17 19:54:54'),(71,60,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-17 20:01:35'),(72,61,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-17 20:04:01'),(73,62,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-18 16:37:18'),(74,63,2,'脱脂牛奶',1.00,'1夸脱',360.00,36.00,0.00,52.00,984.00,'2025-05-18 16:38:06'),(75,64,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-18 16:38:19'),(76,65,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-18 16:39:57'),(77,66,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-18 16:40:31'),(78,67,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-18 16:42:27'),(79,68,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-18 16:54:29'),(80,69,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-18 17:08:01'),(81,70,2,'脱脂牛奶',1.00,'1夸脱',360.00,36.00,0.00,52.00,984.00,'2025-05-18 17:08:10'),(82,71,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-18 17:08:32'),(83,72,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-18 17:08:42'),(84,73,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-18 17:09:59'),(85,74,29,'蛋黄',1.00,'2个',120.00,6.00,10.00,0.00,34.00,'2025-05-18 17:10:07'),(86,75,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-18 17:11:21'),(87,76,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-18 17:17:54'),(88,77,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-18 17:23:42'),(89,78,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-18 17:23:42'),(90,79,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-18 17:24:41'),(91,80,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-18 17:25:25'),(92,81,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-18 19:05:42'),(93,82,2,'脱脂牛奶',1.00,'1夸脱',360.00,36.00,0.00,52.00,984.00,'2025-05-18 19:06:08'),(94,83,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-18 19:21:48'),(95,84,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-24 14:55:42'),(96,85,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-24 21:07:37'),(97,86,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-24 21:16:29'),(98,87,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-24 21:20:39'),(99,88,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-24 21:50:38'),(100,89,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:32:48'),(101,90,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:32:48'),(102,91,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:29'),(103,92,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:29'),(104,93,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:30'),(105,94,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:30'),(106,95,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:30'),(107,96,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:30'),(108,97,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:30'),(109,98,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:30'),(110,99,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:30'),(111,100,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:30'),(112,101,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:31'),(113,102,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:31'),(114,103,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:31'),(115,104,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:31'),(116,105,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:31'),(117,106,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:31'),(118,107,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:31'),(119,108,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:31'),(120,109,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:31'),(121,110,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:31'),(122,111,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:31'),(123,112,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:31'),(124,113,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:31'),(125,114,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:31'),(126,115,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:31'),(127,116,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:32'),(128,117,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:32'),(129,118,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:32'),(130,119,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:32'),(131,120,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:32'),(132,121,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:32'),(133,122,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:32'),(134,123,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:32'),(135,124,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:32'),(136,125,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:32'),(137,126,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:32'),(138,128,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:32'),(139,127,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:32'),(140,129,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:32'),(141,130,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:32'),(142,131,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:32'),(143,132,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:32'),(144,133,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:32'),(145,134,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:33'),(146,135,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:33'),(147,136,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:33'),(148,137,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:33'),(149,138,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:33'),(150,139,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:33'),(151,140,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:33'),(152,141,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:33'),(153,142,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:33'),(154,143,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:33'),(155,144,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:33'),(156,148,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:33'),(157,147,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:33'),(158,145,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:33'),(159,149,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:33'),(160,146,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:33'),(161,150,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:33'),(162,151,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:33'),(163,152,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:33'),(164,153,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:33'),(165,154,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:33'),(166,155,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:33'),(167,158,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:34'),(168,160,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:34'),(169,159,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:34'),(170,156,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:34'),(171,157,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:34'),(172,161,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:34'),(173,162,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:34'),(174,165,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:34'),(175,163,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:34'),(176,164,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:34'),(177,166,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:34'),(178,167,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:34'),(179,168,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:34'),(180,169,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:34'),(181,170,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:34'),(182,171,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:34'),(183,172,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:34'),(184,173,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:34'),(185,174,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:34'),(186,175,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:34'),(187,176,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:34'),(188,177,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:34'),(189,178,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:34'),(190,179,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:34'),(191,180,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:35'),(192,181,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:35'),(193,182,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:35'),(194,183,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:35'),(195,184,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:35'),(196,185,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:35'),(197,186,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:35'),(198,188,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:35'),(199,187,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:35'),(200,189,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:35'),(201,190,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:35'),(202,191,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:35'),(203,192,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:35'),(204,193,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:35'),(205,194,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:35'),(206,195,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:35'),(207,196,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:35'),(208,197,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:35'),(209,198,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:35'),(210,199,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:35'),(211,200,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:35'),(212,201,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:35'),(213,202,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:35'),(214,203,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:36'),(215,204,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:36'),(216,207,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:36'),(217,205,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:36'),(218,206,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:36'),(219,208,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:36'),(220,209,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:36'),(221,210,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:36'),(222,211,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:36'),(223,212,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:36'),(224,213,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:36'),(225,215,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:36'),(226,214,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:36'),(227,216,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:36'),(228,217,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:36'),(229,218,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:36'),(230,219,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:36'),(231,220,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:36'),(232,221,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:36'),(233,222,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:36'),(234,224,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:36'),(235,223,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:36'),(236,225,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:36'),(237,226,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:36'),(238,227,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:36'),(239,228,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:36'),(240,229,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:36'),(241,230,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:36'),(242,231,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:36'),(243,232,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:36'),(244,233,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:37'),(245,234,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:37'),(246,235,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:37'),(247,236,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:37'),(248,237,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:37'),(249,238,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:37'),(250,239,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:37'),(251,241,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:37'),(252,240,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:37'),(253,242,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:37'),(254,243,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:37'),(255,244,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:37'),(256,245,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:37'),(257,246,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:37'),(258,247,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:37'),(259,248,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:37'),(260,249,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:37'),(261,250,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:37'),(262,252,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:37'),(263,251,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:37'),(264,253,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:37'),(265,255,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:37'),(266,254,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:37'),(267,256,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:37'),(268,257,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:38'),(269,258,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:38'),(270,260,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:38'),(271,259,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:38'),(272,261,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:38'),(273,262,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:38'),(274,264,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:38'),(275,263,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:38'),(276,265,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:38'),(277,266,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:38'),(278,268,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:38'),(279,269,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:38'),(280,267,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:38'),(281,270,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:38'),(282,271,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:38'),(283,272,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:38'),(284,273,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:38'),(285,274,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:38'),(286,275,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:38'),(287,276,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:38'),(288,277,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:38'),(289,278,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:38'),(290,279,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:38'),(291,281,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:38'),(292,282,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:38'),(293,280,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:38'),(294,283,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:39'),(295,284,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:39'),(296,285,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:39'),(297,286,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:39'),(298,288,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:39'),(299,287,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:39'),(300,290,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:39'),(301,289,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:39'),(302,292,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:39'),(303,291,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:39'),(304,293,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:39'),(305,294,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:39'),(306,295,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:39'),(307,296,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:39'),(308,297,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:39'),(309,298,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:39'),(310,300,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:39'),(311,299,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:39'),(312,301,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:39'),(313,302,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:39'),(314,304,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:39'),(315,305,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:39'),(316,303,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:39'),(317,306,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:39'),(318,307,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:40'),(319,309,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:40'),(320,308,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:40'),(321,310,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:40'),(322,311,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:40'),(323,312,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:40'),(324,313,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:40'),(325,316,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:40'),(326,315,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:40'),(327,314,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:40'),(328,317,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:40'),(329,318,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:40'),(330,319,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:40'),(331,320,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:40'),(332,321,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:40'),(333,323,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:41'),(334,324,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:41'),(335,322,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:41'),(336,325,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:41'),(337,326,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:41'),(338,327,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:41'),(339,328,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:41'),(340,329,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:41'),(341,330,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:41'),(342,331,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:41'),(343,332,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:41'),(344,334,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:41'),(345,333,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:41'),(346,335,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:41'),(347,337,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:42'),(348,336,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:42'),(349,338,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:42'),(350,339,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:42'),(351,340,1,'牛奶',1.00,'1夸脱',660.00,32.00,40.00,48.00,976.00,'2025-05-25 09:49:42');
/*!40000 ALTER TABLE `diet_record_foods` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `diet_records`
--

DROP TABLE IF EXISTS `diet_records`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `diet_records` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `date` date NOT NULL COMMENT '记录日期',
  `time` time NOT NULL COMMENT '记录时间',
  `meal_type` varchar(20) NOT NULL COMMENT '餐次类型: breakfast/lunch/dinner/snacks',
  `remark` varchar(200) DEFAULT NULL COMMENT '备注信息',
  `total_calorie` decimal(8,2) NOT NULL COMMENT '总热量(千卡)',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_date` (`user_id`,`date`) COMMENT '用户日期索引'
) ENGINE=InnoDB AUTO_INCREMENT=341 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='饮食记录主表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `diet_records`
--

LOCK TABLES `diet_records` WRITE;
/*!40000 ALTER TABLE `diet_records` DISABLE KEYS */;
INSERT INTO `diet_records` VALUES (1,23,'2025-04-21','07:03:00','breakfast','',1680.00,'2025-04-21 17:03:36','2025-04-21 17:03:36'),(2,23,'2025-04-21','12:00:00','lunch','好吃，爱吃',1218.00,'2025-04-21 17:09:20','2025-04-21 17:09:20'),(3,23,'2025-04-22','11:38:00','lunch','',470.00,'2025-04-22 10:39:21','2025-04-22 10:39:21'),(4,23,'2025-04-23','08:44:00','breakfast','睡醒喝杯牛奶',770.00,'2025-04-23 10:45:00','2025-04-23 10:45:00'),(5,23,'2025-04-24','07:21:00','breakfast','',810.00,'2025-04-24 15:21:57','2025-04-24 15:21:57'),(6,23,'2025-04-24','12:22:00','lunch','',290.00,'2025-04-24 15:22:51','2025-04-24 15:22:51'),(7,23,'2025-04-24','17:24:00','dinner','',1610.00,'2025-04-24 16:25:11','2025-04-24 16:25:11'),(8,23,'2025-04-24','20:27:00','snacks','',70.00,'2025-04-24 16:27:41','2025-04-24 16:27:41'),(9,23,'2025-04-26','08:42:00','breakfast','',960.00,'2025-04-26 15:43:37','2025-04-26 15:43:37'),(10,23,'2025-04-27','18:40:00','dinner','',417.00,'2025-04-27 19:40:47','2025-04-27 19:40:47'),(11,23,'2025-04-28','12:12:00','lunch','',185.00,'2025-04-28 15:12:18','2025-04-28 15:12:18'),(12,23,'2025-04-28','19:05:00','dinner','',480.00,'2025-04-28 16:06:20','2025-04-28 16:06:20'),(13,23,'2025-04-28','06:09:00','breakfast','',660.00,'2025-04-28 16:09:50','2025-04-28 16:09:50'),(14,23,'2025-04-28','06:00:00','breakfast','',55.00,'2025-04-28 16:19:17','2025-04-28 16:19:17'),(15,23,'2025-04-28','18:45:00','dinner','',390.00,'2025-04-28 16:45:47','2025-04-28 16:45:47'),(16,23,'2025-04-28','21:00:00','snacks','宵夜',458.00,'2025-04-28 16:59:54','2025-04-28 16:59:54'),(17,23,'2025-04-29','08:53:00','breakfast','',660.00,'2025-04-29 17:53:39','2025-04-29 17:53:39'),(18,23,'2025-04-30','06:50:00','breakfast','',660.00,'2025-04-30 10:50:28','2025-04-30 10:50:28'),(19,23,'2025-04-30','11:19:00','lunch','',470.00,'2025-04-30 11:20:01','2025-04-30 11:20:01'),(20,23,'2025-04-30','19:10:00','dinner','',245.00,'2025-04-30 16:10:18','2025-04-30 16:10:18'),(21,23,'2025-04-30','20:57:00','snacks','',660.00,'2025-04-30 19:57:18','2025-04-30 19:57:18'),(22,23,'2025-05-04','07:58:00','breakfast','',660.00,'2025-05-04 14:58:23','2025-05-04 14:58:23'),(23,23,'2025-05-04','07:58:00','breakfast','',660.00,'2025-05-04 14:58:24','2025-05-04 14:58:24'),(24,23,'2025-05-04','12:54:00','lunch','',470.00,'2025-05-04 15:54:22','2025-05-04 15:54:22'),(25,23,'2025-05-06','07:11:00','breakfast','',1320.00,'2025-05-06 21:14:12','2025-05-06 21:14:12'),(26,23,'2025-05-07','11:23:00','lunch','',113.00,'2025-05-07 21:24:22','2025-05-07 21:24:22'),(27,23,'2025-05-11','09:30:00','breakfast','',195.00,'2025-05-11 10:30:33','2025-05-11 10:30:33'),(28,23,'2025-05-11','12:32:00','breakfast','',95.00,'2025-05-11 11:34:07','2025-05-11 11:34:07'),(29,23,'2025-05-11','19:03:00','dinner','',185.00,'2025-05-11 15:04:02','2025-05-11 15:04:02'),(30,23,'2025-05-13','22:34:00','dinner','',272.00,'2025-05-13 22:35:06','2025-05-13 22:35:06'),(31,23,'2025-05-13','07:40:00','breakfast','',660.00,'2025-05-13 22:40:18','2025-05-13 22:40:18'),(32,23,'2025-05-13','11:43:00','lunch','',95.00,'2025-05-13 22:44:12','2025-05-13 22:44:12'),(33,23,'2025-05-14','06:00:00','breakfast','',0.00,'2025-05-14 09:47:50','2025-05-14 09:47:50'),(34,23,'2025-05-14','06:00:00','breakfast','',0.00,'2025-05-14 09:47:50','2025-05-14 09:47:50'),(35,23,'2025-05-14','12:00:00','lunch','',470.00,'2025-05-14 09:48:55','2025-05-14 09:48:55'),(36,23,'2025-05-14','18:00:00','dinner','',220.00,'2025-05-14 09:52:40','2025-05-14 09:52:40'),(37,23,'2025-05-14','10:21:00','snacks','',0.00,'2025-05-14 10:22:18','2025-05-14 10:22:18'),(38,23,'2025-05-14','11:22:00','snacks','',195.00,'2025-05-14 10:23:07','2025-05-14 10:23:07'),(39,23,'2025-05-14','10:25:00','snacks','',275.00,'2025-05-14 10:25:34','2025-05-14 10:25:34'),(40,23,'2025-05-14','09:38:00','snacks','',660.00,'2025-05-14 10:39:06','2025-05-14 10:39:06'),(41,23,'2025-05-14','08:06:00','snacks','',660.00,'2025-05-14 11:06:36','2025-05-14 11:06:36'),(42,23,'2025-05-16','14:52:00','snacks','',130.00,'2025-05-16 14:52:49','2025-05-16 14:52:49'),(43,23,'2025-05-16','14:56:00','snacks','',150.00,'2025-05-16 14:56:27','2025-05-16 14:56:27'),(44,23,'2025-05-16','15:05:00','snacks','',470.00,'2025-05-16 15:05:38','2025-05-16 15:05:38'),(45,23,'2025-05-17','09:44:00','breakfast','',660.00,'2025-05-17 09:44:24','2025-05-17 09:44:24'),(46,23,'2025-05-17','11:57:00','lunch','',470.00,'2025-05-17 09:57:20','2025-05-17 09:57:20'),(47,23,'2025-05-17','10:43:00','snacks','',660.00,'2025-05-17 10:43:20','2025-05-17 10:43:20'),(48,23,'2025-05-17','10:59:00','snacks','',660.00,'2025-05-17 10:59:54','2025-05-17 10:59:54'),(49,23,'2025-05-17','10:59:00','snacks','',660.00,'2025-05-17 10:59:55','2025-05-17 10:59:55'),(50,23,'2025-05-17','11:13:00','snacks','',360.00,'2025-05-17 11:14:08','2025-05-17 11:14:08'),(51,23,'2025-05-17','14:58:00','breakfast','',660.00,'2025-05-17 14:59:16','2025-05-17 14:59:16'),(52,23,'2025-05-17','17:07:00','snacks','',360.00,'2025-05-17 17:07:18','2025-05-17 17:07:18'),(53,23,'2025-05-17','17:13:00','snacks','',245.00,'2025-05-17 17:13:11','2025-05-17 17:13:11'),(54,23,'2025-05-17','17:13:00','breakfast','',113.00,'2025-05-17 17:13:46','2025-05-17 17:13:46'),(55,23,'2025-05-17','17:14:00','breakfast','',120.00,'2025-05-17 17:14:12','2025-05-17 17:14:12'),(56,23,'2025-05-17','19:23:00','snacks','',87.00,'2025-05-17 19:24:02','2025-05-17 19:24:02'),(57,23,'2025-05-17','19:41:00','breakfast','',120.00,'2025-05-17 19:41:46','2025-05-17 19:41:46'),(58,23,'2025-05-17','19:43:00','snacks','',470.00,'2025-05-17 19:43:59','2025-05-17 19:43:59'),(59,23,'2025-05-17','19:54:00','snacks','',660.00,'2025-05-17 19:54:54','2025-05-17 19:54:54'),(60,23,'2025-05-17','20:01:00','snacks','',660.00,'2025-05-17 20:01:35','2025-05-17 20:01:35'),(61,23,'2025-05-17','20:02:00','snacks','',660.00,'2025-05-17 20:04:01','2025-05-17 20:04:01'),(62,23,'2025-05-18','16:37:00','breakfast','',660.00,'2025-05-18 16:37:18','2025-05-18 16:37:18'),(63,23,'2025-05-18','16:38:00','breakfast','',360.00,'2025-05-18 16:38:06','2025-05-18 16:38:06'),(64,23,'2025-05-18','16:38:00','breakfast','',660.00,'2025-05-18 16:38:19','2025-05-18 16:38:19'),(65,23,'2025-05-18','16:39:00','breakfast','',660.00,'2025-05-18 16:39:57','2025-05-18 16:39:57'),(66,23,'2025-05-18','16:40:00','lunch','',660.00,'2025-05-18 16:40:31','2025-05-18 16:40:31'),(67,23,'2025-05-18','16:42:00','lunch','',660.00,'2025-05-18 16:42:27','2025-05-18 16:42:27'),(68,23,'2025-05-18','16:54:00','snacks','',660.00,'2025-05-18 16:54:29','2025-05-18 16:54:29'),(69,23,'2025-05-18','17:07:00','breakfast','',660.00,'2025-05-18 17:08:01','2025-05-18 17:08:01'),(70,23,'2025-05-18','17:08:00','breakfast','',360.00,'2025-05-18 17:08:10','2025-05-18 17:08:10'),(71,23,'2025-05-18','17:08:00','breakfast','',660.00,'2025-05-18 17:08:32','2025-05-18 17:08:32'),(72,23,'2025-05-18','17:08:00','snacks','',660.00,'2025-05-18 17:08:42','2025-05-18 17:08:42'),(73,23,'2025-05-18','17:09:00','breakfast','',660.00,'2025-05-18 17:09:59','2025-05-18 17:09:59'),(74,23,'2025-05-18','17:10:00','breakfast','',120.00,'2025-05-18 17:10:07','2025-05-18 17:10:07'),(75,23,'2025-05-18','17:11:00','breakfast','',660.00,'2025-05-18 17:11:21','2025-05-18 17:11:21'),(76,23,'2025-05-18','17:17:00','snacks','',660.00,'2025-05-18 17:17:54','2025-05-18 17:17:54'),(77,23,'2025-05-18','17:23:00','breakfast','',660.00,'2025-05-18 17:23:42','2025-05-18 17:23:42'),(78,23,'2025-05-18','17:23:00','breakfast','',660.00,'2025-05-18 17:23:42','2025-05-18 17:23:42'),(79,23,'2025-05-18','17:24:00','breakfast','',660.00,'2025-05-18 17:24:41','2025-05-18 17:24:41'),(80,23,'2025-05-18','17:25:00','breakfast','',660.00,'2025-05-18 17:25:25','2025-05-18 17:25:25'),(81,23,'2025-05-18','19:05:00','snacks','',660.00,'2025-05-18 19:05:42','2025-05-18 19:05:42'),(82,23,'2025-05-18','19:05:00','snacks','',360.00,'2025-05-18 19:06:08','2025-05-18 19:06:08'),(83,23,'2025-05-18','19:21:00','breakfast','',660.00,'2025-05-18 19:21:48','2025-05-18 19:21:48'),(84,23,'2025-05-24','14:55:00','breakfast','',660.00,'2025-05-24 14:55:42','2025-05-24 14:55:42'),(85,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-24 21:07:37','2025-05-24 21:07:37'),(86,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-24 21:16:29','2025-05-24 21:16:29'),(87,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-24 21:20:39','2025-05-24 21:20:39'),(88,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-24 21:50:38','2025-05-24 21:50:38'),(89,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:32:48','2025-05-25 09:32:48'),(90,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:32:48','2025-05-25 09:32:48'),(91,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:29','2025-05-25 09:49:29'),(92,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:29','2025-05-25 09:49:29'),(93,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:30','2025-05-25 09:49:30'),(94,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:30','2025-05-25 09:49:30'),(95,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:30','2025-05-25 09:49:30'),(96,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:30','2025-05-25 09:49:30'),(97,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:30','2025-05-25 09:49:30'),(98,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:30','2025-05-25 09:49:30'),(99,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:30','2025-05-25 09:49:30'),(100,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:30','2025-05-25 09:49:30'),(101,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:31','2025-05-25 09:49:31'),(102,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:31','2025-05-25 09:49:31'),(103,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:31','2025-05-25 09:49:31'),(104,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:31','2025-05-25 09:49:31'),(105,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:31','2025-05-25 09:49:31'),(106,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:31','2025-05-25 09:49:31'),(107,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:31','2025-05-25 09:49:31'),(108,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:31','2025-05-25 09:49:31'),(109,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:31','2025-05-25 09:49:31'),(110,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:31','2025-05-25 09:49:31'),(111,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:31','2025-05-25 09:49:31'),(112,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:31','2025-05-25 09:49:31'),(113,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:31','2025-05-25 09:49:31'),(114,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:31','2025-05-25 09:49:31'),(115,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:31','2025-05-25 09:49:31'),(116,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:32','2025-05-25 09:49:32'),(117,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:32','2025-05-25 09:49:32'),(118,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:32','2025-05-25 09:49:32'),(119,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:32','2025-05-25 09:49:32'),(120,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:32','2025-05-25 09:49:32'),(121,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:32','2025-05-25 09:49:32'),(122,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:32','2025-05-25 09:49:32'),(123,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:32','2025-05-25 09:49:32'),(124,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:32','2025-05-25 09:49:32'),(125,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:32','2025-05-25 09:49:32'),(126,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:32','2025-05-25 09:49:32'),(127,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:32','2025-05-25 09:49:32'),(128,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:32','2025-05-25 09:49:32'),(129,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:32','2025-05-25 09:49:32'),(130,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:32','2025-05-25 09:49:32'),(131,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:32','2025-05-25 09:49:32'),(132,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:32','2025-05-25 09:49:32'),(133,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:32','2025-05-25 09:49:32'),(134,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:33','2025-05-25 09:49:33'),(135,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:33','2025-05-25 09:49:33'),(136,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:33','2025-05-25 09:49:33'),(137,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:33','2025-05-25 09:49:33'),(138,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:33','2025-05-25 09:49:33'),(139,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:33','2025-05-25 09:49:33'),(140,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:33','2025-05-25 09:49:33'),(141,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:33','2025-05-25 09:49:33'),(142,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:33','2025-05-25 09:49:33'),(143,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:33','2025-05-25 09:49:33'),(144,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:33','2025-05-25 09:49:33'),(145,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:33','2025-05-25 09:49:33'),(146,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:33','2025-05-25 09:49:33'),(147,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:33','2025-05-25 09:49:33'),(148,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:33','2025-05-25 09:49:33'),(149,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:33','2025-05-25 09:49:33'),(150,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:33','2025-05-25 09:49:33'),(151,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:33','2025-05-25 09:49:33'),(152,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:33','2025-05-25 09:49:33'),(153,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:33','2025-05-25 09:49:33'),(154,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:33','2025-05-25 09:49:33'),(155,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:33','2025-05-25 09:49:33'),(156,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:34','2025-05-25 09:49:34'),(157,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:34','2025-05-25 09:49:34'),(158,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:34','2025-05-25 09:49:34'),(159,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:34','2025-05-25 09:49:34'),(160,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:34','2025-05-25 09:49:34'),(161,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:34','2025-05-25 09:49:34'),(162,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:34','2025-05-25 09:49:34'),(163,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:34','2025-05-25 09:49:34'),(164,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:34','2025-05-25 09:49:34'),(165,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:34','2025-05-25 09:49:34'),(166,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:34','2025-05-25 09:49:34'),(167,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:34','2025-05-25 09:49:34'),(168,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:34','2025-05-25 09:49:34'),(169,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:34','2025-05-25 09:49:34'),(170,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:34','2025-05-25 09:49:34'),(171,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:34','2025-05-25 09:49:34'),(172,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:34','2025-05-25 09:49:34'),(173,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:34','2025-05-25 09:49:34'),(174,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:34','2025-05-25 09:49:34'),(175,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:34','2025-05-25 09:49:34'),(176,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:34','2025-05-25 09:49:34'),(177,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:34','2025-05-25 09:49:34'),(178,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:34','2025-05-25 09:49:34'),(179,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:34','2025-05-25 09:49:34'),(180,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:35','2025-05-25 09:49:35'),(181,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:35','2025-05-25 09:49:35'),(182,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:35','2025-05-25 09:49:35'),(183,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:35','2025-05-25 09:49:35'),(184,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:35','2025-05-25 09:49:35'),(185,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:35','2025-05-25 09:49:35'),(186,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:35','2025-05-25 09:49:35'),(187,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:35','2025-05-25 09:49:35'),(188,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:35','2025-05-25 09:49:35'),(189,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:35','2025-05-25 09:49:35'),(190,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:35','2025-05-25 09:49:35'),(191,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:35','2025-05-25 09:49:35'),(192,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:35','2025-05-25 09:49:35'),(193,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:35','2025-05-25 09:49:35'),(194,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:35','2025-05-25 09:49:35'),(195,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:35','2025-05-25 09:49:35'),(196,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:35','2025-05-25 09:49:35'),(197,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:35','2025-05-25 09:49:35'),(198,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:35','2025-05-25 09:49:35'),(199,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:35','2025-05-25 09:49:35'),(200,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:35','2025-05-25 09:49:35'),(201,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:35','2025-05-25 09:49:35'),(202,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:35','2025-05-25 09:49:35'),(203,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:35','2025-05-25 09:49:35'),(204,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:36','2025-05-25 09:49:36'),(205,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:36','2025-05-25 09:49:36'),(206,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:36','2025-05-25 09:49:36'),(207,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:36','2025-05-25 09:49:36'),(208,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:36','2025-05-25 09:49:36'),(209,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:36','2025-05-25 09:49:36'),(210,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:36','2025-05-25 09:49:36'),(211,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:36','2025-05-25 09:49:36'),(212,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:36','2025-05-25 09:49:36'),(213,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:36','2025-05-25 09:49:36'),(214,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:36','2025-05-25 09:49:36'),(215,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:36','2025-05-25 09:49:36'),(216,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:36','2025-05-25 09:49:36'),(217,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:36','2025-05-25 09:49:36'),(218,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:36','2025-05-25 09:49:36'),(219,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:36','2025-05-25 09:49:36'),(220,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:36','2025-05-25 09:49:36'),(221,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:36','2025-05-25 09:49:36'),(222,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:36','2025-05-25 09:49:36'),(223,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:36','2025-05-25 09:49:36'),(224,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:36','2025-05-25 09:49:36'),(225,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:36','2025-05-25 09:49:36'),(226,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:36','2025-05-25 09:49:36'),(227,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:36','2025-05-25 09:49:36'),(228,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:36','2025-05-25 09:49:36'),(229,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:36','2025-05-25 09:49:36'),(230,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:36','2025-05-25 09:49:36'),(231,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:36','2025-05-25 09:49:36'),(232,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:36','2025-05-25 09:49:36'),(233,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:37','2025-05-25 09:49:37'),(234,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:37','2025-05-25 09:49:37'),(235,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:37','2025-05-25 09:49:37'),(236,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:37','2025-05-25 09:49:37'),(237,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:37','2025-05-25 09:49:37'),(238,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:37','2025-05-25 09:49:37'),(239,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:37','2025-05-25 09:49:37'),(240,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:37','2025-05-25 09:49:37'),(241,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:37','2025-05-25 09:49:37'),(242,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:37','2025-05-25 09:49:37'),(243,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:37','2025-05-25 09:49:37'),(244,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:37','2025-05-25 09:49:37'),(245,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:37','2025-05-25 09:49:37'),(246,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:37','2025-05-25 09:49:37'),(247,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:37','2025-05-25 09:49:37'),(248,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:37','2025-05-25 09:49:37'),(249,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:37','2025-05-25 09:49:37'),(250,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:37','2025-05-25 09:49:37'),(251,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:37','2025-05-25 09:49:37'),(252,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:37','2025-05-25 09:49:37'),(253,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:37','2025-05-25 09:49:37'),(254,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:37','2025-05-25 09:49:37'),(255,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:37','2025-05-25 09:49:37'),(256,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:37','2025-05-25 09:49:37'),(257,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:38','2025-05-25 09:49:38'),(258,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:38','2025-05-25 09:49:38'),(259,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:38','2025-05-25 09:49:38'),(260,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:38','2025-05-25 09:49:38'),(261,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:38','2025-05-25 09:49:38'),(262,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:38','2025-05-25 09:49:38'),(263,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:38','2025-05-25 09:49:38'),(264,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:38','2025-05-25 09:49:38'),(265,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:38','2025-05-25 09:49:38'),(266,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:38','2025-05-25 09:49:38'),(267,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:38','2025-05-25 09:49:38'),(268,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:38','2025-05-25 09:49:38'),(269,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:38','2025-05-25 09:49:38'),(270,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:38','2025-05-25 09:49:38'),(271,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:38','2025-05-25 09:49:38'),(272,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:38','2025-05-25 09:49:38'),(273,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:38','2025-05-25 09:49:38'),(274,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:38','2025-05-25 09:49:38'),(275,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:38','2025-05-25 09:49:38'),(276,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:38','2025-05-25 09:49:38'),(277,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:38','2025-05-25 09:49:38'),(278,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:38','2025-05-25 09:49:38'),(279,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:38','2025-05-25 09:49:38'),(280,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:38','2025-05-25 09:49:38'),(281,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:38','2025-05-25 09:49:38'),(282,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:38','2025-05-25 09:49:38'),(283,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:39','2025-05-25 09:49:39'),(284,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:39','2025-05-25 09:49:39'),(285,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:39','2025-05-25 09:49:39'),(286,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:39','2025-05-25 09:49:39'),(287,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:39','2025-05-25 09:49:39'),(288,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:39','2025-05-25 09:49:39'),(289,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:39','2025-05-25 09:49:39'),(290,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:39','2025-05-25 09:49:39'),(291,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:39','2025-05-25 09:49:39'),(292,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:39','2025-05-25 09:49:39'),(293,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:39','2025-05-25 09:49:39'),(294,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:39','2025-05-25 09:49:39'),(295,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:39','2025-05-25 09:49:39'),(296,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:39','2025-05-25 09:49:39'),(297,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:39','2025-05-25 09:49:39'),(298,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:39','2025-05-25 09:49:39'),(299,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:39','2025-05-25 09:49:39'),(300,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:39','2025-05-25 09:49:39'),(301,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:39','2025-05-25 09:49:39'),(302,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:39','2025-05-25 09:49:39'),(303,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:39','2025-05-25 09:49:39'),(304,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:39','2025-05-25 09:49:39'),(305,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:39','2025-05-25 09:49:39'),(306,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:39','2025-05-25 09:49:39'),(307,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:40','2025-05-25 09:49:40'),(308,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:40','2025-05-25 09:49:40'),(309,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:40','2025-05-25 09:49:40'),(310,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:40','2025-05-25 09:49:40'),(311,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:40','2025-05-25 09:49:40'),(312,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:40','2025-05-25 09:49:40'),(313,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:40','2025-05-25 09:49:40'),(314,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:40','2025-05-25 09:49:40'),(315,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:40','2025-05-25 09:49:40'),(316,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:40','2025-05-25 09:49:40'),(317,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:40','2025-05-25 09:49:40'),(318,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:40','2025-05-25 09:49:40'),(319,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:40','2025-05-25 09:49:40'),(320,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:40','2025-05-25 09:49:40'),(321,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:40','2025-05-25 09:49:40'),(322,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:41','2025-05-25 09:49:41'),(323,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:41','2025-05-25 09:49:41'),(324,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:41','2025-05-25 09:49:41'),(325,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:41','2025-05-25 09:49:41'),(326,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:41','2025-05-25 09:49:41'),(327,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:41','2025-05-25 09:49:41'),(328,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:41','2025-05-25 09:49:41'),(329,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:41','2025-05-25 09:49:41'),(330,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:41','2025-05-25 09:49:41'),(331,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:41','2025-05-25 09:49:41'),(332,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:41','2025-05-25 09:49:41'),(333,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:41','2025-05-25 09:49:41'),(334,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:41','2025-05-25 09:49:41'),(335,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:41','2025-05-25 09:49:41'),(336,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:42','2025-05-25 09:49:42'),(337,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:42','2025-05-25 09:49:42'),(338,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:42','2025-05-25 09:49:42'),(339,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:42','2025-05-25 09:49:42'),(340,3,'2025-05-24','21:07:00','breakfast','',660.00,'2025-05-25 09:49:42','2025-05-25 09:49:42');
/*!40000 ALTER TABLE `diet_records` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `food`
--

DROP TABLE IF EXISTS `food`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `food` (
  `id` int NOT NULL AUTO_INCREMENT,
  `food_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `measure` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `grams` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `calories` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `protein` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `fat` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `sat_fat` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `fiber` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `carbs` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `image_url` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '食物图片URL',
  `category_id` int DEFAULT NULL COMMENT '分类ID',
  PRIMARY KEY (`id`),
  KEY `idx_category_id` (`category_id`),
  CONSTRAINT `fk_food_category` FOREIGN KEY (`category_id`) REFERENCES `food_category` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=364 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `food`
--

LOCK TABLES `food` WRITE;
/*!40000 ALTER TABLE `food` DISABLE KEYS */;
INSERT INTO `food` VALUES (1,'牛奶','1夸脱','976.0','660.0','32.0','40.0','36.0','0','48.0','foodimage/1/88968db42bef4ade9294a81ff3549b27.jpg',1),(2,'脱脂牛奶','1夸脱','984.0','360.0','36.0','0.0','0.0','0','52.0','foodimage/2/d0b2bd90094640f49653340011af244c.jpg',1),(3,'酪乳','1杯','246','127','9','5','4','0','13','foodimage/3/060923093e254bcb979f750979a2cdb6.jpg',1),(4,'炼乳, 未稀释','1杯','252','345','16','20','18','0','24','foodimage/4/8d7288c1c591444c849244399b3f43a4.jpg',1),(5,'强化牛奶','6杯','1,419','1,373','89','42','23','1.4','119','foodimage/5/734bf47e92044cd183bc69ec5409adfd.jpg',1),(6,'奶粉','1杯','103','515','27','28','24','0','39','foodimage/6/eaaa978890c240719100198cbc7e9a55.jpg',1),(7,'脱脂速溶奶粉','1 1/3杯','85.0','290.0','30.0','0.0','0.0','0','42.0','foodimage/7/1413494a1e824a4c9cf6a3867a05ddbb.jpg',1),(8,'脱脂非速溶奶粉','2/3杯','85','290','30','微量','微量','1','42',NULL,1),(9,'羊奶','1杯','244','165','8','10','8','0','11',NULL,1),(10,'冰淇淋 (1/2杯量)','2杯','540','690','24','24','22','0','70',NULL,1),(11,'可可饮料','1杯','252','235','8','11','10','0','26',NULL,1),(12,'脱脂牛奶饮料','1杯','250','128','18','4','3','1','13',NULL,1),(13,'(玉米淀粉饮料)','1杯','248','275','9','10','9','0','40',NULL,1),(14,'蛋奶沙司','1杯','248','285','13','14','11','0','28',NULL,1),(15,'冰淇淋','1杯','188','300','6','18','16','0','29',NULL,1),(16,'冰牛奶 (Ice milk)','1杯','190','275','9','10','9','0','32',NULL,1),(17,'淡奶油或半对半奶油','1/2杯','120','170','4','15','13','0','5',NULL,1),(18,'或 搅打奶油','1/2杯','119','430','2','44','27','1','3',NULL,1),(19,'奶酪 (cottage cheese)','1杯','225','240','30','11','10','0','6',NULL,1),(20,'无奶油干酪','1杯','225','195','38','微量','微量','0','6',NULL,1),(21,'切达奶酪','1英寸立方块','17','70','4','6','5','0','微量',NULL,1),(22,'切达奶酪丝, 杯','1/2杯','56','226','14','19','17','0','1',NULL,1),(23,'奶油奶酪','1盎司','28','105','2','11','10','0','1',NULL,1),(24,'加工奶酪','1盎司','28','105','7','9','8','0','微量',NULL,1),(25,'罗克福干酪类','1盎司','28','105','6','9','8','0','微量',NULL,1),(26,'瑞士奶酪','1盎司','28','105','7','8','7','0','微量',NULL,1),(27,'生鸡蛋','2个','100','150','12','12','10','0','微量',NULL,1),(28,'炒鸡蛋或煎鸡蛋','2个','128','220','13','16','14','0','1',NULL,1),(29,'蛋黄','2个','34','120','6','10','8','0','微量',NULL,2),(30,'黄油','1汤匙','14','100','微量','11','10','0','微量',NULL,2),(31,'黄油','1/2杯','112','113','114','115','116','117','118',NULL,2),(32,'黄油','1/4磅','112','113','114','115','116','117','118',NULL,2),(33,'氢化食用油','1/2杯','100','665','0','100','88','0','0',NULL,2),(34,'猪油','1/2杯','110','992','0','110','92','0','0',NULL,2),(35,'人造黄油','1/2杯','112','806','微量','91','76','0','微量',NULL,2),(36,'人造黄油, 2 小块 或','1汤匙','14','100','微量','11','9','0','微量',NULL,2),(37,'蛋黄酱','1汤匙','15','110','微量','12','5','0','微量',NULL,2),(38,'玉米油','1汤匙','14','125','0','14','5','0','0',NULL,2),(39,'橄榄油','1汤匙','14','125','0','14','3','0','0',NULL,2),(40,'红花籽油','1汤匙','14','125','0','14','3','0','0',NULL,2),(41,'法式沙拉酱','1汤匙','15','60','微量','6','2','0','2',NULL,2),(42,'千岛酱','1汤匙','15','75','微量','8','3','0','1',NULL,2),(43,'咸猪肉','2盎司','60','470','3','55',NULL,'0','0',NULL,3),(44,'培根','2片','16','95','4','8','7','0','1',NULL,3),(45,'牛肉','3盎司','85','245','23','16','15','0','0',NULL,3),(46,'汉堡肉饼','3盎司','85','245','21','17','15','0','0',NULL,3),(47,'瘦牛肉末','3盎司','85','185','24','10','9','0','0',NULL,3),(48,'烤牛肉','3盎司','85','390','16','36','35','0','0',NULL,3),(49,'牛排','3盎司','85','330','20','27','25','0','0',NULL,3),(50,'牛排, 瘦肉, 如后腿肉','3盎司','85','220','24','12','11','0','0',NULL,3),(51,'咸牛肉','3盎司','85','185','22','10','9','0','0',NULL,3),(52,'罐装咸牛肉哈希','3盎司','85','120','12','8','7','微量','6',NULL,3),(53,'干咸牛肉哈希','2盎司','56','115','19','4','4','0','0',NULL,3),(54,'肉馅饼','1个','227','480','18','28','25','微量','32',NULL,3),(55,'咸牛肉哈希炖菜','1杯','235','185','15','10','9','微量','15',NULL,3),(56,'鸡肉','3盎司','85','185','23','9','7','0','0',NULL,3),(57,'炸鸡, 鸡胸或鸡腿','3盎司','85','245','25','15','11','0','0',NULL,3),(58,'烤鸡','3.5盎司','100','290','25','20','16','0','0',NULL,3),(59,'炸鸡肝','3个中等大小','100','140','22','14','12','0','2.30',NULL,3),(60,'家鸭','3.5盎司','100','370','16','28','0','0','0',NULL,3),(61,'羊排, 烤','4盎司','115','480','24','35','33','0','0',NULL,3),(62,'烤羊腿','3盎司','86','314','20','14','14','0','0',NULL,3),(63,'炖羊肩肉','3盎司','85','285','18','23','21','0','0',NULL,3),(64,'猪排, 1英寸厚','3.5盎司','100','260','16','21','18','0','0',NULL,3),(65,'火腿 (锅煎)','3盎司','85','290','16','22','19','0','0',NULL,3),(66,'火腿, 如','2盎司','57','170','13','13','11','0','0',NULL,3),(67,'火腿, 罐装, 调味','2盎司','57','165','8','14','12','0','1',NULL,3),(68,'烤猪肉','3盎司','85','310','21','24','21','0','0',NULL,3),(69,'猪肉香肠','3.5盎司','100','475','18','44','40','0','0',NULL,3),(70,'火鸡肉','3.5盎司','100','265','27','15','0','0','0',NULL,3),(71,'小牛肉','3盎司','85','185','23','9','8','0','0',NULL,3),(72,'烤小牛肉','3盎司','85','305','13','14','13','0','0',NULL,3),(73,'蛤蜊','3盎司','85','87','12','1','0','0','2',NULL,4),(74,'鳕鱼','3.5盎司','100','170','28','5','0','0','0',NULL,4),(75,'蟹肉','3盎司','85','90','14','2','0','0','1',NULL,4),(76,'炸鱼条','5条','112','200','19','10','5','0','8',NULL,4),(77,'比目鱼','3.5盎司','100','200','30','8','0','0','0',NULL,4),(78,'黑线鳕','3盎司','85','135','16','5','4','0','6',NULL,4),(79,'大比目鱼','3.5盎司','100','182','26','8','0','0','0',NULL,4),(80,'鲱鱼','1条小','100','211','22','13','0','0','0',NULL,4),(81,'龙虾','平均大小','100','92','18','1','0','0','微量',NULL,4),(82,'鲭鱼','3盎司','85','155','18','9','0','微量','0',NULL,4),(83,'牡蛎','6-8个中等','230','231','232','233','234','235','236',NULL,4),(84,'牡蛎汤','1杯','85','125','19','6','1','0','0',NULL,4),(85,'三文鱼','3盎司','85','120','17','5','1','0','0',NULL,4),(86,'沙丁鱼','3盎司','85','180','22','9','4','0','0',NULL,4),(87,'扇贝','3.5盎司','100','104','18','8','0','0','10',NULL,4),(88,'西鲱','3盎司','85','170','20','10','0','0','0',NULL,4),(89,'虾','3盎司','85','110','23','1','0','0','0',NULL,4),(90,'剑鱼','1块','100','180','27','6','0','0','0',NULL,4),(91,'金枪鱼','3盎司','85','170','25','7','3','0','0',NULL,4),(92,'洋蓟','1个大','100','8-44','2','微量','微量','2','10',NULL,5),(93,'芦笋','6根','96','18','1','微量','微量','0.5','3',NULL,5),(94,'豆角','1杯','125','25','1','微量','微量','0.8','6',NULL,5),(95,'利马豆','1杯','160','140','8','微量','微量','3.0','24',NULL,5),(96,'利马豆, 干, 煮熟','1杯','192','260','16','微量','微量','2','48',NULL,5),(97,'海军豆, 加猪肉烤','3/4杯','200','250','11','6','6','2','37',NULL,5),(98,'红腰豆','1杯','260','230','15','1','0','2.5','42',NULL,5),(99,'豆芽','1杯','50','17','1','微量','0','0.3','3',NULL,5),(100,'甜菜叶','1杯','100','27','2','微量','0','1.4','6',NULL,5),(101,'甜菜根','1杯','165','1','12','0',NULL,'微量','0.80',NULL,5),(102,'西兰花','1杯','150','45','5','微量','0','1.9','8',NULL,5),(103,'抱子甘蓝','1杯','130','60','6','微量','0','1.7','12',NULL,5),(104,'德国酸菜','1杯','150','32','1','微量','0','1.2','7',NULL,5),(105,'蒸卷心菜','1杯','170','40','2','微量','0','1.3','9',NULL,5),(106,'胡萝卜','1杯','150','45','1','微量','0','0.9','10',NULL,5),(107,'生胡萝卜丝','1杯','110','45','1','微量','0','1.2','10',NULL,5),(108,'生胡萝卜条','1根中等','50','20','微量','微量','0','0.5','5',NULL,5),(109,'花椰菜','1杯','120','30','3','微量','0','1','6',NULL,5),(110,'芹菜','1杯','100','20','1','微量','0','1','4',NULL,5),(111,'生芹菜茎','1大根','40','5','1','微量','0','0.3','1',NULL,5),(112,'蒸莙荙菜','1杯','150','30','2','微量','0','1.4','7',NULL,5),(113,'羽衣甘蓝(Collards)','1杯','150','51','5','微量','0','2','8',NULL,5),(114,'玉米','1穗','100','92','3','1','微量','0.8','21',NULL,5),(115,'煮熟或罐装玉米','1杯','200','170','5','微量','0','1.6','41',NULL,5),(116,'黄瓜','8片','50','6','微量','0','0','0.2','1',NULL,5),(117,'蒲公英叶','1杯','180','80','5','1','0','3.2','16',NULL,5),(118,'茄子','1杯','180','30','2','微量','0','1.0','9',NULL,5),(119,'苦苣','2盎司','57','10','1','微量','0','0.6','2',NULL,5),(120,'羽衣甘蓝(Kale)','1杯','110','45','4','1','0','0.9','8',NULL,6),(121,'苤蓝','1杯','140','40','2','微量','0','1.5','9',NULL,6),(122,'蒸藜菜','1杯','150','48','5','微量','0','3.2','7',NULL,6),(123,'小扁豆','1杯','200','212','15','微量','0','2.4','38',NULL,6),(124,'生菜','1/4头','100','14','1','微量','0','0.5','2',NULL,6),(125,'卷心生菜','1/4头','100','13','微量','微量','0','0.5','3',NULL,6),(126,'罐装蘑菇','4个','120','12','2','微量','0','微量','4',NULL,6),(127,'芥菜叶','1棵','140','30','3','微量','0','1.2','6',NULL,6),(128,'秋葵','1 1/3杯','100','32','1','微量','0','1','7',NULL,6),(129,'洋葱','1个','210','80','2','微量','0','1.6','18',NULL,6),(130,'生小葱','6棵小','50','22','微量','微量','0','1','5',NULL,6),(131,'欧芹','2汤匙','50','2','微量','微量','0','微量','微量',NULL,6),(132,'欧防风','1杯','155','95','2','1','0','3','22',NULL,6),(133,'豌豆','1杯','100','66','3','微量','0','0.1','13',NULL,6),(134,'新鲜蒸豌豆','1杯','100','70','5','微量','0','2.2','12',NULL,7),(135,'冷冻豌豆','1杯','100',NULL,'5','微量','0','1.8','12',NULL,7),(136,'煮熟裂荚豌豆','4杯','100','115','8','微量','0','0.4','21',NULL,7),(137,'加热豌豆','1杯','100','53','3','微量','0','1','10',NULL,7),(138,'罐装辣椒','1个','38','10','微量','微量','0','微量','2',NULL,7),(139,'生青椒, 甜','1个大','100','25','1','微量','0','1.4','6',NULL,7),(140,'辣椒塞牛肉和面包屑','1个中等','150','255','19','9','8','1','24',NULL,7),(141,'烤土豆','1个中等','100','100','2','微量','0','0.5','22',NULL,7),(142,'炸薯条','10根','60','155','-1','7','3','0.4','20',NULL,7),(143,'土豆泥 (加牛奶黄油)','1杯','200','230','4','12','11','0.7','28',NULL,7),(144,'锅煎土豆','3/4杯','100','268','4','14','6','0.40','33',NULL,7),(145,'奶酪焗土豆','3/4杯','100','145','6','8','7','0.40','14',NULL,7),(146,'去皮前蒸土豆','1个中等','100','80','2','微量','0','0.40','19',NULL,7),(147,'薯片','10片','20','110','1','7','4','微量','10',NULL,7),(148,'小萝卜','5个小','50','10','微量','0','0','0.3','2',NULL,7),(149,'瑞典芜菁','4杯','100','32','微量','0','0','1.4','8',NULL,7),(150,'大豆','1杯','200','260','22','11','0','3.2','20',NULL,7),(151,'菠菜','1杯','100','26','3','微量','0','1','3',NULL,7),(152,'西葫芦/南瓜(Summer Squash)','1杯','210','35','1','微量','0','0.6','8',NULL,7),(153,'冬南瓜泥','1杯','200','95','4','微量','0','2.6','23',NULL,7),(154,'红薯','1个中等','110','155','2','1','0','1','36',NULL,7),(155,'蜜饯红薯','1个中等','175','235','2','6','5','1.5','80',NULL,7),(156,'西红柿','1杯','240','50','2','微量','0','1','9',NULL,7),(157,'生西红柿, 2x2.5英寸','1个中等','150','30','1','微量','0','0.6','6',NULL,7),(158,'番茄汁','1杯','240','50','2','微量','0','0.6','10',NULL,7),(159,'番茄酱','1汤匙','17','15','微量','微量','0','微量','4',NULL,7),(160,'芜菁叶','1杯','145','45','4','1','0','1.8','8',NULL,7),(161,'蒸芜菁','1杯','155','40','1','微量','0','1.8','9',NULL,7),(162,'生豆瓣菜茎','1杯','50','9','1','微量','0','0.3','1',NULL,8),(163,'罐装苹果汁','1杯','250','125','微量','0','0','0','34',NULL,8),(164,'苹果醋','1/3杯','100','14','微量','0','0','0','3',NULL,8),(165,'生苹果','1个中等','130','70','微量','微量','0','1','18',NULL,8),(166,'炖或罐装苹果','1杯','240','100','微量','微量','0','2','26',NULL,8),(167,'杏子','1杯','250','220','2','微量','0','1','57',NULL,8),(168,'杏干, 未煮','1/2杯','75','220','4','微量','0','1','50',NULL,8),(169,'新鲜杏子','3个中等','114','55','1','微量','0','0.70','14',NULL,8),(170,'杏汁','1杯','250','140','1','微量','0','2','36',NULL,8),(171,'牛油果','1/2个大','108','185','2','18','12','1.80','6',NULL,8),(172,'香蕉','1根中等','150','85','1','微量','0','0.9','23',NULL,8),(173,'黑莓','1杯','144','85','2','1','0','6.60','19',NULL,8),(174,'蓝莓','1杯','250','245','1','微量','0','2','65',NULL,8),(175,'哈密瓜','1/2个中等','380','40','1','微量','0','2.20','9',NULL,8),(176,'樱桃','1杯','257','100','2','1','0','2','26',NULL,8),(177,'新鲜生樱桃','1杯','114','65','1','微量','0','0.8','15',NULL,8),(178,'加糖蔓越莓酱','1杯','277','530','微量','微量','0','1.2','142',NULL,8),(179,'枣子','1杯','178','505','4','微量','0','3.6','134',NULL,8),(180,'无花果','2个','42','120','2','微量','0','1.9','30',NULL,8),(181,'新鲜生无花果','3个中等','114','90','2','微量','0','1','22',NULL,8),(182,'糖水无花果罐头','3个','115','130','1','微量','0','1','32',NULL,8),(183,'罐装水果鸡尾酒','1杯','256','195','1','微量','0','0.5','50',NULL,8),(184,'西柚块','1杯','250','170','1','微量','0','0.5','44',NULL,9),(185,'新鲜西柚, 5英寸直径','1/2个','285','50','1','微量','微量','1','14',NULL,9),(186,'西柚汁','1杯','250','100','1','微量','0','1','24',NULL,9),(187,'葡萄','1杯','153','70','1','微量','0','0.8','16',NULL,9),(188,'欧洲品种, 如Muscat, Tokay','1杯','160','100','1','微量','0','0.7','26',NULL,9),(189,'葡萄汁','1杯','250','160','1','微量','0','微量','42',NULL,9),(190,'柠檬汁','1/2杯','125','30','微量','微量','0','微量','10',NULL,9),(191,'冷冻浓缩柠檬水','6盎司罐','220','430','微量','微量','0','微量','112',NULL,9),(192,'冷冻浓缩青柠水','6盎司罐','218','405','微量','微量','0','微量','108',NULL,9),(193,'大橄榄','10个','65','72','1','10','9','0.8','3',NULL,9),(194,'熟橄榄','10个','65','105','1','13','12','1','1',NULL,9),(195,'橙子 3英寸直径','1个中等','180','60','2','微量','微量','1','16',NULL,9),(196,'橙汁','8盎司 或','250','112','2','微量','0','0.2','25',NULL,9),(197,'冷冻橙汁','6盎司罐','210','330','2','微量','微量','0.4','78',NULL,9),(198,'木瓜','1/2个中等','200','75','1','微量','0','1.8','18',NULL,9),(199,'桃子','1杯','257','200','1','微量','0','1','52',NULL,9),(200,'新鲜生桃子','1个中等','114','35','1','微量','0','0.6','10',NULL,9),(201,'梨','1杯','255','195','1','微量','0','2','50',NULL,9),(202,'生梨, 3x2.5英寸','1个中等','182','100','1','1','0','2','25',NULL,9),(203,'柿子','1个中等','125','75','1','微量','0','2','20',NULL,9),(204,'菠萝','1大片','122','95','微量','微量','0','0.4','26',NULL,9),(205,'碎菠萝','1杯','260','205','1','微量','0','0.7','55',NULL,9),(206,'生菠萝丁','1杯','140','75','1','微量\'','0','0.6','19',NULL,9),(207,'菠萝汁','1杯','250','120','1','微量','0','0.2','32',NULL,9),(208,'李子','1杯','256','185','1','微量','0','0.7','50',NULL,9),(209,'生李子, 2英寸直径','1个','60','30','微量','微量','0','0.2','7',NULL,9),(210,'西梅干','1杯','270','300','3','1','0','0.8','81',NULL,9),(211,'西梅汁','1杯','240','170','1','微量','0','0.7','45',NULL,9),(212,'葡萄干','1/2杯','88','230','2','微量','0','0.7','82',NULL,10),(213,'覆盆子','1/2杯','100','100','微量','微量','0','2','25',NULL,10),(214,'生红覆盆子','3/4杯','100','57','微量','微量','0','5','14',NULL,10),(215,'加糖大黄','1杯','270','385','1','微量','0','1.9','98',NULL,10),(216,'草莓','1杯','227','242','1','微量','0','1.3','60',NULL,10),(217,'生草莓','1杯','149','54','微量','微量','0','1.9','12',NULL,10),(218,'橘子','1个中等','114','40','1','微量','0','1','10',NULL,10),(219,'西瓜','1楔形块','925','120','2','1','0','3.6','29',NULL,10),(220,'饼干','1个','38','130','3','4','3','微量','18',NULL,11),(221,'麦麸片','1杯','25','117','3','微量','0','0.10','32',NULL,11),(222,'面包, 碎麦','1片','23','60','2','1','1','0.10','12',NULL,11),(223,'黑麦面包','1片','23','55','2','1','1','0.10','12',NULL,11),(224,'白面包, 20片, 或','1磅面包','454','1,225','39','15','12','9.00','229',NULL,11),(225,'全麦面包','1磅面包','454','1,100','48','14','10','67.50','216',NULL,11),(226,'全麦面包','1片','23','55','2','1','0','0.31','11',NULL,11),(227,'玉米面包 (玉米粉制)','1份','50','100','3','4','2','0.30','15',NULL,11),(228,'玉米片','1杯','25','110','2','微量','0','0.1','25',NULL,11),(229,'煮熟玉米糁','1杯','242','120','8','微量','0','0.2','27',NULL,11),(230,'玉米粉','1杯','118','360','9','4','2','1.6','74',NULL,11),(231,'饼干(Crackers)','2个中等','14','55','1','1','0','微量','10',NULL,11),(232,'苏打饼干, 2.5英寸方形','2片','11','45','1','1','0','微量','8',NULL,11),(233,'麦粉(Farina)','1杯','238','105','3','微量','0','8','22',NULL,11),(234,'面粉','1杯','110','460','39','22','0','2.9','33',NULL,11),(235,'小麦粉 (通用)','1杯','110','400','12','1','0','0.3','84',NULL,11),(236,'小麦粉 (全麦)','1杯','120','390','13','2','0','2.8','79',NULL,11),(237,'通心粉','1杯','140','155','5','1','0','0.1','32',NULL,11),(238,'奶酪焗通心粉','1杯','220','475','18','25','24','微量','44',NULL,11),(239,'玛芬蛋糕','1个','48','135','4','5','4','微量','19',NULL,11),(240,'面条','1杯','160','200','7','2','2','0.1','37',NULL,11),(241,'燕麦片','1杯','236','150','5','3','2','4.6','26',NULL,11),(242,'薄煎饼 4英寸直径','4个','108','250','7','9','0','0.1','28',NULL,11),(243,'小麦薄煎饼 4英寸直径','4个','108','250','7','9','0','0.1','28',NULL,11),(244,'披萨 14英寸直径','1角','75','180','8','6','5','微量','23',NULL,11),(245,'咸爆米花','2杯','28','152','3','7','2','0.5','20',NULL,11),(246,'膨化大米','1杯','14','55','微量','微量','0','微量','12',NULL,11),(247,'预甜膨化小麦','1杯','28','105','1','微量','0','0.6','26',NULL,11),(248,'大米','1杯','208','748','15','3','0','1.2','154',NULL,11),(249,'预处理米','1杯','187','677','14','微量','0','0.4','142',NULL,11),(250,'白米','1杯','191','692','14','微量','0','0.3','150',NULL,11),(251,'米片','1杯','30','115','2','微量','0','0.1','26',NULL,11),(252,'米糠','1/2杯','50','132','6','6','0','1.2','28',NULL,11),(253,'面包卷','1个大','50','411','3','12','11','0.1','23',NULL,11),(254,'精粉面包卷','1个','38','115','3','2','2','微量','20',NULL,11),(255,'全麦面包卷','1个','40','102','4','1','0','0.1','20',NULL,11),(256,'肉酱意面','1杯','250','285','13','10','6','0.50','35',NULL,11),(257,'番茄奶酪意面','1杯','250','210','6','5','3','0.50','36',NULL,11),(258,'西班牙米饭','1杯','250','217','4','4','0','1.20','40',NULL,11),(259,'碎小麦饼干','1块','28','100','3','1','0','0.70','23',NULL,11),(260,'华夫饼','1个','75','240','8','9','1','0.10','30',NULL,11),(261,'小麦胚芽','1杯','68','245','17','7','3','2.50','34',NULL,11),(262,'烤麦胚芽麦片','1杯','65','260','20','7','3','2.50','36',NULL,11),(263,'未精炼小麦粉麦片','3/4杯','30','103','4','1','0','0.70','25',NULL,11),(264,'煮熟小麦','3/4杯','200','275','12','1','0','4.40','35',NULL,11),(265,'豆汤','1杯','250','190','8','5','4','0.60','30',NULL,12),(266,'牛肉汤','1杯','250','100','6','4','4','0.50','11',NULL,12),(267,'肉汤/清汤','1杯','240','24','5','0','0','0','0',NULL,12),(268,'鸡汤','1杯','250','75','4','2','2','0','10',NULL,12),(269,'蛤蜊浓汤','1杯','255','85','5','2','8','0.50','12',NULL,12),(270,'奶油浓汤','1杯','255','200','7','12','11','1.20','18',NULL,12),(271,'面条汤','1杯','250','115','6','4','3','0.20','13',NULL,12),(272,'豌豆汤','1杯','250','147','8','3','3','0.50','25',NULL,12),(273,'番茄汤','1杯','245','175','6','7','6','0.50','22',NULL,12),(274,'蔬菜汤','1杯','250','80','4','2','2','0','14',NULL,12),(275,'苹果派(Apple Betty)','1份','100','150','1','4','0','0.5','29',NULL,13),(276,'面包布丁','3/4杯','200','374','11','12','11','0.20','56',NULL,13),(277,'蛋糕','1片','40','110','3','微量','0','0','23',NULL,13),(278,'巧克力软糖蛋糕','1片','120','420','5','14','12','0.3','70',NULL,13),(279,'纸杯蛋糕','1个','50','160','3','3','2','微量','31',NULL,13),(280,'水果蛋糕','1片','30','105','2','4','3','0.2','17',NULL,13),(281,'姜饼','1片','55','180','2','7','6','微量','28',NULL,13),(282,'原味蛋糕, 无糖霜','1片','55','180','4','5','4','微量','31',NULL,13),(283,'海绵蛋糕','1片','40','115','3','2','2','0','22',NULL,13),(284,'糖果','5颗','25','104','微量','3','3','0','19',NULL,13),(285,'巧克力奶油糖','2颗','30','130','微量','4','4','0','24',NULL,13),(286,'软糖','2块','90','370','微量','12','11','0.1','80',NULL,13),(287,'硬糖','1盎司','28','90','微量','0','0','0','28',NULL,13),(288,'棉花糖','5颗','30','98','1','0','0','0','23',NULL,13),(289,'牛奶巧克力','2盎司条','56','290','2','6','6','0.2','44',NULL,13),(290,'巧克力糖浆','2汤匙','40','80','微量','微量','微量','0','22',NULL,13),(291,'甜甜圈','1个','33','135','2','7','4','微量','17',NULL,13),(292,'明胶甜点, 水制','1杯','239','155','4','微量','微量','0','36',NULL,13),(293,'蜂蜜','2汤匙','42','120','微量','0','0','0','30',NULL,14),(294,'冰淇淋','2杯','300','250','0','0','12','10','0',NULL,13),(295,'冰沙/雪泥','1杯','150','117','0','0','0','0','48',NULL,13),(296,'果酱','1汤匙','20','55','0','0','0','微量','14',NULL,14),(297,'果冻','1汤匙','20','50','0','0','0','0','13',NULL,14),(298,'糖蜜','1汤匙','20','45','0','0','0','8','11',NULL,14),(299,'甘蔗糖浆','1汤匙','20','50','0','0','0','0','13',NULL,14),(300,'9英寸直径派','1片','135','330','3','13','11','0.1','53',NULL,13),(301,'樱桃派','1片','135','340','3','13','11','0.1','55',NULL,13),(302,'蛋奶派','1片','130','265','7','11','10','0','34',NULL,13),(303,'柠檬酥皮派','1片','120','300','4','12','10','0.1','45',NULL,13),(304,'肉馅派','1片','135','340','3','9','8','0.70','62',NULL,13),(305,'南瓜派','1片','130','265','5','12','11','8','34',NULL,13),(306,'布丁 (糖)','1杯','200','770','0','0','0','0','199',NULL,13),(307,'糖 3茶匙','1汤匙','12','50','0','0','0','0','12',NULL,13),(308,'红糖, 压实, 深色','1杯','220','815','0','微量','0','0','210',NULL,14),(309,'糖浆','2汤匙','40','100','0','0','0','0','25',NULL,14),(310,'餐桌混合糖浆','2汤匙','40','110','0','0','0','0','29',NULL,14),(311,'木薯奶油布丁','1杯','250','335','10','10','9','0','42',NULL,13),(312,'杏仁','1/2杯','70','425','13','38','28','1.8','13',NULL,15),(313,'烤盐焗杏仁','1/2杯','70','439','13','40','31','1.8','13',NULL,15),(314,'巴西坚果','1/2杯','70','457','10','47','31','2','7',NULL,15),(315,'腰果','1/2杯','70','392','12','32','28','0.9','20',NULL,15),(316,'加糖椰丝','1/2杯','50','274','1','20','19','2','26',NULL,15),(317,'花生酱','1/3杯','50','300','12','25','17','0.9','9',NULL,15),(318,'天然花生酱','1/3杯','50','284','13','24','10','0.9','8',NULL,15),(319,'花生','1/3杯','50','290','13','25','16','1.2','9',NULL,15),(320,'碧根果','1/2杯','52','343','5','35','25','1.1','7',NULL,15),(321,'芝麻籽','1/2杯','50','280','9','24','13','3.1','10',NULL,15),(322,'葵花籽','1/2杯','50','280','12','26','7','1.9','10',NULL,15),(323,'核桃','1/2杯','50','325','7','32','7','1','8',NULL,15),(324,'啤酒','2杯','480','228','微量','0','0','0','8',NULL,16),(325,'杜松子酒','1盎司','28','70','0','0','0','0','微量',NULL,16),(326,'葡萄酒','1/2杯','120','164','微量','0','0','0','9',NULL,16),(327,'餐酒 (12.2% 酒精)','1/2杯','120','100','微量','0','0','0','5',NULL,16),(328,'碳酸饮料 (人工甜味剂)','12盎司','346','0','0','0','0','0','0',NULL,16),(329,'苏打水','12盎司','346','0','0','0','0','0','0',NULL,16),(330,'可乐饮料','12盎司','346','137','0','0','0','0','38',NULL,16),(331,'水果味苏打水','12盎司','346','161','0','0','0','0','42',NULL,16),(332,'姜汁汽水','12盎司','346','105','0','0','0','0','28',NULL,16),(333,'根汁啤酒','12盎司','346','140','0','0','0','0','35',NULL,16),(334,'咖啡','1杯','230','3','微量','0','0','0','1',NULL,16),(335,'茶','1杯','230','4','0','微量','0','0','1',NULL,16),(349,'test','1片','0.1','0.1','0.0','0.0','0.0',NULL,'0.0','',5),(350,'test','1杯','100.0','0.0','0.0','0.0','0.0',NULL,'0.0','foodimage/350/e19e442472a245849050d7c382771324.jpg',1),(351,'test','1杯','100.0','0.0','0.0','0.0','0.0',NULL,'0.0','foodimage/351/973bae98edfc4eda9983d844f1eb8adc.jpg',4),(355,'test','1杯','100.0','0.0','0.0','0.0',NULL,NULL,'0.0','foodimage/355/8c4d7c830b924dc58534a7ca33f5a6ad.jpg',6),(356,'test','1片','100.0','0.0','0.0','0.0',NULL,NULL,'0.0','foodimage/356/c5c9edfb781943c4b220d33f5f221106.jpg',6);
/*!40000 ALTER TABLE `food` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `food_category`
--

DROP TABLE IF EXISTS `food_category`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `food_category` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '分类名称',
  `description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '分类描述',
  `color` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '分类颜色',
  `sort_order` int DEFAULT '0' COMMENT '排序顺序',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_category_name` (`name`)
) ENGINE=InnoDB AUTO_INCREMENT=34 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='食物分类表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `food_category`
--

LOCK TABLES `food_category` WRITE;
/*!40000 ALTER TABLE `food_category` DISABLE KEYS */;
INSERT INTO `food_category` VALUES (1,'乳制品','乳制品','',0,'2025-05-07 10:37:19','2025-05-08 15:55:27'),(2,'脂肪, 油脂, 起酥油','','',0,'2025-05-07 10:37:19','2025-05-08 15:51:56'),(3,'肉类, 家禽',NULL,NULL,0,'2025-05-07 10:37:19','2025-05-07 10:37:19'),(4,'鱼类, 海鲜',NULL,NULL,0,'2025-05-07 10:37:19','2025-05-07 10:37:19'),(5,'蔬菜 (A-E)',NULL,NULL,0,'2025-05-07 10:37:19','2025-05-07 10:37:19'),(6,'蔬菜 (F-P)',NULL,NULL,0,'2025-05-07 10:37:19','2025-05-07 10:37:19'),(7,'蔬菜 (R-Z)',NULL,NULL,0,'2025-05-07 10:37:19','2025-05-07 10:37:19'),(8,'水果 (A-F)',NULL,NULL,0,'2025-05-07 10:37:19','2025-05-07 10:37:19'),(9,'水果 (G-P)',NULL,NULL,0,'2025-05-07 10:37:19','2025-05-07 10:37:19'),(10,'水果 (R-Z)',NULL,NULL,0,'2025-05-07 10:37:19','2025-05-07 10:37:19'),(11,'面包, 谷物, 快餐, 谷类',NULL,NULL,0,'2025-05-07 10:37:19','2025-05-07 10:37:19'),(12,'汤类',NULL,NULL,0,'2025-05-07 10:37:19','2025-05-07 10:37:19'),(13,'甜点, 糖果',NULL,NULL,0,'2025-05-07 10:37:19','2025-05-07 10:37:19'),(14,'果酱, 果冻',NULL,NULL,0,'2025-05-07 10:37:19','2025-05-07 10:37:19'),(15,'种子与坚果',NULL,NULL,0,'2025-05-07 10:37:19','2025-05-07 10:37:19'),(16,'饮品, 酒精, 饮料',NULL,NULL,0,'2025-05-07 10:37:19','2025-05-07 10:37:19');
/*!40000 ALTER TABLE `food_category` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `nutrition_advice`
--

DROP TABLE IF EXISTS `nutrition_advice`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `nutrition_advice` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `type` varchar(20) NOT NULL COMMENT '建议类型: warning, info, danger, success',
  `title` varchar(100) NOT NULL COMMENT '建议标题',
  `description` varchar(500) NOT NULL COMMENT '建议详情',
  `condition_type` varchar(20) NOT NULL COMMENT '条件类型: protein, carbs, fat, calorie',
  `min_percentage` int DEFAULT NULL COMMENT '最小百分比阈值',
  `max_percentage` int DEFAULT NULL COMMENT '最大百分比阈值',
  `is_default` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否为默认建议',
  `priority` int NOT NULL DEFAULT '0' COMMENT '优先级，数字越大优先级越高',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：1-启用，0-禁用',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_condition_type` (`condition_type`) COMMENT '条件类型索引',
  KEY `idx_status` (`status`) COMMENT '状态索引'
) ENGINE=InnoDB AUTO_INCREMENT=13 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='营养建议配置表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `nutrition_advice`
--

LOCK TABLES `nutrition_advice` WRITE;
/*!40000 ALTER TABLE `nutrition_advice` DISABLE KEYS */;
INSERT INTO `nutrition_advice` VALUES (1,'warning','蛋白质摄入不足','建议增加鸡胸肉、鱼类、豆制品等高蛋白食物的摄入。','protein',0,60,0,10,1,'2025-05-15 15:42:50','2025-05-15 15:42:50'),(2,'warning','蛋白质摄入过多','蛋白质摄入过多可能增加肾脏负担，建议适当控制高蛋白食物的摄入。','protein',150,999,0,10,1,'2025-05-15 15:42:50','2025-05-15 15:42:50'),(3,'success','蛋白质摄入适中','目前蛋白质摄入量适中，很好地满足了身体需要。','protein',90,110,0,5,1,'2025-05-15 15:42:50','2025-05-15 15:42:50'),(4,'info','碳水摄入偏低','碳水是身体能量的主要来源，建议适当增加全谷物、薯类等优质碳水食物的摄入。','carbs',0,60,0,10,1,'2025-05-15 15:42:50','2025-05-15 15:42:50'),(5,'danger','碳水摄入过高','过多的碳水摄入可能导致血糖波动和肥胖，建议减少精制碳水如白面包、糖果等的摄入。','carbs',150,999,0,10,1,'2025-05-15 15:42:50','2025-05-15 15:42:50'),(6,'success','碳水摄入适中','目前碳水摄入量适中，很好地满足了身体需要。','carbs',90,110,0,5,1,'2025-05-15 15:42:50','2025-05-15 15:42:50'),(7,'info','脂肪摄入偏低','适量脂肪对健康有益，建议适当增加鱼油、橄榄油、坚果等健康脂肪的摄入。','fat',0,60,0,10,1,'2025-05-15 15:42:50','2025-05-15 15:42:50'),(8,'danger','脂肪摄入过高','脂肪摄入过多可能增加心血管疾病风险，建议减少油炸食品和高脂肪食物的摄入。','fat',150,999,0,10,1,'2025-05-15 15:42:50','2025-05-15 15:42:50'),(9,'success','脂肪摄入适中','目前脂肪摄入量适中，很好地满足了身体需要。','fat',90,110,0,5,1,'2025-05-15 15:42:50','2025-05-15 15:42:50'),(10,'info','营养摄入基本合理','今日的营养摄入基本合理，保持均衡饮食有助于健康。','default',NULL,NULL,1,1,1,'2025-05-15 15:42:50','2025-05-15 15:42:50');
/*!40000 ALTER TABLE `nutrition_advice` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `user`
--

DROP TABLE IF EXISTS `user`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `user` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL,
  `password` varchar(100) NOT NULL,
  `email` varchar(100) NOT NULL,
  `role` varchar(20) NOT NULL DEFAULT 'USER',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '1表示用户启动，0表示用户被封禁',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `openid` varchar(100) DEFAULT NULL COMMENT '微信openid，用于唯一标识用户',
  `avatar_url` varchar(255) DEFAULT NULL COMMENT '用户头像URL',
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`),
  UNIQUE KEY `email` (`email`),
  UNIQUE KEY `idx_user_openid` (`openid`)
) ENGINE=InnoDB AUTO_INCREMENT=26 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `user`
--

LOCK TABLES `user` WRITE;
/*!40000 ALTER TABLE `user` DISABLE KEYS */;
INSERT INTO `user` VALUES (3,'user1','$2a$10$LzQtjCwPnLF2EMQL0xGBhuRvCgbou.Jo22iC/BY0MU72BanG9qmee','<EMAIL>','USER',1,'2025-03-16 15:23:34',NULL,''),(5,'admin','$2a$10$dF1K5R2na6uoNvTX2iisb.h10UmNlLDxGw5e54P9yClrafKLj891y','<EMAIL>','ADMIN',1,'2025-03-16 15:23:34',NULL,NULL),(6,'user2','$2a$10$6XPCs/fgnQLKJK9ZFFlb../P0lmsk5yosVoh08LpUq0nptXCpKgAu','<EMAIL>','USER',1,'2025-03-16 15:23:34',NULL,NULL),(16,'user3','$2a$10$FgThbu19gy1j4Ra8./yU2.v9.9tL4FZGTONRl0TIHZ1lVQetb0/U6','<EMAIL>','USER',1,'2025-04-03 16:07:47',NULL,NULL),(18,'user4','$2a$10$jGJBauIlLkuIYB3FTrrz.u/xGYz9lu8HRI0axWNc3IAd/z70YC.aa','<EMAIL>','USER',0,'2025-04-03 16:08:25',NULL,NULL),(19,'user5','$2a$10$9Z3emLlOLj7dm8oIlIe9hup0YNkHBhbDedfWI.11jhn3ZJn/eh8di','<EMAIL>','USER',1,'2025-04-03 16:48:19',NULL,NULL),(23,'wx_d4655ce4','$2a$10$erx6ynWzYMcrmzheO6y9e.1dpK9XVgsgfk2SJ0jQOluS85DH9C5nu','<EMAIL>','USER',1,'2025-04-06 19:33:04','oXYCm7d_dLX8Qt3lTVaxgdO-9fvY','avatar/23/21ab7b8d43704655800dc9c7c1dd68f6.png'),(24,'user8','$2a$10$f3TnGC8RukFAyVGAARhGYO7T8ffKFv70YUR4Gqmaws3b8UEvn1gF2','<EMAIL>','USER',1,'2025-04-29 19:52:59',NULL,NULL),(25,'user9','$2a$10$fG3WIU6GMjj/sYY6imcmPuVP7ZQVz3CYjjw1UTfk3LCpyitnMjUa.','<EMAIL>','USER',1,'2025-04-29 20:47:41',NULL,NULL);
/*!40000 ALTER TABLE `user` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `user_nutrition_goals`
--

DROP TABLE IF EXISTS `user_nutrition_goals`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `user_nutrition_goals` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `user_id` bigint NOT NULL,
  `calorie_target` int DEFAULT NULL,
  `weight_target` decimal(5,2) DEFAULT NULL,
  `protein_target` int DEFAULT NULL,
  `carbs_target` int DEFAULT NULL,
  `fat_target` int DEFAULT NULL,
  `is_vegetarian` tinyint(1) DEFAULT '0',
  `is_low_carb` tinyint(1) DEFAULT '0',
  `is_high_protein` tinyint(1) DEFAULT '0',
  `is_gluten_free` tinyint(1) DEFAULT '0',
  `is_low_sodium` tinyint(1) DEFAULT '0',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `user_nutrition_goals`
--

LOCK TABLES `user_nutrition_goals` WRITE;
/*!40000 ALTER TABLE `user_nutrition_goals` DISABLE KEYS */;
INSERT INTO `user_nutrition_goals` VALUES (1,23,2400,46.00,88,320,90,1,1,0,1,0,'2025-04-09 00:22:12','2025-04-28 00:58:52');
/*!40000 ALTER TABLE `user_nutrition_goals` ENABLE KEYS */;
UNLOCK TABLES;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-05-25 11:19:02
