package errs

import "net/http"

// ErrorMapping 错误码映射配置
type ErrorMapping struct {
	HTTPStatus int    // HTTP状态码
	Message    string // 前端友好消息
}

// GetErrorMapping 获取错误码对应的映射信息
func GetErrorMapping(code string) ErrorMapping {
	mappings := map[string]ErrorMapping{
		// 文件模块
		CodeFileUnsupportedType: {
			HTTPStatus: http.StatusBadRequest,
			Message:    "不支持的文件类型",
		},
		CodeFileNotFound: {
			HTTPStatus: http.StatusNotFound,
			Message:    "文件不存在",
		},
		CodeFileStorageError: {
			HTTPStatus: http.StatusInternalServerError,
			Message:    "文件存储服务错误",
		},

		// 用户模块
		CodeUserNotFound: {
			HTTPStatus: http.StatusNotFound,
			Message:    "用户不存在",
		},
		CodeUserAlreadyExists: {
			HTTPStatus: http.StatusConflict,
			Message:    "用户已存在",
		},
		CodeUserDisabled: {
			HTTPStatus: http.StatusForbidden,
			Message:    "账户已被禁用",
		},
		CodeUserEmailExists: {
			HTTPStatus: http.StatusConflict,
			Message:    "邮箱已被注册",
		},
		CodeUserWeakPassword: {
			HTTPStatus: http.StatusBadRequest,
			Message:    "密码强度不足",
		},

		// 认证模块
		CodeAuthUserNotFound: {
			HTTPStatus: http.StatusUnauthorized,
			Message:    "用户名或密码错误",
		},
		CodeAuthInvalidPassword: {
			HTTPStatus: http.StatusUnauthorized,
			Message:    "用户名或密码错误",
		},
		CodeAuthTokenExpired: {
			HTTPStatus: http.StatusUnauthorized,
			Message:    "登录已过期，请重新登录",
		},
		CodeAuthTokenInvalid: {
			HTTPStatus: http.StatusUnauthorized,
			Message:    "认证失败，请重新登录",
		},
		CodeAuthPermissionDenied: {
			HTTPStatus: http.StatusForbidden,
			Message:    "权限不足",
		},
		CodeAuthTokenGenerationFailed: {
			HTTPStatus: http.StatusInternalServerError,
			Message:    "登录失败，请重试",
		},

		// 食物模块
		CodeFoodNotFound: {
			HTTPStatus: http.StatusNotFound,
			Message:    "食物不存在",
		},
		CodeFoodAlreadyExists: {
			HTTPStatus: http.StatusConflict,
			Message:    "食物已存在",
		},
		CodeFoodCategoryNotFound: {
			HTTPStatus: http.StatusNotFound,
			Message:    "食物分类不存在",
		},

		// 饮食记录模块
		CodeDietRecordNotFound: {
			HTTPStatus: http.StatusNotFound,
			Message:    "饮食记录不存在",
		},
		CodeDietInvalidMealType: {
			HTTPStatus: http.StatusBadRequest,
			Message:    "无效的餐次类型",
		},
		CodeDietDuplicateRecord: {
			HTTPStatus: http.StatusConflict,
			Message:    "该餐次记录已存在",
		},
		CodeDietInvalidDateRange: {
			HTTPStatus: http.StatusBadRequest,
			Message:    "日期范围无效",
		},

		// 营养模块
		CodeNutritionGoalNotFound: {
			HTTPStatus: http.StatusNotFound,
			Message:    "营养目标不存在",
		},
		CodeNutritionInvalidDateRange: {
			HTTPStatus: http.StatusBadRequest,
			Message:    "营养统计日期范围无效",
		},
		CodeNutritionCalculationError: {
			HTTPStatus: http.StatusInternalServerError,
			Message:    "营养计算失败",
		},

		// 系统错误
		CodeInternal: {
			HTTPStatus: http.StatusInternalServerError,
			Message:    "系统内部错误",
		},
		CodeDatabaseError: {
			HTTPStatus: http.StatusInternalServerError,
			Message:    "数据访问错误",
		},
		CodeExternalServiceError: {
			HTTPStatus: http.StatusInternalServerError,
			Message:    "外部服务错误",
		},
		CodeSystemNotFound: {
			HTTPStatus: http.StatusNotFound,
			Message:    "请求的资源不存在",
		},
		CodeSystemInvalidParams: {
			HTTPStatus: http.StatusBadRequest,
			Message:    "请求参数错误",
		},
	}

	if mapping, exists := mappings[code]; exists {
		return mapping
	}

	// 默认映射
	return ErrorMapping{
		HTTPStatus: http.StatusInternalServerError,
		Message:    "未知错误",
	}
}
