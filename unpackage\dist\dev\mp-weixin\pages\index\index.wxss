@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.dashboard {
  padding: 0 20rpx 20rpx;
  background-color: #4CAF50;
  background-image: linear-gradient(to bottom, #4CAF50, #43A047);
  min-height: 100vh;
}
.safe-area {
  height: 80rpx;
  /* 增加顶部安全区域高度 */
  background-color: #4CAF50;
}
.header {
  padding: 0 30rpx;
  display: flex;
  align-items: center;
  height: 88rpx;
  /* 与胶囊按钮高度一致 */
  margin-bottom: 10rpx;
}
.header .left-section {
  width: 120rpx;
}
.header .left-section .title {
  font-size: 36rpx;
  font-weight: bold;
  color: #ffffff;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}
.header .center-section {
  flex: 1;
  display: flex;
  justify-content: center;
}
.header .center-section .completed-status {
  height: 60rpx;
  padding: 0 20rpx;
  background-color: rgba(255, 255, 255, 0.15);
  border-radius: 30rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
}
.header .center-section .completed-status .progress {
  font-size: 26rpx;
  color: #ffffff;
  text-shadow: 0 1px 1px rgba(0, 0, 0, 0.1);
}
.header .right-section {
  width: 120rpx;
}
.date-info {
  padding: 0 30rpx;
  margin-bottom: 15rpx;
}
.date-info .date {
  font-size: 30rpx;
  color: #ffffff;
  opacity: 0.95;
  text-shadow: 0 1px 1px rgba(0, 0, 0, 0.1);
}
.nutrition-progress-container {
  margin: 20rpx 0 30rpx;
  padding: 0 20rpx;
}
.nutrition-progress-container .progress-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 25rpx;
}
.nutrition-progress-container .progress-row:last-child {
  margin-bottom: 0;
}
.nutrition-progress-container .progress-row .progress-item {
  width: 48%;
  /* 每个项目占用行宽的48%，留出间距 */
  background-color: rgba(0, 0, 0, 0.06);
  border-radius: 16rpx;
  padding: 20rpx 0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}
.quick-actions {
  display: flex;
  justify-content: space-around;
  margin: 40rpx 0;
}
.quick-actions .action-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.15);
  border-radius: 16rpx;
  padding: 24rpx 15rpx;
  width: 200rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  border: 1rpx solid rgba(255, 255, 255, 0.2);
}
.quick-actions .action-item .action-icon {
  width: 80rpx;
  height: 80rpx;
  margin-bottom: 15rpx;
  background-color: rgba(255, 255, 255, 0.25);
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}
.quick-actions .action-item .action-icon image {
  width: 50rpx;
  height: 50rpx;
}
.quick-actions .action-item text {
  font-size: 28rpx;
  color: #ffffff;
  font-weight: 500;
  text-shadow: 0 1px 1px rgba(0, 0, 0, 0.1);
}
.quick-actions .action-item:active {
  -webkit-transform: scale(0.96);
          transform: scale(0.96);
  box-shadow: 0 2rpx 5rpx rgba(0, 0, 0, 0.1);
}
.nutrition-advice {
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 25rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
}
.nutrition-advice .advice-header {
  margin-bottom: 15rpx;
  display: flex;
  align-items: center;
}
.nutrition-advice .advice-header .advice-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
  position: relative;
  padding-left: 20rpx;
}
.nutrition-advice .advice-header .advice-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
  width: 8rpx;
  height: 28rpx;
  background-color: #4CAF50;
  border-radius: 4rpx;
}
.nutrition-advice .advice-content {
  padding: 10rpx 0;
}
.nutrition-advice .advice-content text {
  font-size: 28rpx;
  color: #666666;
  line-height: 1.6;
}
.nutrition-advice .advice-content .advice-list .advice-item {
  margin-bottom: 20rpx;
  padding: 15rpx;
  border-radius: 10rpx;
  background-color: #f9f9f9;
  border-left: 4rpx solid #4CAF50;
}
.nutrition-advice .advice-content .advice-list .advice-item:last-child {
  margin-bottom: 0;
}
.nutrition-advice .advice-content .advice-list .advice-item.warning {
  border-left-color: #FF9800;
}
.nutrition-advice .advice-content .advice-list .advice-item.danger {
  border-left-color: #F44336;
}
.nutrition-advice .advice-content .advice-list .advice-item.success {
  border-left-color: #4CAF50;
}
.nutrition-advice .advice-content .advice-list .advice-item.info {
  border-left-color: #2196F3;
}
.nutrition-advice .advice-content .advice-list .advice-item .advice-item-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 8rpx;
  display: block;
}
.nutrition-advice .advice-content .advice-list .advice-item .advice-item-desc {
  font-size: 26rpx;
  color: #666666;
  line-height: 1.5;
}

