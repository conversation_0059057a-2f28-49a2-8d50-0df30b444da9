package response

import (
	"net/http"

	"github.com/gin-gonic/gin"
)

// ApiResponse 统一API响应格式
type ApiResponse struct {
	Code    interface{} `json:"code"`    // 可以是 int 或 string
	Message string      `json:"message"`
	Data    interface{} `json:"data"`
}

// PageResult 分页结果
type PageResult struct {
	Total   int64       `json:"total"`   // 总记录数
	Records interface{} `json:"records"` // 记录列表
	Current int         `json:"current"` // 当前页码
	Size    int         `json:"size"`    // 每页大小
}

// 成功响应码
const SuccessCode = 200

// 成功响应消息
const SuccessMessage = "成功"

// Success 成功响应
func Success(c *gin.Context, data interface{}) {
	c.JSON(http.StatusOK, ApiResponse{
		Code:    SuccessCode,
		Message: SuccessMessage,
		Data:    data,
	})
}

// SuccessWithPage 分页成功响应
func SuccessWithPage(c *gin.Context, data interface{}, total int64, current, size int) {
	pageResult := PageResult{
		Total:   total,
		Records: data,
		Current: current,
		Size:    size,
	}
	Success(c, pageResult)
}

// ErrorWithCodeAndMessage 使用业务错误码的错误响应
func ErrorWithCodeAndMessage(c *gin.Context, httpStatus int, code, message string) {
	c.JSON(httpStatus, ApiResponse{
		Code:    code,
		Message: message,
		Data:    nil,
	})
}

// SuccessWithCode 使用业务码的成功响应（保持原有的数字200）
func SuccessWithCode(c *gin.Context, data interface{}) {
	c.JSON(http.StatusOK, ApiResponse{
		Code:    SuccessCode, // 使用数字 200
		Message: SuccessMessage,
		Data:    data,
	})
}
