package diet

import (
	"errors"
	"gorm.io/gorm"
	"shikeyinxiang/internal/entities"
	"shikeyinxiang/internal/pkg/errs"
)

// IDietRecordFoodRepo 定义了饮食记录食物明细仓库需要实现的所有方法
type IDietRecordFoodRepo interface {
	// 基础CRUD操作
	Create(dietRecordFood *entities.DietRecordFood) error
	GetByID(id int64) (*entities.DietRecordFood, error)
	GetByIDWithFood(id int64) (*entities.DietRecordFood, error)
	Update(dietRecordFood *entities.DietRecordFood) error
	Delete(id int64) error

	// 批量操作
	BatchCreate(dietRecordFoods []*entities.DietRecordFood) error
	BatchDeleteByDietRecordID(dietRecordID int64) error

	// 查询方法
	GetByDietRecordID(dietRecordID int64) ([]*entities.DietRecordFood, error)
	GetByDietRecordIDWithFood(dietRecordID int64) ([]*entities.DietRecordFood, error)
	GetByFoodID(foodID int64, offset, limit int) ([]*entities.DietRecordFood, int64, error)
	GetByDietRecordIDs(dietRecordIDs []int64) ([]*entities.DietRecordFood, error)

	// 营养计算支持
	CalculateNutritionByDietRecordID(dietRecordID int64) (*NutritionStats, error)
	GetNutritionSumByDietRecordIDs(dietRecordIDs []int64) (map[int64]*NutritionStats, error)

	// 统计方法
	CountByDietRecordID(dietRecordID int64) (int64, error)
	CountByFoodID(foodID int64) (int64, error)
	ExistsByDietRecordAndFood(dietRecordID, foodID int64) (bool, error)

	// 事务支持
	CreateWithTx(tx *gorm.DB, dietRecordFood *entities.DietRecordFood) error
	BatchCreateWithTx(tx *gorm.DB, dietRecordFoods []*entities.DietRecordFood) error
	UpdateWithTx(tx *gorm.DB, dietRecordFood *entities.DietRecordFood) error
	BatchUpdateWithTx(tx *gorm.DB, dietRecordFoods []*entities.DietRecordFood) error
	DeleteWithTx(tx *gorm.DB, id int64) error
	BatchDeleteWithTx(tx *gorm.DB, ids []int64) error
	BatchDeleteByDietRecordIDWithTx(tx *gorm.DB, dietRecordID int64) error
}

// dietRecordFoodRepository 饮食记录食物明细仓储
type dietRecordFoodRepository struct {
	db *gorm.DB
}

// NewDietRecordFoodRepository 创建饮食记录食物明细仓储实例
func NewDietRecordFoodRepository(db *gorm.DB) IDietRecordFoodRepo {
	return &dietRecordFoodRepository{
		db: db,
	}
}

// 确保 dietRecordFoodRepository 实现了 IDietRecordFoodRepo 接口
var _ IDietRecordFoodRepo = &dietRecordFoodRepository{}

// Create 创建新饮食记录食物明细
func (r *dietRecordFoodRepository) Create(dietRecordFood *entities.DietRecordFood) error {
	if err := r.db.Create(dietRecordFood).Error; err != nil {
		return errs.Wrap(errs.CodeDatabaseError, "failed to create diet record food", err)
	}
	return nil
}

// GetByID 根据ID获取饮食记录食物明细
func (r *dietRecordFoodRepository) GetByID(id int64) (*entities.DietRecordFood, error) {
	var dietRecordFood entities.DietRecordFood
	if err := r.db.Where("id = ?", id).First(&dietRecordFood).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errs.New(errs.CodeDietRecordNotFound, "diet record food not found")
		}
		return nil, errs.Wrap(errs.CodeDatabaseError, "failed to query diet record food by id", err)
	}
	return &dietRecordFood, nil
}

// GetByIDWithFood 根据ID获取饮食记录食物明细（包含食物信息）
func (r *dietRecordFoodRepository) GetByIDWithFood(id int64) (*entities.DietRecordFood, error) {
	var dietRecordFood entities.DietRecordFood
	if err := r.db.Preload("Food").Where("id = ?", id).First(&dietRecordFood).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errs.New(errs.CodeDietRecordNotFound, "diet record food not found")
		}
		return nil, errs.Wrap(errs.CodeDatabaseError, "failed to query diet record food with food by id", err)
	}
	return &dietRecordFood, nil
}

// Update 更新饮食记录食物明细信息
func (r *dietRecordFoodRepository) Update(dietRecordFood *entities.DietRecordFood) error {
	if err := r.db.Save(dietRecordFood).Error; err != nil {
		return errs.Wrap(errs.CodeDatabaseError, "failed to update diet record food", err)
	}
	return nil
}

// Delete 删除饮食记录食物明细
func (r *dietRecordFoodRepository) Delete(id int64) error {
	if err := r.db.Delete(&entities.DietRecordFood{}, id).Error; err != nil {
		return errs.Wrap(errs.CodeDatabaseError, "failed to delete diet record food", err)
	}
	return nil
}

// BatchCreate 批量创建饮食记录食物明细
func (r *dietRecordFoodRepository) BatchCreate(dietRecordFoods []*entities.DietRecordFood) error {
	if len(dietRecordFoods) == 0 {
		return nil
	}
	if err := r.db.Create(&dietRecordFoods).Error; err != nil {
		return errs.Wrap(errs.CodeDatabaseError, "failed to batch create diet record foods", err)
	}
	return nil
}

// BatchDelete 批量删除饮食记录食物明细
func (r *dietRecordFoodRepository) BatchDelete(ids []int64) error {
	if len(ids) == 0 {
		return nil
	}
	if err := r.db.Where("id IN ?", ids).Delete(&entities.DietRecordFood{}).Error; err != nil {
		return errs.Wrap(errs.CodeDatabaseError, "failed to batch delete diet record foods", err)
	}
	return nil
}

// BatchDeleteByDietRecordID 根据饮食记录ID批量删除食物明细
func (r *dietRecordFoodRepository) BatchDeleteByDietRecordID(dietRecordID int64) error {
	if err := r.db.Where("diet_record_id = ?", dietRecordID).Delete(&entities.DietRecordFood{}).Error; err != nil {
		return errs.Wrap(errs.CodeDatabaseError, "failed to batch delete diet record foods by diet record id", err)
	}
	return nil
}

// GetByDietRecordID 根据饮食记录ID获取食物明细列表
func (r *dietRecordFoodRepository) GetByDietRecordID(dietRecordID int64) ([]*entities.DietRecordFood, error) {
	var dietRecordFoods []*entities.DietRecordFood
	if err := r.db.Where("diet_record_id = ?", dietRecordID).Order("id ASC").Find(&dietRecordFoods).Error; err != nil {
		return nil, errs.Wrap(errs.CodeDatabaseError, "failed to get diet record foods by diet record id", err)
	}
	return dietRecordFoods, nil
}

// GetByDietRecordIDWithFood 根据饮食记录ID获取食物明细列表（包含食物信息）
func (r *dietRecordFoodRepository) GetByDietRecordIDWithFood(dietRecordID int64) ([]*entities.DietRecordFood, error) {
	var dietRecordFoods []*entities.DietRecordFood
	if err := r.db.Preload("Food").Where("diet_record_id = ?", dietRecordID).Order("id ASC").Find(&dietRecordFoods).Error; err != nil {
		return nil, errs.Wrap(errs.CodeDatabaseError, "failed to get diet record foods with food by diet record id", err)
	}
	return dietRecordFoods, nil
}

// GetByFoodID 根据食物ID获取饮食记录食物明细列表（分页）
func (r *dietRecordFoodRepository) GetByFoodID(foodID int64, offset, limit int) ([]*entities.DietRecordFood, int64, error) {
	var dietRecordFoods []*entities.DietRecordFood
	var total int64

	query := r.db.Model(&entities.DietRecordFood{}).Where("food_id = ?", foodID)

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, errs.Wrap(errs.CodeDatabaseError, "failed to count diet record foods by food id", err)
	}

	// 获取分页数据
	if err := query.Offset(offset).Limit(limit).Order("created_at DESC").Find(&dietRecordFoods).Error; err != nil {
		return nil, 0, errs.Wrap(errs.CodeDatabaseError, "failed to list diet record foods by food id", err)
	}

	return dietRecordFoods, total, nil
}

// GetByDietRecordIDs 根据饮食记录ID列表获取食物明细
func (r *dietRecordFoodRepository) GetByDietRecordIDs(dietRecordIDs []int64) ([]*entities.DietRecordFood, error) {
	if len(dietRecordIDs) == 0 {
		return []*entities.DietRecordFood{}, nil
	}

	var dietRecordFoods []*entities.DietRecordFood
	if err := r.db.Where("diet_record_id IN ?", dietRecordIDs).Order("diet_record_id ASC, id ASC").Find(&dietRecordFoods).Error; err != nil {
		return nil, errs.Wrap(errs.CodeDatabaseError, "failed to get diet record foods by diet record ids", err)
	}
	return dietRecordFoods, nil
}

// CalculateNutritionByDietRecordID 计算指定饮食记录的营养统计
func (r *dietRecordFoodRepository) CalculateNutritionByDietRecordID(dietRecordID int64) (*NutritionStats, error) {
	var result NutritionStats

	if err := r.db.Model(&entities.DietRecordFood{}).
		Select("SUM(calories) as total_calorie, SUM(protein) as total_protein, SUM(fat) as total_fat, SUM(carbs) as total_carbs, COUNT(*) as record_count").
		Where("diet_record_id = ?", dietRecordID).
		Scan(&result).Error; err != nil {
		return nil, errs.Wrap(errs.CodeDatabaseError, "failed to calculate nutrition by diet record id", err)
	}

	return &result, nil
}

// GetNutritionSumByDietRecordIDs 获取多个饮食记录的营养统计汇总
func (r *dietRecordFoodRepository) GetNutritionSumByDietRecordIDs(dietRecordIDs []int64) (map[int64]*NutritionStats, error) {
	if len(dietRecordIDs) == 0 {
		return make(map[int64]*NutritionStats), nil
	}

	var results []struct {
		DietRecordID int64   `json:"diet_record_id"`
		TotalCalorie float64 `json:"total_calorie"`
		TotalProtein float64 `json:"total_protein"`
		TotalFat     float64 `json:"total_fat"`
		TotalCarbs   float64 `json:"total_carbs"`
		RecordCount  int64   `json:"record_count"`
	}

	if err := r.db.Model(&entities.DietRecordFood{}).
		Select("diet_record_id, SUM(calories) as total_calorie, SUM(protein) as total_protein, SUM(fat) as total_fat, SUM(carbs) as total_carbs, COUNT(*) as record_count").
		Where("diet_record_id IN ?", dietRecordIDs).
		Group("diet_record_id").
		Scan(&results).Error; err != nil {
		return nil, errs.Wrap(errs.CodeDatabaseError, "failed to get nutrition sum by diet record ids", err)
	}

	// 转换为map
	nutritionMap := make(map[int64]*NutritionStats)
	for _, result := range results {
		nutritionMap[result.DietRecordID] = &NutritionStats{
			TotalCalorie: result.TotalCalorie,
			TotalProtein: result.TotalProtein,
			TotalFat:     result.TotalFat,
			TotalCarbs:   result.TotalCarbs,
			RecordCount:  result.RecordCount,
		}
	}

	return nutritionMap, nil
}

// CountByDietRecordID 统计指定饮食记录的食物明细数量
func (r *dietRecordFoodRepository) CountByDietRecordID(dietRecordID int64) (int64, error) {
	var count int64
	if err := r.db.Model(&entities.DietRecordFood{}).Where("diet_record_id = ?", dietRecordID).Count(&count).Error; err != nil {
		return 0, errs.Wrap(errs.CodeDatabaseError, "failed to count diet record foods by diet record id", err)
	}
	return count, nil
}

// CountByFoodID 统计指定食物的使用次数
func (r *dietRecordFoodRepository) CountByFoodID(foodID int64) (int64, error) {
	var count int64
	if err := r.db.Model(&entities.DietRecordFood{}).Where("food_id = ?", foodID).Count(&count).Error; err != nil {
		return 0, errs.Wrap(errs.CodeDatabaseError, "failed to count diet record foods by food id", err)
	}
	return count, nil
}

// ExistsByDietRecordAndFood 检查指定饮食记录中是否已包含指定食物
func (r *dietRecordFoodRepository) ExistsByDietRecordAndFood(dietRecordID, foodID int64) (bool, error) {
	var count int64
	if err := r.db.Model(&entities.DietRecordFood{}).Where("diet_record_id = ? AND food_id = ?", dietRecordID, foodID).Count(&count).Error; err != nil {
		return false, errs.Wrap(errs.CodeDatabaseError, "failed to check diet record food exists", err)
	}
	return count > 0, nil
}

// CreateWithTx 在事务中创建饮食记录食物明细
func (r *dietRecordFoodRepository) CreateWithTx(tx *gorm.DB, dietRecordFood *entities.DietRecordFood) error {
	if err := tx.Create(dietRecordFood).Error; err != nil {
		return errs.Wrap(errs.CodeDatabaseError, "failed to create diet record food with tx", err)
	}
	return nil
}

// BatchCreateWithTx 在事务中批量创建饮食记录食物明细
func (r *dietRecordFoodRepository) BatchCreateWithTx(tx *gorm.DB, dietRecordFoods []*entities.DietRecordFood) error {
	if len(dietRecordFoods) == 0 {
		return nil
	}
	if err := tx.Create(&dietRecordFoods).Error; err != nil {
		return errs.Wrap(errs.CodeDatabaseError, "failed to batch create diet record foods with tx", err)
	}
	return nil
}

// UpdateWithTx 在事务中更新饮食记录食物明细
func (r *dietRecordFoodRepository) UpdateWithTx(tx *gorm.DB, dietRecordFood *entities.DietRecordFood) error {
	if err := tx.Save(dietRecordFood).Error; err != nil {
		return errs.Wrap(errs.CodeDatabaseError, "failed to update diet record food with tx", err)
	}
	return nil
}

// BatchUpdateWithTx 在事务中批量更新饮食记录食物明细
func (r *dietRecordFoodRepository) BatchUpdateWithTx(tx *gorm.DB, dietRecordFoods []*entities.DietRecordFood) error {
	if len(dietRecordFoods) == 0 {
		return nil
	}

	// GORM的Save方法不支持批量更新，需要逐个更新
	for _, food := range dietRecordFoods {
		if err := tx.Save(food).Error; err != nil {
			return errs.Wrap(errs.CodeDatabaseError, "failed to batch update diet record food with tx", err)
		}
	}
	return nil
}

// DeleteWithTx 在事务中删除饮食记录食物明细
func (r *dietRecordFoodRepository) DeleteWithTx(tx *gorm.DB, id int64) error {
	if err := tx.Delete(&entities.DietRecordFood{}, id).Error; err != nil {
		return errs.Wrap(errs.CodeDatabaseError, "failed to delete diet record food with tx", err)
	}
	return nil
}

// BatchDeleteWithTx 在事务中批量删除饮食记录食物明细
func (r *dietRecordFoodRepository) BatchDeleteWithTx(tx *gorm.DB, ids []int64) error {
	if len(ids) == 0 {
		return nil
	}
	if err := tx.Where("id IN ?", ids).Delete(&entities.DietRecordFood{}).Error; err != nil {
		return errs.Wrap(errs.CodeDatabaseError, "failed to batch delete diet record foods with tx", err)
	}
	return nil
}

// BatchDeleteByDietRecordIDWithTx 在事务中根据饮食记录ID批量删除食物明细
func (r *dietRecordFoodRepository) BatchDeleteByDietRecordIDWithTx(tx *gorm.DB, dietRecordID int64) error {
	if err := tx.Where("diet_record_id = ?", dietRecordID).Delete(&entities.DietRecordFood{}).Error; err != nil {
		return errs.Wrap(errs.CodeDatabaseError, "failed to batch delete diet record foods with tx by diet record id", err)
	}
	return nil
}
