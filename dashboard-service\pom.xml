<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.example</groupId>
        <artifactId>spring-boot-dubbo-demo</artifactId>
        <version>1.0.0</version>
    </parent>

    <groupId>com.example</groupId>
    <artifactId>dashboard-service</artifactId>
    <packaging>jar</packaging>

    <name>dashboard-service</name>
    <url>http://maven.apache.org</url>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <!-- Spring Boot Web Starter -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>

        <!-- Dubbo Spring Boot Starter -->
        <dependency>
            <groupId>org.apache.dubbo</groupId>
            <artifactId>dubbo-spring-boot-starter</artifactId>
        </dependency>

        <!-- Dubbo Zookeeper 依赖 -->
        <dependency>
            <groupId>org.apache.dubbo</groupId>
            <artifactId>dubbo-dependencies-zookeeper</artifactId>
            <type>pom</type>
            <exclusions>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-reload4j</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- 依赖 shared-kernel 模块 -->
        <dependency>
            <groupId>com.example</groupId>
            <artifactId>shared-kernel</artifactId>
        </dependency>

        <!-- 依赖所有 api-contracts 模块 -->
        <dependency>
            <groupId>com.example</groupId>
            <artifactId>dashboard-api-contracts</artifactId>
        </dependency>

        <dependency>
            <groupId>com.example</groupId>
            <artifactId>user-api-contracts</artifactId>
        </dependency>

        <dependency>
            <groupId>com.example</groupId>
            <artifactId>diet-api-contracts</artifactId>
        </dependency>

        <dependency>
            <groupId>com.example</groupId>
            <artifactId>nutrition-api-contracts</artifactId>
        </dependency>
    </dependencies>
</project>
