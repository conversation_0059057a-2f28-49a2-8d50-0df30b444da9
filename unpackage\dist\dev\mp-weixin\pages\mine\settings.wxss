@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.settings {
  min-height: 100vh;
  background-color: #f8f8f8;
  padding: 20rpx;
}
.section {
  background-color: #ffffff;
  border-radius: 10rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
}
.section .section-header {
  margin-bottom: 20rpx;
}
.section .section-header .section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}
.setting-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}
.setting-item:last-child {
  border-bottom: none;
}
.setting-item .setting-label {
  font-size: 28rpx;
  color: #333;
}
.setting-item .setting-action {
  display: flex;
  align-items: center;
}
.setting-item .setting-action text {
  font-size: 28rpx;
  color: #999;
  margin-right: 10rpx;
}
.setting-item .setting-action image {
  width: 24rpx;
  height: 24rpx;
}
.save-btn {
  background-color: #4CAF50;
  border-radius: 10rpx;
  padding: 30rpx;
  text-align: center;
  margin-top: 60rpx;
}
.save-btn text {
  font-size: 32rpx;
  color: #ffffff;
}

