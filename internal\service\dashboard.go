package service

import (
	"context"
	v1 "shikeyinxiang/api/v1"
)

// IDashboardService 仪表盘服务接口，定义了所有与管理员仪表盘相关的业务能力
type IDashboardService interface {
	// GetDashboardStats 获取管理员仪表盘统计数据
	// 包括：总用户数、今日饮食记录数、营养达标率、推荐准确率等
	GetDashboardStats(ctx context.Context, req *v1.DashboardStatsRequest) (*v1.DashboardStatsResponse, error)

	// GetNutritionTrend 获取用户营养摄入趋势数据
	// 用于管理员仪表盘展示所有用户的平均营养摄入趋势
	GetNutritionTrend(ctx context.Context, req *v1.NutritionTrendRequest) (*v1.NutritionTrendResponse, error)

	// GetLatestDietRecords 获取最新饮食记录列表
	// 用于管理员仪表盘展示所有用户的最新饮食记录
	GetLatestDietRecords(ctx context.Context, req *v1.DietRecordQueryRequest) (*v1.DietRecordListResponse, error)

	// GetDietRecordDetail 获取饮食记录详情
	GetDietRecordDetail(ctx context.Context, recordID int64) (*v1.DietRecordResponse, error)

	// GetPopularFoods 获取热门食物统计
	GetPopularFoods(ctx context.Context, req *v1.PopularFoodsRequest) ([]map[string]interface{}, error)
}
