{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/HBuilderProjects/springboot-dubbo-front-wechat/pages/mine/settings.vue?83d6", "webpack:///D:/HBuilderProjects/springboot-dubbo-front-wechat/pages/mine/settings.vue?2394", "webpack:///D:/HBuilderProjects/springboot-dubbo-front-wechat/pages/mine/settings.vue?9375", "webpack:///D:/HBuilderProjects/springboot-dubbo-front-wechat/pages/mine/settings.vue?fd5a", "uni-app:///pages/mine/settings.vue", "webpack:///D:/HBuilderProjects/springboot-dubbo-front-wechat/pages/mine/settings.vue?6aa1", "webpack:///D:/HBuilderProjects/springboot-dubbo-front-wechat/pages/mine/settings.vue?a291"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "settings", "dailyReminder", "nutritionReminder", "waterReminder", "allowDataAnalysis", "allowPersonalization", "cacheSize", "onLoad", "methods", "updateUserSettings", "getSettings", "setTimeout", "onSwitchChange", "clearCache", "uni", "title", "content", "success", "icon", "checkUpdate", "showCancel", "saveSettings"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,iBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAqH;AACrH;AAC4D;AACL;AACc;;;AAGrE;AACmK;AACnK,gBAAgB,6KAAU;AAC1B,EAAE,8EAAM;AACR,EAAE,mFAAM;AACR,EAAE,4FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,uFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAqmB,CAAgB,knBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;ACoEznB;AAAA;AAAA;AAAA,eAEA;EACAC;IACA;MACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;MACAC;IACA;EACA;EACAC;IACA;EACA;EACAC,yCACA;IACAC;EACA;IACAC;MAAA;MACA;MACA;MACAC;QACA;UACAV;UACAC;UACAC;UACAC;UACAC;QACA;MACA;IACA;IACAO;MACA;IACA;IACAC;MAAA;MACAC;QACAC;QACAC;QACAC;UACA;YACA;YACAH;cACAC;YACA;;YAEA;YACAJ;cACA;cACAG;cACA;cACAA;gBACAC;gBACAG;cACA;YACA;UACA;QACA;MACA;IACA;IACAC;MACAL;QACAC;MACA;MAEAJ;QACAG;QACAA;UACAC;UACAC;UACAI;QACA;MACA;IACA;IACAC;MACA;MACA;MAEAP;QACAC;QACAG;MACA;MAEAP;QACAG;MACA;IACA;EAAA;AAEA;AAAA,2B;;;;;;;;;;;;;AC/JA;AAAA;AAAA;AAAA;AAAgpC,CAAgB,imCAAG,EAAC,C;;;;;;;;;;;ACApqC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/mine/settings.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/mine/settings.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./settings.vue?vue&type=template&id=68887b36&\"\nvar renderjs\nimport script from \"./settings.vue?vue&type=script&lang=js&\"\nexport * from \"./settings.vue?vue&type=script&lang=js&\"\nimport style0 from \"./settings.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/mine/settings.vue\"\nexport default component.exports", "export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./settings.vue?vue&type=template&id=68887b36&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./settings.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./settings.vue?vue&type=script&lang=js&\"", "<template>\r\n  <view class=\"settings\">\r\n    <view class=\"section\">\r\n      <view class=\"section-header\">\r\n        <text class=\"section-title\">通知设置</text>\r\n      </view>\r\n\r\n      <view class=\"setting-item\">\r\n        <text class=\"setting-label\">每日打卡提醒</text>\r\n        <switch :checked=\"settings.dailyReminder\" color=\"#4CAF50\" @change=\"onSwitchChange($event, 'dailyReminder')\" />\r\n      </view>\r\n\r\n      <view class=\"setting-item\">\r\n        <text class=\"setting-label\">营养摄入提醒</text>\r\n        <switch :checked=\"settings.nutritionReminder\" color=\"#4CAF50\" @change=\"onSwitchChange($event, 'nutritionReminder')\" />\r\n      </view>\r\n\r\n      <view class=\"setting-item\">\r\n        <text class=\"setting-label\">饮水提醒</text>\r\n        <switch :checked=\"settings.waterReminder\" color=\"#4CAF50\" @change=\"onSwitchChange($event, 'waterReminder')\" />\r\n      </view>\r\n    </view>\r\n\r\n    <view class=\"section\">\r\n      <view class=\"section-header\">\r\n        <text class=\"section-title\">隐私设置</text>\r\n      </view>\r\n\r\n      <view class=\"setting-item\">\r\n        <text class=\"setting-label\">允许数据分析</text>\r\n        <switch :checked=\"settings.allowDataAnalysis\" color=\"#4CAF50\" @change=\"onSwitchChange($event, 'allowDataAnalysis')\" />\r\n      </view>\r\n\r\n      <view class=\"setting-item\">\r\n        <text class=\"setting-label\">允许推送个性化内容</text>\r\n        <switch :checked=\"settings.allowPersonalization\" color=\"#4CAF50\" @change=\"onSwitchChange($event, 'allowPersonalization')\" />\r\n      </view>\r\n    </view>\r\n\r\n    <view class=\"section\">\r\n      <view class=\"section-header\">\r\n        <text class=\"section-title\">应用设置</text>\r\n      </view>\r\n\r\n      <view class=\"setting-item\">\r\n        <text class=\"setting-label\">清除缓存</text>\r\n        <view class=\"setting-action\" @click=\"clearCache\">\r\n          <text>{{ cacheSize }}</text>\r\n          <image src=\"/static/icons/arrow-right.png\"></image>\r\n        </view>\r\n      </view>\r\n\r\n      <view class=\"setting-item\">\r\n        <text class=\"setting-label\">检查更新</text>\r\n        <view class=\"setting-action\" @click=\"checkUpdate\">\r\n          <text>当前版本 v1.0.0</text>\r\n          <image src=\"/static/icons/arrow-right.png\"></image>\r\n        </view>\r\n      </view>\r\n    </view>\r\n\r\n    <view class=\"save-btn\" @click=\"saveSettings\">\r\n      <text>保存设置</text>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nimport { mapActions } from 'vuex'\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      settings: {\r\n        dailyReminder: true,\r\n        nutritionReminder: true,\r\n        waterReminder: false,\r\n        allowDataAnalysis: true,\r\n        allowPersonalization: true\r\n      },\r\n      cacheSize: '2.5MB'\r\n    }\r\n  },\r\n  onLoad() {\r\n    this.getSettings()\r\n  },\r\n  methods: {\r\n    ...mapActions({\r\n      updateUserSettings: 'user/updateUserSettings'\r\n    }),\r\n    getSettings() {\r\n      // 实际项目中应该从后端获取设置\r\n      // 这里使用模拟数据\r\n      setTimeout(() => {\r\n        this.settings = {\r\n          dailyReminder: true,\r\n          nutritionReminder: true,\r\n          waterReminder: false,\r\n          allowDataAnalysis: true,\r\n          allowPersonalization: true\r\n        }\r\n      }, 500)\r\n    },\r\n    onSwitchChange(e, key) {\r\n      this.settings[key] = e.detail.value\r\n    },\r\n    clearCache() {\r\n      uni.showModal({\r\n        title: '提示',\r\n        content: '确定要清除缓存吗？',\r\n        success: res => {\r\n          if (res.confirm) {\r\n            // 显示加载中\r\n            uni.showLoading({\r\n              title: '清除中...'\r\n            })\r\n\r\n            // 简化的缓存清理：主要清理小程序自身的缓存\r\n            setTimeout(() => {\r\n              // 隐藏加载\r\n              uni.hideLoading()\r\n              this.cacheSize = '0KB'\r\n              uni.showToast({\r\n                title: '清除成功',\r\n                icon: 'success'\r\n              })\r\n            }, 1000)\r\n          }\r\n        }\r\n      })\r\n    },\r\n    checkUpdate() {\r\n      uni.showLoading({\r\n        title: '检查中...'\r\n      })\r\n\r\n      setTimeout(() => {\r\n        uni.hideLoading()\r\n        uni.showModal({\r\n          title: '检查更新',\r\n          content: '当前已是最新版本',\r\n          showCancel: false\r\n        })\r\n      }, 1000)\r\n    },\r\n    saveSettings() {\r\n      // 调用 action 更新设置\r\n      this.updateUserSettings(this.settings)\r\n\r\n      uni.showToast({\r\n        title: '保存成功',\r\n        icon: 'success'\r\n      })\r\n\r\n      setTimeout(() => {\r\n        uni.navigateBack()\r\n      }, 1500)\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.settings {\r\n  min-height: 100vh;\r\n  background-color: #f8f8f8;\r\n  padding: 20rpx;\r\n}\r\n\r\n.section {\r\n  background-color: #ffffff;\r\n  border-radius: 10rpx;\r\n  padding: 30rpx;\r\n  margin-bottom: 20rpx;\r\n\r\n  .section-header {\r\n    margin-bottom: 20rpx;\r\n\r\n    .section-title {\r\n      font-size: 32rpx;\r\n      font-weight: bold;\r\n      color: #333;\r\n    }\r\n  }\r\n}\r\n\r\n.setting-item {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 20rpx 0;\r\n  border-bottom: 1rpx solid #f0f0f0;\r\n\r\n  &:last-child {\r\n    border-bottom: none;\r\n  }\r\n\r\n  .setting-label {\r\n    font-size: 28rpx;\r\n    color: #333;\r\n  }\r\n\r\n  .setting-action {\r\n    display: flex;\r\n    align-items: center;\r\n\r\n    text {\r\n      font-size: 28rpx;\r\n      color: #999;\r\n      margin-right: 10rpx;\r\n    }\r\n\r\n    image {\r\n      width: 24rpx;\r\n      height: 24rpx;\r\n    }\r\n  }\r\n}\r\n\r\n.save-btn {\r\n  background-color: #4CAF50;\r\n  border-radius: 10rpx;\r\n  padding: 30rpx;\r\n  text-align: center;\r\n  margin-top: 60rpx;\r\n\r\n  text {\r\n    font-size: 32rpx;\r\n    color: #ffffff;\r\n  }\r\n}\r\n</style>", "import mod from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./settings.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./settings.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1751160935261\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}