package food

import (
	"context"
	"errors"
	"fmt"
	"shikeyinxiang/internal/repositories/food"

	v1 "shikeyinxiang/api/v1"
	"shikeyinxiang/internal/entities"
	"shikeyinxiang/internal/pkg/errs"
	"shikeyinxiang/internal/service"
)

// foodCategoryLogic 食物分类业务逻辑实现
type foodCategoryLogic struct {
	categoryRepo food.IFoodCategoryRepo
	foodRepo     food.IFoodRepo
}

// NewFoodCategoryLogic 创建食物分类业务逻辑实例
func NewFoodCategoryLogic(categoryRepo food.IFoodCategoryRepo, foodRepo food.IFoodRepo) service.IFoodCategoryService {
	return &foodCategoryLogic{
		categoryRepo: categoryRepo,
		foodRepo:     foodRepo,
	}
}

// 确保 foodCategoryLogic 实现了 IFoodCategoryService 接口
var _ service.IFoodCategoryService = &foodCategoryLogic{}

// CreateCategory 创建食物分类
func (f *foodCategoryLogic) CreateCategory(ctx context.Context, req *v1.FoodCategoryCreateReq) (*v1.FoodCategoryResponse, error) {
	// 参数验证
	if req.Name == "" {
		return nil, errs.New(errs.CodeSystemInvalidParams, "name is required")
	}

	// 检查分类名称是否已存在
	exists, err := f.categoryRepo.ExistsByName(req.Name)
	if err != nil {
		// 透传 Repository 层的 CodeError
		var codeErr *errs.CodeError
		if errors.As(err, &codeErr) {
			return nil, err
		}
		return nil, errs.Wrap(errs.CodeSystemInvalidParams, "failed to check category name exists", err)
	}
	if exists {
		return nil, errs.New(errs.CodeFoodAlreadyExists, fmt.Sprintf("category name %s already exists", req.Name))
	}

	// 设置排序顺序
	sortOrder := req.SortOrder
	if sortOrder == 0 {
		// 如果没有指定排序顺序，设置为最大值+1
		maxSortOrder, err := f.categoryRepo.GetMaxSortOrder()
		if err != nil {
			// 透传 Repository 层的 CodeError
			var codeErr *errs.CodeError
			if errors.As(err, &codeErr) {
				return nil, err
			}
			return nil, errs.Wrap(errs.CodeSystemInvalidParams, "failed to get max sort order", err)
		}
		sortOrder = maxSortOrder + 1
	}

	// 创建分类实体
	category := entities.NewFoodCategory(req.Name)
	if req.Description != nil {
		category.SetDescription(*req.Description)
	}
	if req.Color != nil {
		category.SetColor(*req.Color)
	}
	category.SetSortOrder(sortOrder)

	// 保存分类
	if err := f.categoryRepo.Create(category); err != nil {
		// 透传 Repository 层的 CodeError
		var codeErr *errs.CodeError
		if errors.As(err, &codeErr) {
			return nil, err
		}
		return nil, errs.Wrap(errs.CodeSystemInvalidParams, "failed to create category", err)
	}

	return f.convertToCategoryResponse(category), nil
}

// GetCategory 获取食物分类详情
func (f *foodCategoryLogic) GetCategory(ctx context.Context, id int) (*v1.FoodCategoryResponse, error) {
	if id <= 0 {
		return nil, errs.New(errs.CodeSystemInvalidParams, "id must be positive")
	}

	category, err := f.categoryRepo.GetByID(id)
	if err != nil {
		// 透传 Repository 层的 CodeError
		var codeErr *errs.CodeError
		if errors.As(err, &codeErr) {
			return nil, err
		}
		return nil, errs.Wrap(errs.CodeFoodCategoryNotFound, "failed to get category", err)
	}

	return f.convertToCategoryResponse(category), nil
}

// UpdateCategory 更新食物分类
func (f *foodCategoryLogic) UpdateCategory(ctx context.Context, req *v1.FoodCategoryUpdateReq) (*v1.FoodCategoryResponse, error) {
	// 参数验证
	if req.ID <= 0 {
		return nil, errs.New(errs.CodeSystemInvalidParams, "id must be positive")
	}
	if req.Name == "" {
		return nil, errs.New(errs.CodeSystemInvalidParams, "name is required")
	}

	// 验证分类是否存在
	category, err := f.categoryRepo.GetByID(req.ID)
	if err != nil {
		// 透传 Repository 层的 CodeError
		var codeErr *errs.CodeError
		if errors.As(err, &codeErr) {
			return nil, err
		}
		return nil, errs.Wrap(errs.CodeFoodCategoryNotFound, "failed to get category for update", err)
	}

	// 检查分类名称是否已存在（排除自身）
	exists, err := f.categoryRepo.ExistsByNameExcludeID(req.Name, req.ID)
	if err != nil {
		// 透传 Repository 层的 CodeError
		var codeErr *errs.CodeError
		if errors.As(err, &codeErr) {
			return nil, err
		}
		return nil, errs.Wrap(errs.CodeSystemInvalidParams, "failed to check category name exists", err)
	}
	if exists {
		return nil, errs.New(errs.CodeFoodAlreadyExists, fmt.Sprintf("category name %s already exists", req.Name))
	}

	// 更新分类信息
	category.UpdateInfo(req.Name, req.Description, req.Color, req.SortOrder)

	// 保存更新
	if err := f.categoryRepo.Update(category); err != nil {
		// 透传 Repository 层的 CodeError
		var codeErr *errs.CodeError
		if errors.As(err, &codeErr) {
			return nil, err
		}
		return nil, errs.Wrap(errs.CodeSystemInvalidParams, "failed to update category", err)
	}

	return f.convertToCategoryResponse(category), nil
}

// DeleteCategory 删除食物分类
func (f *foodCategoryLogic) DeleteCategory(ctx context.Context, id int) error {
	if id <= 0 {
		return errs.New(errs.CodeSystemInvalidParams, "id must be positive")
	}

	// 验证分类是否存在
	if _, err := f.categoryRepo.GetByID(id); err != nil {
		// 透传 Repository 层的 CodeError
		var codeErr *errs.CodeError
		if errors.As(err, &codeErr) {
			return err
		}
		return errs.Wrap(errs.CodeFoodCategoryNotFound, "failed to get category for delete", err)
	}

	// 检查是否有食物正在使用该分类
	count, err := f.foodRepo.CountByCategory(id)
	if err != nil {
		// 透传 Repository 层的 CodeError
		var codeErr *errs.CodeError
		if errors.As(err, &codeErr) {
			return err
		}
		return errs.Wrap(errs.CodeSystemInvalidParams, "failed to count foods by category", err)
	}
	if count > 0 {
		return errs.New(errs.CodeSystemInvalidParams, fmt.Sprintf("category %d is in use by %d foods, cannot delete", id, count))
	}

	// 删除分类
	if err := f.categoryRepo.Delete(id); err != nil {
		// 透传 Repository 层的 CodeError
		var codeErr *errs.CodeError
		if errors.As(err, &codeErr) {
			return err
		}
		return errs.Wrap(errs.CodeSystemInvalidParams, "failed to delete category", err)
	}

	return nil
}

// ListCategories 获取食物分类列表
func (f *foodCategoryLogic) ListCategories(ctx context.Context, req *v1.FoodCategoryQueryReq) (*v1.FoodCategoryListResponse, error) {
	// 设置默认分页参数
	page := req.Current
	if page <= 0 {
		page = 1
	}
	size := req.Size
	if size <= 0 {
		size = 10
	}

	offset := (page - 1) * size

	var categories []*entities.FoodCategory
	var total int64
	var err error

	// 根据查询条件选择不同的查询方法
	if req.Keyword != "" {
		categories, total, err = f.categoryRepo.Search(req.Keyword, offset, size)
	} else {
		categories, total, err = f.categoryRepo.List(offset, size)
	}

	if err != nil {
		// 透传 Repository 层的 CodeError
		var codeErr *errs.CodeError
		if errors.As(err, &codeErr) {
			return nil, err
		}
		return nil, errs.Wrap(errs.CodeSystemInvalidParams, "failed to list categories", err)
	}

	// 转换为响应格式
	records := make([]*v1.FoodCategoryResponse, len(categories))
	for i, category := range categories {
		records[i] = f.convertToCategoryResponse(category)
	}

	return &v1.FoodCategoryListResponse{
		Total:   total,
		Records: records,
		Current: page,
		Size:    size,
	}, nil
}

// GetAllCategories 获取所有食物分类（按排序顺序）
func (f *foodCategoryLogic) GetAllCategories(ctx context.Context) ([]*v1.FoodCategoryResponse, error) {
	categories, err := f.categoryRepo.GetAllOrderBySortOrder()
	if err != nil {
		// 透传 Repository 层的 CodeError
		var codeErr *errs.CodeError
		if errors.As(err, &codeErr) {
			return nil, err
		}
		return nil, errs.Wrap(errs.CodeSystemInvalidParams, "failed to get all categories", err)
	}

	// 转换为响应格式
	responses := make([]*v1.FoodCategoryResponse, len(categories))
	for i, category := range categories {
		responses[i] = f.convertToCategoryResponse(category)
	}

	return responses, nil
}

// UpdateCategorySortOrder 更新分类排序顺序
func (f *foodCategoryLogic) UpdateCategorySortOrder(ctx context.Context, id int, sortOrder int) error {
	if id <= 0 {
		return errs.New(errs.CodeSystemInvalidParams, "id must be positive")
	}
	if sortOrder < 0 {
		return errs.New(errs.CodeSystemInvalidParams, "sortOrder cannot be negative")
	}

	// 验证分类是否存在
	if _, err := f.categoryRepo.GetByID(id); err != nil {
		// 透传 Repository 层的 CodeError
		var codeErr *errs.CodeError
		if errors.As(err, &codeErr) {
			return err
		}
		return errs.Wrap(errs.CodeFoodCategoryNotFound, "failed to get category for sort order update", err)
	}

	// 更新排序顺序
	if err := f.categoryRepo.UpdateSortOrder(id, sortOrder); err != nil {
		// 透传 Repository 层的 CodeError
		var codeErr *errs.CodeError
		if errors.As(err, &codeErr) {
			return err
		}
		return errs.Wrap(errs.CodeSystemInvalidParams, "failed to update category sort order", err)
	}

	return nil
}

// convertToCategoryResponse 转换实体为响应格式
func (f *foodCategoryLogic) convertToCategoryResponse(category *entities.FoodCategory) *v1.FoodCategoryResponse {
	// 计算该分类下的食物数量（与Java版本保持一致）
	foodCount, err := f.foodRepo.CountByCategory(category.ID)
	if err != nil {
		// 如果计算失败，记录错误但不影响主流程，设置为0
		foodCount = 0
	}

	return &v1.FoodCategoryResponse{
		ID:          category.ID,
		Name:        category.Name,
		Description: category.Description,
		Color:       category.Color,
		SortOrder:   category.SortOrder,
		FoodCount:   int(foodCount), // 添加食物数量字段
		CreatedAt:   category.CreatedAt,
		UpdatedAt:   category.UpdatedAt,
	}
}
