(global["webpackJsonp"] = global["webpackJsonp"] || []).push([["uni_modules/qiun-data-charts/components/qiun-data-charts/qiun-data-charts"],{

/***/ 135:
/*!***************************************************************************************************************************************!*\
  !*** D:/HBuilderProjects/springboot-dubbo-front-wechat/uni_modules/qiun-data-charts/components/qiun-data-charts/qiun-data-charts.vue ***!
  \***************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _qiun_data_charts_vue_vue_type_template_id_fe947b98_scoped_true_filter_modules_eyJyZGNoYXJ0cyI6eyJ0eXBlIjoicmVuZGVyanMiLCJjb250ZW50IjoiIiwic3RhcnQiOjM4ODkwLCJhdHRycyI6eyJtb2R1bGUiOiJyZGNoYXJ0cyIsImxhbmciOiJqcyJ9LCJlbmQiOjU0NjkyfX0_3D___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./qiun-data-charts.vue?vue&type=template&id=fe947b98&scoped=true&filter-modules=eyJyZGNoYXJ0cyI6eyJ0eXBlIjoicmVuZGVyanMiLCJjb250ZW50IjoiIiwic3RhcnQiOjM4ODkwLCJhdHRycyI6eyJtb2R1bGUiOiJyZGNoYXJ0cyIsImxhbmciOiJqcyJ9LCJlbmQiOjU0NjkyfX0%3D& */ 136);
/* harmony import */ var _qiun_data_charts_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./qiun-data-charts.vue?vue&type=script&lang=js& */ 138);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _qiun_data_charts_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _qiun_data_charts_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__[key]; }) }(__WEBPACK_IMPORT_KEY__));
/* harmony import */ var _qiun_data_charts_vue_vue_type_style_index_0_id_fe947b98_scoped_true_lang_css___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./qiun-data-charts.vue?vue&type=style&index=0&id=fe947b98&scoped=true&lang=css& */ 151);
/* harmony import */ var _HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js */ 47);

var renderjs





/* normalize component */

var component = Object(_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__["default"])(
  _qiun_data_charts_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__["default"],
  _qiun_data_charts_vue_vue_type_template_id_fe947b98_scoped_true_filter_modules_eyJyZGNoYXJ0cyI6eyJ0eXBlIjoicmVuZGVyanMiLCJjb250ZW50IjoiIiwic3RhcnQiOjM4ODkwLCJhdHRycyI6eyJtb2R1bGUiOiJyZGNoYXJ0cyIsImxhbmciOiJqcyJ9LCJlbmQiOjU0NjkyfX0_3D___WEBPACK_IMPORTED_MODULE_0__["render"],
  _qiun_data_charts_vue_vue_type_template_id_fe947b98_scoped_true_filter_modules_eyJyZGNoYXJ0cyI6eyJ0eXBlIjoicmVuZGVyanMiLCJjb250ZW50IjoiIiwic3RhcnQiOjM4ODkwLCJhdHRycyI6eyJtb2R1bGUiOiJyZGNoYXJ0cyIsImxhbmciOiJqcyJ9LCJlbmQiOjU0NjkyfX0_3D___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"],
  false,
  null,
  "fe947b98",
  null,
  false,
  _qiun_data_charts_vue_vue_type_template_id_fe947b98_scoped_true_filter_modules_eyJyZGNoYXJ0cyI6eyJ0eXBlIjoicmVuZGVyanMiLCJjb250ZW50IjoiIiwic3RhcnQiOjM4ODkwLCJhdHRycyI6eyJtb2R1bGUiOiJyZGNoYXJ0cyIsImxhbmciOiJqcyJ9LCJlbmQiOjU0NjkyfX0_3D___WEBPACK_IMPORTED_MODULE_0__["components"],
  renderjs
)

component.options.__file = "uni_modules/qiun-data-charts/components/qiun-data-charts/qiun-data-charts.vue"
/* harmony default export */ __webpack_exports__["default"] = (component.exports);

/***/ }),

/***/ 136:
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** D:/HBuilderProjects/springboot-dubbo-front-wechat/uni_modules/qiun-data-charts/components/qiun-data-charts/qiun-data-charts.vue?vue&type=template&id=fe947b98&scoped=true&filter-modules=eyJyZGNoYXJ0cyI6eyJ0eXBlIjoicmVuZGVyanMiLCJjb250ZW50IjoiIiwic3RhcnQiOjM4ODkwLCJhdHRycyI6eyJtb2R1bGUiOiJyZGNoYXJ0cyIsImxhbmciOiJqcyJ9LCJlbmQiOjU0NjkyfX0%3D& ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_qiun_data_charts_vue_vue_type_template_id_fe947b98_scoped_true_filter_modules_eyJyZGNoYXJ0cyI6eyJ0eXBlIjoicmVuZGVyanMiLCJjb250ZW50IjoiIiwic3RhcnQiOjM4ODkwLCJhdHRycyI6eyJtb2R1bGUiOiJyZGNoYXJ0cyIsImxhbmciOiJqcyJ9LCJlbmQiOjU0NjkyfX0_3D___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./qiun-data-charts.vue?vue&type=template&id=fe947b98&scoped=true&filter-modules=eyJyZGNoYXJ0cyI6eyJ0eXBlIjoicmVuZGVyanMiLCJjb250ZW50IjoiIiwic3RhcnQiOjM4ODkwLCJhdHRycyI6eyJtb2R1bGUiOiJyZGNoYXJ0cyIsImxhbmciOiJqcyJ9LCJlbmQiOjU0NjkyfX0%3D& */ 137);
/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "render", function() { return _HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_qiun_data_charts_vue_vue_type_template_id_fe947b98_scoped_true_filter_modules_eyJyZGNoYXJ0cyI6eyJ0eXBlIjoicmVuZGVyanMiLCJjb250ZW50IjoiIiwic3RhcnQiOjM4ODkwLCJhdHRycyI6eyJtb2R1bGUiOiJyZGNoYXJ0cyIsImxhbmciOiJqcyJ9LCJlbmQiOjU0NjkyfX0_3D___WEBPACK_IMPORTED_MODULE_0__["render"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return _HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_qiun_data_charts_vue_vue_type_template_id_fe947b98_scoped_true_filter_modules_eyJyZGNoYXJ0cyI6eyJ0eXBlIjoicmVuZGVyanMiLCJjb250ZW50IjoiIiwic3RhcnQiOjM4ODkwLCJhdHRycyI6eyJtb2R1bGUiOiJyZGNoYXJ0cyIsImxhbmciOiJqcyJ9LCJlbmQiOjU0NjkyfX0_3D___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return _HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_qiun_data_charts_vue_vue_type_template_id_fe947b98_scoped_true_filter_modules_eyJyZGNoYXJ0cyI6eyJ0eXBlIjoicmVuZGVyanMiLCJjb250ZW50IjoiIiwic3RhcnQiOjM4ODkwLCJhdHRycyI6eyJtb2R1bGUiOiJyZGNoYXJ0cyIsImxhbmciOiJqcyJ9LCJlbmQiOjU0NjkyfX0_3D___WEBPACK_IMPORTED_MODULE_0__["recyclableRender"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "components", function() { return _HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_qiun_data_charts_vue_vue_type_template_id_fe947b98_scoped_true_filter_modules_eyJyZGNoYXJ0cyI6eyJ0eXBlIjoicmVuZGVyanMiLCJjb250ZW50IjoiIiwic3RhcnQiOjM4ODkwLCJhdHRycyI6eyJtb2R1bGUiOiJyZGNoYXJ0cyIsImxhbmciOiJqcyJ9LCJlbmQiOjU0NjkyfX0_3D___WEBPACK_IMPORTED_MODULE_0__["components"]; });



/***/ }),

/***/ 137:
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/HBuilderProjects/springboot-dubbo-front-wechat/uni_modules/qiun-data-charts/components/qiun-data-charts/qiun-data-charts.vue?vue&type=template&id=fe947b98&scoped=true&filter-modules=eyJyZGNoYXJ0cyI6eyJ0eXBlIjoicmVuZGVyanMiLCJjb250ZW50IjoiIiwic3RhcnQiOjM4ODkwLCJhdHRycyI6eyJtb2R1bGUiOiJyZGNoYXJ0cyIsImxhbmciOiJqcyJ9LCJlbmQiOjU0NjkyfX0%3D& ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "render", function() { return render; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return staticRenderFns; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return recyclableRender; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "components", function() { return components; });
var components
try {
  components = {
    qiunLoading: function () {
      return __webpack_require__.e(/*! import() | uni_modules/qiun-data-charts/components/qiun-loading/qiun-loading */ "uni_modules/qiun-data-charts/components/qiun-loading/qiun-loading").then(__webpack_require__.bind(null, /*! @/uni_modules/qiun-data-charts/components/qiun-loading/qiun-loading.vue */ 162))
    },
    qiunError: function () {
      return __webpack_require__.e(/*! import() | uni_modules/qiun-data-charts/components/qiun-error/qiun-error */ "uni_modules/qiun-data-charts/components/qiun-error/qiun-error").then(__webpack_require__.bind(null, /*! @/uni_modules/qiun-data-charts/components/qiun-error/qiun-error.vue */ 167))
    },
  }
} catch (e) {
  if (
    e.message.indexOf("Cannot find module") !== -1 &&
    e.message.indexOf(".vue") !== -1
  ) {
    console.error(e.message)
    console.error("1. 排查组件名称拼写是否正确")
    console.error(
      "2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom"
    )
    console.error(
      "3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件"
    )
  } else {
    throw e
  }
}
var render = function () {
  var _vm = this
  var _h = _vm.$createElement
  var _c = _vm._self._c || _h
}
var recyclableRender = false
var staticRenderFns = []
render._withStripped = true



/***/ }),

/***/ 138:
/*!****************************************************************************************************************************************************************!*\
  !*** D:/HBuilderProjects/springboot-dubbo-front-wechat/uni_modules/qiun-data-charts/components/qiun-data-charts/qiun-data-charts.vue?vue&type=script&lang=js& ***!
  \****************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_qiun_data_charts_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./qiun-data-charts.vue?vue&type=script&lang=js& */ 139);
/* harmony import */ var _HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_qiun_data_charts_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_qiun_data_charts_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_qiun_data_charts_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_qiun_data_charts_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_qiun_data_charts_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 139:
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/babel-loader/lib!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/HBuilderProjects/springboot-dubbo-front-wechat/uni_modules/qiun-data-charts/components/qiun-data-charts/qiun-data-charts.vue?vue&type=script&lang=js& ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(uniCloud, uni) {

var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ 4);
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _typeof2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/typeof */ 13));
var _uCharts = _interopRequireDefault(__webpack_require__(/*! ../../js_sdk/u-charts/u-charts.js */ 149));
var _configUcharts = _interopRequireDefault(__webpack_require__(/*! ../../js_sdk/u-charts/config-ucharts.js */ 150));
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//

function deepCloneAssign() {
  var origin = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};
  for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {
    args[_key - 1] = arguments[_key];
  }
  for (var i in args) {
    for (var key in args[i]) {
      if (args[i].hasOwnProperty(key)) {
        origin[key] = args[i][key] && (0, _typeof2.default)(args[i][key]) === 'object' ? deepCloneAssign(Array.isArray(args[i][key]) ? [] : {}, origin[key], args[i][key]) : args[i][key];
      }
    }
  }
  return origin;
}
function formatterAssign(args, formatter) {
  for (var key in args) {
    if (args.hasOwnProperty(key) && args[key] !== null && (0, _typeof2.default)(args[key]) === 'object') {
      formatterAssign(args[key], formatter);
    } else if (key === 'format' && typeof args[key] === 'string') {
      args['formatter'] = formatter[args[key]] ? formatter[args[key]] : undefined;
    }
  }
  return args;
}

// 时间转换函数，为了匹配uniClinetDB读取出的时间与categories不同
function getFormatDate(date) {
  var seperator = "-";
  var year = date.getFullYear();
  var month = date.getMonth() + 1;
  var strDate = date.getDate();
  if (month >= 1 && month <= 9) {
    month = "0" + month;
  }
  if (strDate >= 0 && strDate <= 9) {
    strDate = "0" + strDate;
  }
  var currentdate = year + seperator + month + seperator + strDate;
  return currentdate;
}
var lastMoveTime = null;
/**
 * 防抖
 *
 * @param { Function } fn 要执行的方法
 * @param { Number } wait  防抖多少毫秒
 *
 * 在 vue 中使用（注意：不能使用箭头函数，否则this指向不对，并且不能再次封装如：
 * move(){  // 错误调用方式
 *   debounce(function () {
 *   console.log(this.title);
 * }, 1000)}）;
 * 应该直接使用：// 正确调用方式
 * move: debounce(function () {
 *   console.log(this.title);
 * }, 1000)
 */
function debounce(fn, wait) {
  var timer = false;
  return function () {
    var _arguments = arguments,
      _this2 = this;
    clearTimeout(timer);
    timer && clearTimeout(timer);
    timer = setTimeout(function () {
      timer = false;
      fn.apply(_this2, _arguments); // 把参数传进去
    }, wait);
  };
}
var _default2 = {
  name: 'qiun-data-charts',
  mixins: [uniCloud.mixinDatacom],
  props: {
    type: {
      type: String,
      default: null
    },
    canvasId: {
      type: String,
      default: 'uchartsid'
    },
    canvas2d: {
      type: Boolean,
      default: false
    },
    background: {
      type: String,
      default: 'rgba(0,0,0,0)'
    },
    animation: {
      type: Boolean,
      default: true
    },
    chartData: {
      type: Object,
      default: function _default() {
        return {
          categories: [],
          series: []
        };
      }
    },
    opts: {
      type: Object,
      default: function _default() {
        return {};
      }
    },
    eopts: {
      type: Object,
      default: function _default() {
        return {};
      }
    },
    loadingType: {
      type: Number,
      default: 2
    },
    errorShow: {
      type: Boolean,
      default: true
    },
    errorReload: {
      type: Boolean,
      default: true
    },
    errorMessage: {
      type: String,
      default: null
    },
    inScrollView: {
      type: Boolean,
      default: false
    },
    reshow: {
      type: Boolean,
      default: false
    },
    reload: {
      type: Boolean,
      default: false
    },
    disableScroll: {
      type: Boolean,
      default: false
    },
    optsWatch: {
      type: Boolean,
      default: true
    },
    onzoom: {
      type: Boolean,
      default: false
    },
    ontap: {
      type: Boolean,
      default: true
    },
    ontouch: {
      type: Boolean,
      default: false
    },
    onmouse: {
      type: Boolean,
      default: true
    },
    onmovetip: {
      type: Boolean,
      default: false
    },
    echartsH5: {
      type: Boolean,
      default: false
    },
    echartsApp: {
      type: Boolean,
      default: false
    },
    tooltipShow: {
      type: Boolean,
      default: true
    },
    tooltipFormat: {
      type: String,
      default: undefined
    },
    tooltipCustom: {
      type: Object,
      default: undefined
    },
    startDate: {
      type: String,
      default: undefined
    },
    endDate: {
      type: String,
      default: undefined
    },
    textEnum: {
      type: Array,
      default: function _default() {
        return [];
      }
    },
    groupEnum: {
      type: Array,
      default: function _default() {
        return [];
      }
    },
    pageScrollTop: {
      type: Number,
      default: 0
    },
    directory: {
      type: String,
      default: '/'
    },
    tapLegend: {
      type: Boolean,
      default: true
    },
    menus: {
      type: Array,
      default: function _default() {
        return [];
      }
    }
  },
  data: function data() {
    return {
      cid: 'uchartsid',
      inWx: false,
      inAli: false,
      inTt: false,
      inBd: false,
      inH5: false,
      inApp: false,
      inWin: false,
      type2d: true,
      disScroll: false,
      openmouse: false,
      pixel: 1,
      cWidth: 375,
      cHeight: 250,
      showchart: false,
      echarts: false,
      echartsResize: {
        state: false
      },
      uchartsOpts: {},
      echartsOpts: {},
      drawData: {},
      lastDrawTime: null
    };
  },
  created: function created() {
    this.cid = this.canvasId;
    if (this.canvasId == 'uchartsid' || this.canvasId == '') {
      var t = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz';
      var len = t.length;
      var id = '';
      for (var i = 0; i < 32; i++) {
        id += t.charAt(Math.floor(Math.random() * len));
      }
      this.cid = id;
    }
    var systemInfo = uni.getSystemInfoSync();
    if (systemInfo.platform === 'windows' || systemInfo.platform === 'mac') {
      this.inWin = true;
    }
    this.inWx = true;
    if (this.canvas2d === false || systemInfo.platform === 'windows' || systemInfo.platform === 'mac') {
      this.type2d = false;
    } else {
      this.type2d = true;
      this.pixel = systemInfo.pixelRatio;
    }

    //非微信小程序端强制关闭canvas2d模式

    this.disScroll = this.disableScroll;
  },
  mounted: function mounted() {
    var _this3 = this;
    this.$nextTick(function () {
      _this3.beforeInit();
    });
    var time = this.inH5 ? 500 : 200;
    var _this = this;
    uni.onWindowResize(debounce(function (res) {
      if (_this.mixinDatacomLoading == true) {
        return;
      }
      var errmsg = _this.mixinDatacomErrorMessage;
      if (errmsg !== null && errmsg !== 'null' && errmsg !== '') {
        return;
      }
      if (_this.echarts) {
        _this.echartsResize.state = !_this.echartsResize.state;
      } else {
        _this.resizeHandler();
      }
    }, time));
  },
  destroyed: function destroyed() {
    if (this.echarts === true) {
      delete cfe.option[this.cid];
      delete cfe.instance[this.cid];
    } else {
      delete _configUcharts.default.option[this.cid];
      delete _configUcharts.default.instance[this.cid];
    }
    uni.offWindowResize(function () {});
  },
  watch: {
    chartDataProps: {
      handler: function handler(val, oldval) {
        if ((0, _typeof2.default)(val) === 'object') {
          if (JSON.stringify(val) !== JSON.stringify(oldval)) {
            this._clearChart();
            if (val.series && val.series.length > 0) {
              this.beforeInit();
            } else {
              this.mixinDatacomLoading = true;
              this.showchart = false;
              this.mixinDatacomErrorMessage = null;
            }
          }
        } else {
          this.mixinDatacomLoading = false;
          this._clearChart();
          this.showchart = false;
          this.mixinDatacomErrorMessage = '参数错误：chartData数据类型错误';
        }
      },
      immediate: false,
      deep: true
    },
    localdata: {
      handler: function handler(val, oldval) {
        if (JSON.stringify(val) !== JSON.stringify(oldval)) {
          if (val.length > 0) {
            this.beforeInit();
          } else {
            this.mixinDatacomLoading = true;
            this._clearChart();
            this.showchart = false;
            this.mixinDatacomErrorMessage = null;
          }
        }
      },
      immediate: false,
      deep: true
    },
    optsProps: {
      handler: function handler(val, oldval) {
        if ((0, _typeof2.default)(val) === 'object') {
          if (JSON.stringify(val) !== JSON.stringify(oldval) && this.echarts === false && this.optsWatch == true) {
            this.checkData(this.drawData);
          }
        } else {
          this.mixinDatacomLoading = false;
          this._clearChart();
          this.showchart = false;
          this.mixinDatacomErrorMessage = '参数错误：opts数据类型错误';
        }
      },
      immediate: false,
      deep: true
    },
    eoptsProps: {
      handler: function handler(val, oldval) {
        if ((0, _typeof2.default)(val) === 'object') {
          if (JSON.stringify(val) !== JSON.stringify(oldval) && this.echarts === true) {
            this.checkData(this.drawData);
          }
        } else {
          this.mixinDatacomLoading = false;
          this.showchart = false;
          this.mixinDatacomErrorMessage = '参数错误：eopts数据类型错误';
        }
      },
      immediate: false,
      deep: true
    },
    reshow: function reshow(val, oldval) {
      var _this4 = this;
      if (val === true && this.mixinDatacomLoading === false) {
        setTimeout(function () {
          _this4.mixinDatacomErrorMessage = null;
          _this4.echartsResize.state = !_this4.echartsResize.state;
          _this4.checkData(_this4.drawData);
        }, 200);
      }
    },
    reload: function reload(val, oldval) {
      if (val === true) {
        this.showchart = false;
        this.mixinDatacomErrorMessage = null;
        this.reloading();
      }
    },
    mixinDatacomErrorMessage: function mixinDatacomErrorMessage(val, oldval) {
      if (val) {
        this.emitMsg({
          name: 'error',
          params: {
            type: "error",
            errorShow: this.errorShow,
            msg: val,
            id: this.cid
          }
        });
        if (this.errorShow) {
          console.log('[秋云图表组件]' + val);
        }
      }
    },
    errorMessage: function errorMessage(val, oldval) {
      if (val && this.errorShow && val !== null && val !== 'null' && val !== '') {
        this.showchart = false;
        this.mixinDatacomLoading = false;
        this.mixinDatacomErrorMessage = val;
      } else {
        this.showchart = false;
        this.mixinDatacomErrorMessage = null;
        this.reloading();
      }
    }
  },
  computed: {
    optsProps: function optsProps() {
      return JSON.parse(JSON.stringify(this.opts));
    },
    eoptsProps: function eoptsProps() {
      return JSON.parse(JSON.stringify(this.eopts));
    },
    chartDataProps: function chartDataProps() {
      return JSON.parse(JSON.stringify(this.chartData));
    }
  },
  methods: {
    beforeInit: function beforeInit() {
      this.mixinDatacomErrorMessage = null;
      if ((0, _typeof2.default)(this.chartData) === 'object' && this.chartData != null && this.chartData.series !== undefined && this.chartData.series.length > 0) {
        //拷贝一下chartData，为了opts变更后统一数据来源
        this.drawData = deepCloneAssign({}, this.chartData);
        this.mixinDatacomLoading = false;
        this.showchart = true;
        this.checkData(this.chartData);
      } else if (this.localdata.length > 0) {
        this.mixinDatacomLoading = false;
        this.showchart = true;
        this.localdataInit(this.localdata);
      } else if (this.collection !== '') {
        this.mixinDatacomLoading = false;
        this.getCloudData();
      } else {
        this.mixinDatacomLoading = true;
      }
    },
    localdataInit: function localdataInit(resdata) {
      //替换enum类型为正确的描述
      if (this.groupEnum.length > 0) {
        for (var i = 0; i < resdata.length; i++) {
          for (var j = 0; j < this.groupEnum.length; j++) {
            if (resdata[i].group === this.groupEnum[j].value) {
              resdata[i].group = this.groupEnum[j].text;
            }
          }
        }
      }
      if (this.textEnum.length > 0) {
        for (var _i = 0; _i < resdata.length; _i++) {
          for (var _j = 0; _j < this.textEnum.length; _j++) {
            if (resdata[_i].text === this.textEnum[_j].value) {
              resdata[_i].text = this.textEnum[_j].text;
            }
          }
        }
      }
      var needCategories = false;
      var tmpData = {
        categories: [],
        series: []
      };
      var tmpcategories = [];
      var tmpseries = [];
      //拼接categories
      if (this.echarts === true) {
        needCategories = cfe.categories.includes(this.type);
      } else {
        needCategories = _configUcharts.default.categories.includes(this.type);
      }
      if (needCategories === true) {
        //如果props中的chartData带有categories，则优先使用chartData的categories
        if (this.chartData && this.chartData.categories && this.chartData.categories.length > 0) {
          tmpcategories = this.chartData.categories;
        } else {
          //如果是日期类型的数据，不管是本地数据还是云数据，都按起止日期自动拼接categories
          if (this.startDate && this.endDate) {
            var idate = new Date(this.startDate);
            var edate = new Date(this.endDate);
            while (idate <= edate) {
              tmpcategories.push(getFormatDate(idate));
              idate = idate.setDate(idate.getDate() + 1);
              idate = new Date(idate);
            }
            //否则从结果中去重并拼接categories
          } else {
            var tempckey = {};
            resdata.map(function (item, index) {
              if (item.text != undefined && !tempckey[item.text]) {
                tmpcategories.push(item.text);
                tempckey[item.text] = true;
              }
            });
          }
        }
        tmpData.categories = tmpcategories;
      }
      //拼接series
      var tempskey = {};
      resdata.map(function (item, index) {
        if (item.group != undefined && !tempskey[item.group]) {
          tmpseries.push({
            name: item.group,
            data: []
          });
          tempskey[item.group] = true;
        }
      });
      //如果没有获取到分组名称(可能是带categories的数据，也可能是不带的饼图类)
      if (tmpseries.length == 0) {
        tmpseries = [{
          name: '默认分组',
          data: []
        }];
        //如果是需要categories的图表类型
        if (needCategories === true) {
          for (var _j2 = 0; _j2 < tmpcategories.length; _j2++) {
            var seriesdata = 0;
            for (var _i2 = 0; _i2 < resdata.length; _i2++) {
              if (resdata[_i2].text == tmpcategories[_j2]) {
                seriesdata = resdata[_i2].value;
              }
            }
            tmpseries[0].data.push(seriesdata);
          }
          //如果是饼图类的图表类型
        } else {
          for (var _i3 = 0; _i3 < resdata.length; _i3++) {
            tmpseries[0].data.push({
              "name": resdata[_i3].text,
              "value": resdata[_i3].value
            });
          }
        }
        //如果有分组名
      } else {
        for (var k = 0; k < tmpseries.length; k++) {
          //如果有categories
          if (tmpcategories.length > 0) {
            for (var _j3 = 0; _j3 < tmpcategories.length; _j3++) {
              var _seriesdata = 0;
              for (var _i4 = 0; _i4 < resdata.length; _i4++) {
                if (tmpseries[k].name == resdata[_i4].group && resdata[_i4].text == tmpcategories[_j3]) {
                  _seriesdata = resdata[_i4].value;
                }
              }
              tmpseries[k].data.push(_seriesdata);
            }
            //如果传了group而没有传text，即没有categories（正常情况下这种数据是不符合数据要求规范的）
          } else {
            for (var _i5 = 0; _i5 < resdata.length; _i5++) {
              if (tmpseries[k].name == resdata[_i5].group) {
                tmpseries[k].data.push(resdata[_i5].value);
              }
            }
          }
        }
      }
      tmpData.series = tmpseries;
      //拷贝一下chartData，为了opts变更后统一数据来源
      this.drawData = deepCloneAssign({}, tmpData);
      this.checkData(tmpData);
    },
    reloading: function reloading() {
      if (this.errorReload === false) {
        return;
      }
      this.showchart = false;
      this.mixinDatacomErrorMessage = null;
      if (this.collection !== '') {
        this.mixinDatacomLoading = false;
        this.onMixinDatacomPropsChange(true);
      } else {
        this.beforeInit();
      }
    },
    checkData: function checkData(anyData) {
      var _this5 = this;
      var cid = this.cid;
      //复位opts或eopts
      if (this.echarts === true) {
        cfe.option[cid] = deepCloneAssign({}, this.eopts);
        cfe.option[cid].id = cid;
        cfe.option[cid].type = this.type;
      } else {
        if (this.type && _configUcharts.default.type.includes(this.type)) {
          _configUcharts.default.option[cid] = deepCloneAssign({}, _configUcharts.default[this.type], this.opts);
          _configUcharts.default.option[cid].canvasId = cid;
        } else {
          this.mixinDatacomLoading = false;
          this.showchart = false;
          this.mixinDatacomErrorMessage = '参数错误：props参数中type类型不正确';
        }
      }
      //挂载categories和series
      var newData = deepCloneAssign({}, anyData);
      if (newData.series !== undefined && newData.series.length > 0) {
        this.mixinDatacomErrorMessage = null;
        if (this.echarts === true) {
          cfe.option[cid].chartData = newData;
          this.$nextTick(function () {
            _this5.init();
          });
        } else {
          _configUcharts.default.option[cid].categories = newData.categories;
          _configUcharts.default.option[cid].series = newData.series;
          this.$nextTick(function () {
            _this5.init();
          });
        }
      }
    },
    resizeHandler: function resizeHandler() {
      var _this6 = this;
      //渲染防抖
      var currTime = Date.now();
      var lastDrawTime = this.lastDrawTime ? this.lastDrawTime : currTime - 3000;
      var duration = currTime - lastDrawTime;
      if (duration < 1000) return;
      var chartdom = uni.createSelectorQuery().in(this).select('#ChartBoxId' + this.cid).boundingClientRect(function (data) {
        _this6.showchart = true;
        if (data.width > 0 && data.height > 0) {
          if (data.width !== _this6.cWidth || data.height !== _this6.cHeight) {
            _this6.checkData(_this6.drawData);
          }
        }
      }).exec();
    },
    getCloudData: function getCloudData() {
      var _this7 = this;
      if (this.mixinDatacomLoading == true) {
        return;
      }
      this.mixinDatacomLoading = true;
      this.mixinDatacomGet().then(function (res) {
        _this7.mixinDatacomResData = res.result.data;
        _this7.localdataInit(_this7.mixinDatacomResData);
      }).catch(function (err) {
        _this7.mixinDatacomLoading = false;
        _this7.showchart = false;
        _this7.mixinDatacomErrorMessage = '请求错误：' + err;
      });
    },
    onMixinDatacomPropsChange: function onMixinDatacomPropsChange(needReset, changed) {
      if (needReset == true && this.collection !== '') {
        this.showchart = false;
        this.mixinDatacomErrorMessage = null;
        this._clearChart();
        this.getCloudData();
      }
    },
    _clearChart: function _clearChart() {
      var cid = this.cid;
      if (this.echarts !== true && _configUcharts.default.option[cid] && _configUcharts.default.option[cid].context) {
        var ctx = _configUcharts.default.option[cid].context;
        if ((0, _typeof2.default)(ctx) === "object" && !!!_configUcharts.default.option[cid].update) {
          ctx.clearRect(0, 0, this.cWidth * this.pixel, this.cHeight * this.pixel);
          ctx.draw();
        }
      }
    },
    init: function init() {
      var _this8 = this;
      var cid = this.cid;
      var chartdom = uni.createSelectorQuery().in(this).select('#ChartBoxId' + cid).boundingClientRect(function (data) {
        if (data.width > 0 && data.height > 0) {
          _this8.mixinDatacomLoading = false;
          _this8.showchart = true;
          _this8.lastDrawTime = Date.now();
          _this8.cWidth = data.width;
          _this8.cHeight = data.height;
          if (_this8.echarts !== true) {
            _configUcharts.default.option[cid].background = _this8.background == 'rgba(0,0,0,0)' ? '#FFFFFF' : _this8.background;
            _configUcharts.default.option[cid].canvas2d = _this8.type2d;
            _configUcharts.default.option[cid].pixelRatio = _this8.pixel;
            _configUcharts.default.option[cid].animation = _this8.animation;
            _configUcharts.default.option[cid].width = data.width * _this8.pixel;
            _configUcharts.default.option[cid].height = data.height * _this8.pixel;
            _configUcharts.default.option[cid].onzoom = _this8.onzoom;
            _configUcharts.default.option[cid].ontap = _this8.ontap;
            _configUcharts.default.option[cid].ontouch = _this8.ontouch;
            _configUcharts.default.option[cid].onmouse = _this8.openmouse;
            _configUcharts.default.option[cid].onmovetip = _this8.onmovetip;
            _configUcharts.default.option[cid].tooltipShow = _this8.tooltipShow;
            _configUcharts.default.option[cid].tooltipFormat = _this8.tooltipFormat;
            _configUcharts.default.option[cid].tooltipCustom = _this8.tooltipCustom;
            _configUcharts.default.option[cid].inScrollView = _this8.inScrollView;
            _configUcharts.default.option[cid].lastDrawTime = _this8.lastDrawTime;
            _configUcharts.default.option[cid].tapLegend = _this8.tapLegend;
          }
          //如果是H5或者App端，采用renderjs渲染图表
          if (_this8.inH5 || _this8.inApp) {
            if (_this8.echarts == true) {
              cfe.option[cid].ontap = _this8.ontap;
              cfe.option[cid].onmouse = _this8.openmouse;
              cfe.option[cid].tooltipShow = _this8.tooltipShow;
              cfe.option[cid].tooltipFormat = _this8.tooltipFormat;
              cfe.option[cid].tooltipCustom = _this8.tooltipCustom;
              cfe.option[cid].lastDrawTime = _this8.lastDrawTime;
              _this8.echartsOpts = deepCloneAssign({}, cfe.option[cid]);
            } else {
              _configUcharts.default.option[cid].rotateLock = _configUcharts.default.option[cid].rotate;
              _this8.uchartsOpts = deepCloneAssign({}, _configUcharts.default.option[cid]);
            }
            //如果是小程序端，采用uCharts渲染
          } else {
            _configUcharts.default.option[cid] = formatterAssign(_configUcharts.default.option[cid], _configUcharts.default.formatter);
            _this8.mixinDatacomErrorMessage = null;
            _this8.mixinDatacomLoading = false;
            _this8.showchart = true;
            _this8.$nextTick(function () {
              if (_this8.type2d === true) {
                var query = uni.createSelectorQuery().in(_this8);
                query.select('#' + cid).fields({
                  node: true,
                  size: true
                }).exec(function (res) {
                  if (res[0]) {
                    var canvas = res[0].node;
                    var ctx = canvas.getContext('2d');
                    _configUcharts.default.option[cid].context = ctx;
                    _configUcharts.default.option[cid].rotateLock = _configUcharts.default.option[cid].rotate;
                    if (_configUcharts.default.instance[cid] && _configUcharts.default.option[cid] && _configUcharts.default.option[cid].update === true) {
                      _this8._updataUChart(cid);
                    } else {
                      canvas.width = data.width * _this8.pixel;
                      canvas.height = data.height * _this8.pixel;
                      canvas._width = data.width * _this8.pixel;
                      canvas._height = data.height * _this8.pixel;
                      setTimeout(function () {
                        _configUcharts.default.option[cid].context.restore();
                        _configUcharts.default.option[cid].context.save();
                        _this8._newChart(cid);
                      }, 100);
                    }
                  } else {
                    _this8.showchart = false;
                    _this8.mixinDatacomErrorMessage = '参数错误：开启2d模式后，未获取到dom节点，canvas-id:' + cid;
                  }
                });
              } else {
                if (_this8.inAli) {
                  _configUcharts.default.option[cid].rotateLock = _configUcharts.default.option[cid].rotate;
                }
                _configUcharts.default.option[cid].context = uni.createCanvasContext(cid, _this8);
                if (_configUcharts.default.instance[cid] && _configUcharts.default.option[cid] && _configUcharts.default.option[cid].update === true) {
                  _this8._updataUChart(cid);
                } else {
                  setTimeout(function () {
                    _configUcharts.default.option[cid].context.restore();
                    _configUcharts.default.option[cid].context.save();
                    _this8._newChart(cid);
                  }, 100);
                }
              }
            });
          }
        } else {
          _this8.mixinDatacomLoading = false;
          _this8.showchart = false;
          if (_this8.reshow == true) {
            _this8.mixinDatacomErrorMessage = '布局错误：未获取到父元素宽高尺寸！canvas-id:' + cid;
          }
        }
      }).exec();
    },
    saveImage: function saveImage() {
      uni.canvasToTempFilePath({
        canvasId: this.cid,
        success: function success(res) {
          uni.saveImageToPhotosAlbum({
            filePath: res.tempFilePath,
            success: function success() {
              uni.showToast({
                title: '保存成功',
                duration: 2000
              });
            }
          });
        }
      }, this);
    },
    getImage: function getImage() {
      var _this9 = this;
      if (this.type2d == false) {
        uni.canvasToTempFilePath({
          canvasId: this.cid,
          success: function success(res) {
            _this9.emitMsg({
              name: 'getImage',
              params: {
                type: "getImage",
                base64: res.tempFilePath
              }
            });
          }
        }, this);
      } else {
        var query = uni.createSelectorQuery().in(this);
        query.select('#' + this.cid).fields({
          node: true,
          size: true
        }).exec(function (res) {
          if (res[0]) {
            var canvas = res[0].node;
            _this9.emitMsg({
              name: 'getImage',
              params: {
                type: "getImage",
                base64: canvas.toDataURL('image/png')
              }
            });
          }
        });
      }
    },
    _newChart: function _newChart(cid) {
      var _this10 = this;
      if (this.mixinDatacomLoading == true) {
        return;
      }
      this.showchart = true;
      _configUcharts.default.instance[cid] = new _uCharts.default(_configUcharts.default.option[cid]);
      _configUcharts.default.instance[cid].addEventListener('renderComplete', function () {
        _this10.emitMsg({
          name: 'complete',
          params: {
            type: "complete",
            complete: true,
            id: cid,
            opts: _configUcharts.default.instance[cid].opts
          }
        });
        _configUcharts.default.instance[cid].delEventListener('renderComplete');
      });
      _configUcharts.default.instance[cid].addEventListener('scrollLeft', function () {
        _this10.emitMsg({
          name: 'scrollLeft',
          params: {
            type: "scrollLeft",
            scrollLeft: true,
            id: cid,
            opts: _configUcharts.default.instance[cid].opts
          }
        });
      });
      _configUcharts.default.instance[cid].addEventListener('scrollRight', function () {
        _this10.emitMsg({
          name: 'scrollRight',
          params: {
            type: "scrollRight",
            scrollRight: true,
            id: cid,
            opts: _configUcharts.default.instance[cid].opts
          }
        });
      });
    },
    _updataUChart: function _updataUChart(cid) {
      _configUcharts.default.instance[cid].updateData(_configUcharts.default.option[cid]);
    },
    _tooltipDefault: function _tooltipDefault(item, category, index, opts) {
      if (category) {
        var data = item.data;
        if ((0, _typeof2.default)(item.data) === "object") {
          data = item.data.value;
        }
        return category + ' ' + item.name + ':' + data;
      } else {
        if (item.properties && item.properties.name) {
          return item.properties.name;
        } else {
          return item.name + ':' + item.data;
        }
      }
    },
    _showTooltip: function _showTooltip(e) {
      var _this11 = this;
      var cid = this.cid;
      var tc = _configUcharts.default.option[cid].tooltipCustom;
      if (tc && tc !== undefined && tc !== null) {
        var offset = undefined;
        if (tc.x >= 0 && tc.y >= 0) {
          offset = {
            x: tc.x,
            y: tc.y + 10
          };
        }
        _configUcharts.default.instance[cid].showToolTip(e, {
          index: tc.index,
          offset: offset,
          textList: tc.textList,
          formatter: function formatter(item, category, index, opts) {
            if (typeof _configUcharts.default.option[cid].tooltipFormat === 'string' && _configUcharts.default.formatter[_configUcharts.default.option[cid].tooltipFormat]) {
              return _configUcharts.default.formatter[_configUcharts.default.option[cid].tooltipFormat](item, category, index, opts);
            } else {
              return _this11._tooltipDefault(item, category, index, opts);
            }
          }
        });
      } else {
        _configUcharts.default.instance[cid].showToolTip(e, {
          formatter: function formatter(item, category, index, opts) {
            if (typeof _configUcharts.default.option[cid].tooltipFormat === 'string' && _configUcharts.default.formatter[_configUcharts.default.option[cid].tooltipFormat]) {
              return _configUcharts.default.formatter[_configUcharts.default.option[cid].tooltipFormat](item, category, index, opts);
            } else {
              return _this11._tooltipDefault(item, category, index, opts);
            }
          }
        });
      }
    },
    _tap: function _tap(e, move) {
      var _this12 = this;
      var cid = this.cid;
      var currentIndex = null;
      var legendIndex = null;
      if (this.inScrollView === true || this.inAli) {
        var chartdom = uni.createSelectorQuery().in(this).select('#ChartBoxId' + cid).boundingClientRect(function (data) {
          e.changedTouches = [];
          if (_this12.inAli) {
            e.changedTouches.unshift({
              x: e.detail.clientX - data.left,
              y: e.detail.clientY - data.top
            });
          } else {
            e.changedTouches.unshift({
              x: e.detail.x - data.left,
              y: e.detail.y - data.top - _this12.pageScrollTop
            });
          }
          if (move) {
            if (_this12.tooltipShow === true) {
              _this12._showTooltip(e);
            }
          } else {
            currentIndex = _configUcharts.default.instance[cid].getCurrentDataIndex(e);
            legendIndex = _configUcharts.default.instance[cid].getLegendDataIndex(e);
            if (_this12.tapLegend === true) {
              _configUcharts.default.instance[cid].touchLegend(e);
            }
            if (_this12.tooltipShow === true) {
              _this12._showTooltip(e);
            }
            _this12.emitMsg({
              name: 'getIndex',
              params: {
                type: "getIndex",
                event: {
                  x: e.detail.x - data.left,
                  y: e.detail.y - data.top
                },
                currentIndex: currentIndex,
                legendIndex: legendIndex,
                id: cid,
                opts: _configUcharts.default.instance[cid].opts
              }
            });
          }
        }).exec();
      } else {
        if (move) {
          if (this.tooltipShow === true) {
            this._showTooltip(e);
          }
        } else {
          e.changedTouches = [];
          e.changedTouches.unshift({
            x: e.detail.x - e.currentTarget.offsetLeft,
            y: e.detail.y - e.currentTarget.offsetTop
          });
          currentIndex = _configUcharts.default.instance[cid].getCurrentDataIndex(e);
          legendIndex = _configUcharts.default.instance[cid].getLegendDataIndex(e);
          if (this.tapLegend === true) {
            _configUcharts.default.instance[cid].touchLegend(e);
          }
          if (this.tooltipShow === true) {
            this._showTooltip(e);
          }
          this.emitMsg({
            name: 'getIndex',
            params: {
              type: "getIndex",
              event: {
                x: e.detail.x,
                y: e.detail.y - e.currentTarget.offsetTop
              },
              currentIndex: currentIndex,
              legendIndex: legendIndex,
              id: cid,
              opts: _configUcharts.default.instance[cid].opts
            }
          });
        }
      }
    },
    _touchStart: function _touchStart(e) {
      var cid = this.cid;
      lastMoveTime = Date.now();
      if (_configUcharts.default.option[cid].enableScroll === true && e.touches.length == 1) {
        _configUcharts.default.instance[cid].scrollStart(e);
      }
      this.emitMsg({
        name: 'getTouchStart',
        params: {
          type: "touchStart",
          event: e.changedTouches[0],
          id: cid,
          opts: _configUcharts.default.instance[cid].opts
        }
      });
    },
    _touchMove: function _touchMove(e) {
      var cid = this.cid;
      var currMoveTime = Date.now();
      var duration = currMoveTime - lastMoveTime;
      var touchMoveLimit = _configUcharts.default.option[cid].touchMoveLimit || 24;
      if (duration < Math.floor(1000 / touchMoveLimit)) return; //每秒60帧
      lastMoveTime = currMoveTime;
      if (_configUcharts.default.option[cid].enableScroll === true && e.changedTouches.length == 1) {
        _configUcharts.default.instance[cid].scroll(e);
      }
      if (this.ontap === true && _configUcharts.default.option[cid].enableScroll === false && this.onmovetip === true) {
        this._tap(e, true);
      }
      if (this.ontouch === true && _configUcharts.default.option[cid].enableScroll === true && this.onzoom === true && e.changedTouches.length == 2) {
        _configUcharts.default.instance[cid].dobuleZoom(e);
      }
      this.emitMsg({
        name: 'getTouchMove',
        params: {
          type: "touchMove",
          event: e.changedTouches[0],
          id: cid,
          opts: _configUcharts.default.instance[cid].opts
        }
      });
    },
    _touchEnd: function _touchEnd(e) {
      var cid = this.cid;
      if (_configUcharts.default.option[cid].enableScroll === true && e.touches.length == 0) {
        _configUcharts.default.instance[cid].scrollEnd(e);
      }
      this.emitMsg({
        name: 'getTouchEnd',
        params: {
          type: "touchEnd",
          event: e.changedTouches[0],
          id: cid,
          opts: _configUcharts.default.instance[cid].opts
        }
      });
      if (this.ontap === true && _configUcharts.default.option[cid].enableScroll === false && this.onmovetip === true) {
        this._tap(e, true);
      }
    },
    _error: function _error(e) {
      this.mixinDatacomErrorMessage = e.detail.errMsg;
    },
    emitMsg: function emitMsg(msg) {
      this.$emit(msg.name, msg.params);
    },
    getRenderType: function getRenderType() {
      //防止如果开启echarts且父元素为v-if的情况renderjs监听不到prop变化的问题
      if (this.echarts === true && this.mixinDatacomLoading === false) {
        this.beforeInit();
      }
    },
    toJSON: function toJSON() {
      return this;
    }
  }
};
exports.default = _default2;
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/vue-cli-plugin-uni/packages/uni-cloud/dist/index.js */ 140)["uniCloud"], __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/index.js */ 2)["default"]))

/***/ }),

/***/ 151:
/*!************************************************************************************************************************************************************************************************!*\
  !*** D:/HBuilderProjects/springboot-dubbo-front-wechat/uni_modules/qiun-data-charts/components/qiun-data-charts/qiun-data-charts.vue?vue&type=style&index=0&id=fe947b98&scoped=true&lang=css& ***!
  \************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_1_1_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_6_oneOf_1_2_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_3_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_qiun_data_charts_vue_vue_type_style_index_0_id_fe947b98_scoped_true_lang_css___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--6-oneOf-1-2!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src??ref--6-oneOf-1-3!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!../../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./qiun-data-charts.vue?vue&type=style&index=0&id=fe947b98&scoped=true&lang=css& */ 152);
/* harmony import */ var _HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_1_1_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_6_oneOf_1_2_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_3_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_qiun_data_charts_vue_vue_type_style_index_0_id_fe947b98_scoped_true_lang_css___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_1_1_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_6_oneOf_1_2_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_3_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_qiun_data_charts_vue_vue_type_style_index_0_id_fe947b98_scoped_true_lang_css___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_1_1_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_6_oneOf_1_2_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_3_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_qiun_data_charts_vue_vue_type_style_index_0_id_fe947b98_scoped_true_lang_css___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_1_1_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_6_oneOf_1_2_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_3_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_qiun_data_charts_vue_vue_type_style_index_0_id_fe947b98_scoped_true_lang_css___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_1_1_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_6_oneOf_1_2_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_3_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_qiun_data_charts_vue_vue_type_style_index_0_id_fe947b98_scoped_true_lang_css___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 152:
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!./node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--6-oneOf-1-2!./node_modules/postcss-loader/src??ref--6-oneOf-1-3!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/HBuilderProjects/springboot-dubbo-front-wechat/uni_modules/qiun-data-charts/components/qiun-data-charts/qiun-data-charts.vue?vue&type=style&index=0&id=fe947b98&scoped=true&lang=css& ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

// extracted by mini-css-extract-plugin
    if(false) { var cssReload; }
  

/***/ })

}]);
//# sourceMappingURL=../../../../../.sourcemap/mp-weixin/uni_modules/qiun-data-charts/components/qiun-data-charts/qiun-data-charts.js.map
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'uni_modules/qiun-data-charts/components/qiun-data-charts/qiun-data-charts-create-component',
    {
        'uni_modules/qiun-data-charts/components/qiun-data-charts/qiun-data-charts-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('2')['createComponent'](__webpack_require__(135))
        })
    },
    [['uni_modules/qiun-data-charts/components/qiun-data-charts/qiun-data-charts-create-component']]
]);
