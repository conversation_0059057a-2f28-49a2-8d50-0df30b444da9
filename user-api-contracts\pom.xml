<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.example</groupId>
        <artifactId>spring-boot-dubbo-demo</artifactId>
        <version>1.0.0</version>
    </parent>

    <groupId>com.example</groupId>
    <artifactId>user-api-contracts</artifactId>
    <packaging>jar</packaging>

    <name>user-api-contracts</name>
    <description>用户服务API契约模块，包含用户相关的服务接口、DTO和Command</description>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <!-- 依赖 shared-kernel 获取通用的响应类和异常 -->
        <dependency>
            <groupId>com.example</groupId>
            <artifactId>shared-kernel</artifactId>
        </dependency>

        <!-- Bean Validation API -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>

        <!-- MyBatis Plus (用于 IService 接口) -->
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
        </dependency>

        <!-- 测试依赖 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>
</project>
