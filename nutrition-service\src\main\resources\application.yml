server:
  port: 8089

spring:
  application:
    name: nutrition-service
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ************************************************************************************************************************
    username: root
    password: 123456
  redis:
    host: localhost
    port: 6379
    database: 0
    timeout: 5000
    lettuce:
      pool:
        max-active: 8
        max-idle: 8
        min-idle: 0
        max-wait: -1
  kafka:
    bootstrap-servers: localhost:9092
    producer:
      acks: all
      retries: 3
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: org.springframework.kafka.support.serializer.JsonSerializer
      properties:
        enable.idempotence: true
        max.in.flight.requests.per.connection: 1
    consumer:
      group-id: nutrition-service
      auto-offset-reset: earliest
      enable-auto-commit: true

# ==================== 事件系统配置 ====================
app:
  event:
    # 事件提供者类型：redis（默认）或 kafka
    provider: kafka  # 可选值: redis, kafka (默认: redis)

    # 统一的事件channel/topic名称
    # Redis使用此名称作为pub/sub channel
    # Kafka使用此名称作为topic
    channel: domain-events  # 统一的事件channel/topic名称

    # 消费者配置
    consumer:
      # 是否启用事件消费（默认：false）
      # nutrition-service需要消费事件进行营养分析
      enabled: true

    # 线程池配置（用于事件监听器）
    thread-pool:
      core-size: 8              # 核心线程数（默认：8）
      max-size: 16              # 最大线程数（默认：16）
      queue-capacity: 2000      # 队列容量（默认：2000）
      name-prefix: "event-listener-"  # 线程名前缀（默认：event-listener-）

    # Kafka特定配置
    kafka:
      # 并发消费者数量（默认：5）
      concurrency: 5

      # 信任的包列表（用于反序列化安全）
      trusted-packages:
        - com.example.shared.event
        - com.example.diet.event
        - com.example.nutrition.event

# ==================== 缓存系统配置 ====================
  cache:
    # 本地缓存配置（Caffeine）
    local:
      expire-after-write: 15m   # 写入后过期时间（默认：15分钟）
      maximum-size: 20000       # 最大缓存条目数（默认：20000）

    # Redis缓存配置
    redis:
      ttl: 45m                  # 缓存TTL（默认：45分钟）

    # 异步缓存操作线程池配置
    async:
      core-pool-size: 8         # 核心线程池大小（默认：8）
      max-pool-size: 16         # 最大线程池大小（默认：16）
      queue-capacity: 1000      # 队列容量（默认：1000）

mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
#    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      id-type: auto
  type-aliases-package: com.example.nutrition.entity

dubbo:
  application:
    name: ${spring.application.name}
  protocol:
    name: dubbo
    port: -1
  registry:
    address: zookeeper://localhost:2181
  provider:
    version: 3.1.0    # 服务提供者使用的版本
  consumer:
    version: 3.1.0    # 服务消费者默认使用的版本

jwt:
  expiration: 86400000
  secret: your-secret-key-should-be-at-least-256-bits-long

# 添加日志配置
logging:
  level:
    com.example.*: INFO
    com.example.common.cache: WARN
    com.example.common.event.cache: WARN
    com.example.nutrition.listener: WARN