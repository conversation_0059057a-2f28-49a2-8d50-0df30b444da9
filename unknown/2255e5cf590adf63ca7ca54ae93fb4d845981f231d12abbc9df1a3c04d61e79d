@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.add-diet {
  min-height: 100vh;
  background-color: #f8f8f8;
  padding: 0 20rpx 120rpx;
}
.safe-area {
  height: 80rpx;
  background-color: #f8f8f8;
}
.header {
  display: flex;
  align-items: center;
  height: 88rpx;
  padding: 0 30rpx;
}
.header .back-btn {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}
.header .back-btn image {
  width: 36rpx;
  height: 36rpx;
}
.header .title {
  flex: 1;
  text-align: center;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}
.form-section {
  padding-bottom: 30rpx;
}
.meal-selector {
  display: flex;
  background-color: #ffffff;
  border-radius: 20rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  -webkit-animation: fadeIn 0.5s ease-in-out;
          animation: fadeIn 0.5s ease-in-out;
}
.meal-selector .meal-option {
  flex: 1;
  height: 80rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  transition: all 0.3s ease;
}
.meal-selector .meal-option text {
  font-size: 28rpx;
  color: #666;
  transition: all 0.3s ease;
}
.meal-selector .meal-option.active {
  background: linear-gradient(135deg, #4CAF50, #8BC34A);
}
.meal-selector .meal-option.active text {
  color: #ffffff;
  font-weight: 500;
}
.meal-selector .meal-option:active {
  opacity: 0.8;
}
.form-item {
  background-color: #ffffff;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  -webkit-animation: fadeIn 0.5s ease-in-out;
          animation: fadeIn 0.5s ease-in-out;
}
.form-item .form-label {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 20rpx;
  display: block;
  position: relative;
  padding-left: 20rpx;
}
.form-item .form-label::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
  width: 8rpx;
  height: 28rpx;
  background-color: #4CAF50;
  border-radius: 4rpx;
}
.form-item .form-input .picker-value {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 80rpx;
  background-color: #f9f9f9;
  border-radius: 12rpx;
  padding: 0 20rpx;
}
.form-item .form-input .picker-value text {
  font-size: 28rpx;
  color: #333;
}
.form-item .form-input .picker-value image {
  width: 36rpx;
  height: 36rpx;
}
.form-item .form-input textarea {
  width: 100%;
  height: 160rpx;
  font-size: 28rpx;
  background-color: #f9f9f9;
  border-radius: 12rpx;
  padding: 20rpx;
}
.food-list {
  background-color: #ffffff;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  -webkit-animation: fadeIn 0.5s ease-in-out;
          animation: fadeIn 0.5s ease-in-out;
}
.food-list .section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}
.food-list .section-header .section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  position: relative;
  padding-left: 20rpx;
}
.food-list .section-header .section-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
  width: 8rpx;
  height: 32rpx;
  background-color: #4CAF50;
  border-radius: 4rpx;
}
.food-list .section-header .add-food-btn {
  display: flex;
  align-items: center;
  background-color: rgba(76, 175, 80, 0.1);
  padding: 10rpx 20rpx;
  border-radius: 30rpx;
  transition: all 0.3s ease;
}
.food-list .section-header .add-food-btn:active {
  -webkit-transform: scale(0.95);
          transform: scale(0.95);
}
.food-list .section-header .add-food-btn image {
  width: 32rpx;
  height: 32rpx;
  margin-right: 8rpx;
}
.food-list .section-header .add-food-btn text {
  font-size: 28rpx;
  color: #4CAF50;
  font-weight: 500;
}
.food-list .empty-tip {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 80rpx 0;
}
.food-list .empty-tip image {
  width: 140rpx;
  height: 140rpx;
  margin-bottom: 30rpx;
  opacity: 0.6;
}
.food-list .empty-tip text {
  font-size: 28rpx;
  color: #999;
}
.food-list .food-item {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
  padding: 30rpx;
  border-radius: 16rpx;
  background-color: #f9f9f9;
  transition: all 0.3s ease;
}
.food-list .food-item:last-child {
  margin-bottom: 0;
}
.food-list .food-item:active {
  -webkit-transform: scale(0.98);
          transform: scale(0.98);
}
.food-list .food-item .food-info {
  flex: 1;
  margin-right: 20rpx;
}
.food-list .food-item .food-info .food-name {
  font-size: 30rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 10rpx;
  display: block;
}
.food-list .food-item .food-info .food-desc {
  font-size: 24rpx;
  color: #999;
}
.food-list .food-item .food-amount {
  display: flex;
  align-items: center;
  margin-right: 20rpx;
}
.food-list .food-item .food-amount .amount-control {
  display: flex;
  align-items: center;
}
.food-list .food-item .food-amount .amount-control .minus-btn, .food-list .food-item .food-amount .amount-control .plus-btn {
  width: 60rpx;
  height: 60rpx;
  border-radius: 30rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  transition: all 0.3s ease;
}
.food-list .food-item .food-amount .amount-control .minus-btn.minus-btn, .food-list .food-item .food-amount .amount-control .plus-btn.minus-btn {
  background-color: rgba(244, 67, 54, 0.1);
}
.food-list .food-item .food-amount .amount-control .minus-btn.minus-btn:active, .food-list .food-item .food-amount .amount-control .plus-btn.minus-btn:active {
  background-color: rgba(244, 67, 54, 0.2);
}
.food-list .food-item .food-amount .amount-control .minus-btn.minus-btn text, .food-list .food-item .food-amount .amount-control .plus-btn.minus-btn text {
  color: #F44336;
}
.food-list .food-item .food-amount .amount-control .minus-btn.plus-btn, .food-list .food-item .food-amount .amount-control .plus-btn.plus-btn {
  background-color: rgba(76, 175, 80, 0.1);
}
.food-list .food-item .food-amount .amount-control .minus-btn.plus-btn:active, .food-list .food-item .food-amount .amount-control .plus-btn.plus-btn:active {
  background-color: rgba(76, 175, 80, 0.2);
}
.food-list .food-item .food-amount .amount-control .minus-btn.plus-btn text, .food-list .food-item .food-amount .amount-control .plus-btn.plus-btn text {
  color: #4CAF50;
}
.food-list .food-item .food-amount .amount-control .minus-btn text, .food-list .food-item .food-amount .amount-control .plus-btn text {
  font-size: 36rpx;
  font-weight: bold;
}
.food-list .food-item .food-amount .amount-control .amount-input {
  width: 80rpx;
  text-align: center;
  margin: 0 15rpx;
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
}
.food-list .food-item .food-nutrition-info {
  margin-right: 20rpx;
}
.food-list .food-item .food-nutrition-info .food-calorie {
  font-size: 30rpx;
  color: #FF9800;
  font-weight: bold;
  display: block;
  text-align: right;
  margin-bottom: 10rpx;
}
.food-list .food-item .food-nutrition-info .nutrition-detail {
  display: flex;
  flex-direction: column;
}
.food-list .food-item .food-nutrition-info .nutrition-detail text {
  font-size: 22rpx;
  color: #666;
  margin-top: 6rpx;
}
.food-list .food-item .delete-btn {
  width: 70rpx;
  height: 70rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 35rpx;
  background-color: rgba(244, 67, 54, 0.1);
  transition: all 0.3s ease;
}
.food-list .food-item .delete-btn:active {
  background-color: rgba(244, 67, 54, 0.2);
}
.food-list .food-item .delete-btn image {
  width: 36rpx;
  height: 36rpx;
}
.bottom-bar {
  position: fixed;
  left: 0;
  bottom: 0;
  right: 0;
  height: 120rpx;
  background-color: #ffffff;
  display: flex;
  align-items: center;
  padding: 0 30rpx;
  box-shadow: 0 -6rpx 20rpx rgba(0, 0, 0, 0.05);
  z-index: 100;
}
.bottom-bar .total-calorie {
  flex: 1;
}
.bottom-bar .total-calorie text {
  font-size: 32rpx;
  color: #FF9800;
  font-weight: bold;
  background: linear-gradient(90deg, #FF9800, #FF5722);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
.bottom-bar .save-btn {
  width: 240rpx;
  height: 80rpx;
  background: linear-gradient(135deg, #4CAF50, #8BC34A);
  border-radius: 40rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: 0 6rpx 12rpx rgba(76, 175, 80, 0.2);
  transition: all 0.3s ease;
}
.bottom-bar .save-btn:active {
  -webkit-transform: scale(0.95);
          transform: scale(0.95);
  box-shadow: 0 3rpx 6rpx rgba(76, 175, 80, 0.2);
}
.bottom-bar .save-btn text {
  font-size: 30rpx;
  color: #ffffff;
  font-weight: 500;
}
.food-search-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
}
.food-search-modal .modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  -webkit-animation: fadeIn 0.3s ease;
          animation: fadeIn 0.3s ease;
}
.food-search-modal .modal-content {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ffffff;
  border-radius: 30rpx 30rpx 0 0;
  overflow: hidden;
  max-height: 85vh;
  display: flex;
  flex-direction: column;
  -webkit-animation: slideUp 0.3s ease-out;
          animation: slideUp 0.3s ease-out;
  box-shadow: 0 -10rpx 30rpx rgba(0, 0, 0, 0.1);
  transition: height 0.3s ease;
}
.food-search-modal .modal-content .modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 100rpx;
  padding: 0 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}
.food-search-modal .modal-content .modal-header .modal-title {
  font-size: 34rpx;
  font-weight: bold;
  color: #333;
}
.food-search-modal .modal-content .modal-header .close-btn {
  width: 70rpx;
  height: 70rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 35rpx;
  transition: all 0.3s ease;
}
.food-search-modal .modal-content .modal-header .close-btn:active {
  background-color: #f5f5f5;
}
.food-search-modal .modal-content .modal-header .close-btn image {
  width: 32rpx;
  height: 32rpx;
}
.food-search-modal .modal-content .search-box {
  display: flex;
  align-items: center;
  padding: 20rpx 30rpx;
  margin-bottom: 10rpx;
}
.food-search-modal .modal-content .search-box image {
  width: 36rpx;
  height: 36rpx;
  margin-right: 15rpx;
  opacity: 0.6;
  position: absolute;
  left: 50rpx;
  z-index: 1;
}
.food-search-modal .modal-content .search-box input {
  flex: 1;
  height: 70rpx;
  background-color: #f5f5f5;
  border-radius: 35rpx;
  padding: 0 30rpx 0 56rpx;
  font-size: 28rpx;
  position: relative;
  box-shadow: inset 0 2rpx 5rpx rgba(0, 0, 0, 0.03);
}
.food-search-modal .modal-content .search-box input:focus {
  background-color: #f0f0f0;
}
.food-search-modal .modal-content .category-scroll {
  white-space: nowrap;
  margin: 0 30rpx 20rpx;
  height: 80rpx;
}
.food-search-modal .modal-content .category-scroll .category-item {
  display: inline-block;
  height: 64rpx;
  padding: 0 30rpx;
  margin-right: 15rpx;
  border-radius: 32rpx;
  background-color: #f5f5f5;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}
.food-search-modal .modal-content .category-scroll .category-item:active {
  -webkit-transform: scale(0.95);
          transform: scale(0.95);
}
.food-search-modal .modal-content .category-scroll .category-item.active {
  background: linear-gradient(135deg, #4CAF50, #8BC34A);
  box-shadow: 0 4rpx 8rpx rgba(76, 175, 80, 0.2);
}
.food-search-modal .modal-content .category-scroll .category-item.active:after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 32rpx;
}
.food-search-modal .modal-content .category-scroll .category-item.active text {
  color: #ffffff;
  font-weight: 500;
}
.food-search-modal .modal-content .category-scroll .category-item text {
  font-size: 26rpx;
  color: #666;
  line-height: 64rpx;
}
.food-search-modal .modal-content .food-scroll {
  height: 650rpx;
  min-height: 400rpx;
}
.food-search-modal .modal-content .food-scroll::-webkit-scrollbar {
  width: 0;
  height: 0;
  display: none;
}
.food-search-modal .modal-content .food-scroll .food-list-container {
  padding: 10rpx 30rpx 30rpx;
}
.food-search-modal .modal-content .food-scroll .food-list-container .food-card {
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 25rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  border: 1rpx solid rgba(0, 0, 0, 0.03);
}
.food-search-modal .modal-content .food-scroll .food-list-container .food-card:active {
  -webkit-transform: scale(0.98);
          transform: scale(0.98);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.03);
}
.food-search-modal .modal-content .food-scroll .food-list-container .food-card .food-card-main {
  display: flex;
  justify-content: space-between;
  margin-bottom: 15rpx;
}
.food-search-modal .modal-content .food-scroll .food-list-container .food-card .food-card-main .food-info {
  flex: 1;
}
.food-search-modal .modal-content .food-scroll .food-list-container .food-card .food-card-main .food-info .food-name {
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 12rpx;
  display: block;
}
.food-search-modal .modal-content .food-scroll .food-list-container .food-card .food-card-main .food-nutrition {
  text-align: right;
  margin-left: 20rpx;
}
.food-search-modal .modal-content .food-scroll .food-list-container .food-card .food-card-main .food-nutrition .food-calorie {
  font-size: 28rpx;
  color: #FF9800;
  font-weight: bold;
  display: block;
  margin-bottom: 10rpx;
  background: linear-gradient(90deg, #FF9800, #FF5722);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
.food-search-modal .modal-content .food-scroll .food-list-container .food-card .food-card-main .food-nutrition .food-unit {
  font-size: 24rpx;
  color: #999;
}
.food-search-modal .modal-content .food-scroll .food-list-container .food-card .food-detail {
  display: flex;
  justify-content: space-between;
  background-color: #f9f9f9;
  border-radius: 12rpx;
  padding: 15rpx;
}
.food-search-modal .modal-content .food-scroll .food-list-container .food-card .food-detail .nutrition-item {
  text-align: center;
  padding: 0 10rpx;
}
.food-search-modal .modal-content .food-scroll .food-list-container .food-card .food-detail .nutrition-item .nutrition-value {
  font-size: 26rpx;
  color: #333;
  font-weight: 500;
  display: block;
  margin-bottom: 5rpx;
}
.food-search-modal .modal-content .food-scroll .food-list-container .food-card .food-detail .nutrition-item .nutrition-label {
  font-size: 22rpx;
  color: #666;
}
.food-selection-panel {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
}
.food-selection-panel .modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
}
.food-selection-panel .selection-content {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ffffff;
  border-radius: 20rpx 20rpx 0 0;
  overflow: hidden;
}
.food-selection-panel .selection-content .selection-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 88rpx;
  padding: 0 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}
.food-selection-panel .selection-content .selection-header .selection-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}
.food-selection-panel .selection-content .selection-header .close-btn {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}
.food-selection-panel .selection-content .selection-header .close-btn image {
  width: 32rpx;
  height: 32rpx;
}
.food-selection-panel .selection-content .selection-tabs {
  display: flex;
  height: 80rpx;
  border-bottom: 1rpx solid #f0f0f0;
}
.food-selection-panel .selection-content .selection-tabs .tab-item {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
}
.food-selection-panel .selection-content .selection-tabs .tab-item text {
  font-size: 28rpx;
  color: #666;
}
.food-selection-panel .selection-content .selection-tabs .tab-item.active text {
  color: #4CAF50;
  font-weight: 500;
}
.food-selection-panel .selection-content .selection-tabs .tab-item.active:after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  -webkit-transform: translateX(-50%);
          transform: translateX(-50%);
  width: 40rpx;
  height: 4rpx;
  background-color: #4CAF50;
}
.food-selection-panel .selection-content .selection-body {
  padding: 30rpx;
}
.food-selection-panel .selection-content .selection-body .food-measure {
  text-align: center;
  margin-bottom: 20rpx;
}
.food-selection-panel .selection-content .selection-body .food-measure text {
  font-size: 28rpx;
  color: #666;
}
.food-selection-panel .selection-content .selection-body .amount-control {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 30rpx;
}
.food-selection-panel .selection-content .selection-body .amount-control .control-btn {
  width: 80rpx;
  height: 80rpx;
  border-radius: 40rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f5f5f5;
}
.food-selection-panel .selection-content .selection-body .amount-control .control-btn text {
  font-size: 36rpx;
  color: #333;
}
.food-selection-panel .selection-content .selection-body .amount-control .amount-input {
  width: 100rpx;
  height: 80rpx;
  text-align: center;
  font-size: 32rpx;
  margin: 0 20rpx;
}
.food-selection-panel .selection-content .selection-body .grams-input-container {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 30rpx;
}
.food-selection-panel .selection-content .selection-body .grams-input-container .grams-input {
  width: 200rpx;
  height: 80rpx;
  background-color: #f5f5f5;
  border-radius: 10rpx;
  text-align: center;
  font-size: 32rpx;
}
.food-selection-panel .selection-content .selection-body .grams-input-container .grams-label {
  font-size: 28rpx;
  color: #666;
  margin-left: 10rpx;
}
.food-selection-panel .selection-content .selection-body .nutrition-preview {
  background-color: #f8f8f8;
  border-radius: 10rpx;
  padding: 20rpx;
  display: flex;
  flex-wrap: wrap;
}
.food-selection-panel .selection-content .selection-body .nutrition-preview .preview-item {
  width: 50%;
  margin-bottom: 20rpx;
}
.food-selection-panel .selection-content .selection-body .nutrition-preview .preview-item .preview-label {
  font-size: 24rpx;
  color: #999;
  display: block;
  margin-bottom: 5rpx;
}
.food-selection-panel .selection-content .selection-body .nutrition-preview .preview-item .preview-value {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}
.food-selection-panel .selection-content .selection-footer {
  display: flex;
  height: 100rpx;
  border-top: 1rpx solid #f0f0f0;
}
.food-selection-panel .selection-content .selection-footer .cancel-btn {
  width: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
}
.food-selection-panel .selection-content .selection-footer .cancel-btn text {
  font-size: 30rpx;
  color: #666;
}
.food-selection-panel .selection-content .selection-footer .confirm-btn {
  width: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #4CAF50;
}
.food-selection-panel .selection-content .selection-footer .confirm-btn text {
  font-size: 30rpx;
  color: #ffffff;
}
/* 加载和动画等，全都保留 */

