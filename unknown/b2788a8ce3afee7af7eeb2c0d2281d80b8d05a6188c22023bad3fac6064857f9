server:
  port: 8088

spring:
  application:
    name: diet-service
  datasource:
    url: ***********************************************************************************************************
    username: root
    password: 123456
    driver-class-name: com.mysql.cj.jdbc.Driver
  redis:
    host: localhost
    port: 6379
  # Kafka配置 - 仅用于事件发布
  kafka:
    bootstrap-servers: localhost:9092
    producer:
      acks: all
      retries: 3
      properties:
        enable.idempotence: true
        max.in.flight.requests.per.connection: 1

# ==================== 事件系统配置 ====================
app:
  event:
    # 事件提供者类型：redis（默认）或 kafka
    provider: kafka  # 可选值: redis, kafka (默认: redis)

    # 统一的事件channel/topic名称
    # Redis使用此名称作为pub/sub channel
    # Kafka使用此名称作为topic
    channel: domain-events  # 统一的事件channel/topic名称

    # 消费者配置
    consumer:
      # 是否启用事件消费（默认：false）
      # diet-service只发布事件，不消费事件
      enabled: false

    # 线程池配置（用于事件监听器）- 当consumer.enabled=true时生效
    # thread-pool:
    #   core-size: 8              # 核心线程数（默认：8）
    #   max-size: 16              # 最大线程数（默认：16）
    #   queue-capacity: 2000      # 队列容量（默认：2000）
    #   name-prefix: "event-listener-"  # 线程名前缀

    # Kafka特定配置 - 当使用Kafka且consumer.enabled=true时生效
    # kafka:
    #   concurrency: 5              # 并发消费者数量（默认：5）
    #   trusted-packages:           # 信任的包列表（用于反序列化安全）
    #     - com.example.shared.event
    #     - com.example.diet.event
    #     - com.example.nutrition.event

# ==================== 缓存系统配置 ====================
  # cache:
    # 本地缓存配置（Caffeine）
    # local:
    #   expire-after-write: 15m   # 写入后过期时间（默认：15分钟）
    #   maximum-size: 20000       # 最大缓存条目数（默认：20000）

    # Redis缓存配置
    # redis:
    #   ttl: 45m                  # 缓存TTL（默认：45分钟）

    # 异步缓存操作线程池配置
    # async:
    #   core-pool-size: 8         # 核心线程池大小（默认：8）
    #   max-pool-size: 16         # 最大线程池大小（默认：16）
    #   queue-capacity: 1000      # 队列容量（默认：1000）

mybatis-plus:
  mapper-locations: classpath:/mapper/*.xml
  type-aliases-package: com.example.diet.entity
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.nologging.NoLoggingImpl

# 添加JWT配置
jwt:
  expiration: 86400000
  secret: your-secret-key-should-be-at-least-256-bits-long

dubbo:
  application:
    name: ${spring.application.name}
  protocol:
    name: dubbo
    port: 20885
  registry:
    address: zookeeper://127.0.0.1:2181
  scan:
    base-packages: com.example.diet.service
  provider:
    version: 3.1.0    # 服务提供者使用的版本
  consumer:
    version: 3.1.0    # 服务消费者默认使用的版本

# 添加日志配置
logging:
  level:
    com.example.*: INFO
    com.example.common.cache: WARN
    com.example.common.event.cache: WARN