<view class="health-report"><view class="safe-area"></view><view class="header"><view data-event-opts="{{[['tap',[['navigateBack']]]]}}" class="back-btn" bindtap="__e"><image src="/static/icons/back.png"></image></view><text class="title">健康报告</text></view><view class="date-selector"><view class="date-actions"><view data-event-opts="{{[['tap',[['changeDate',[-1]]]]]}}" class="date-arrow" bindtap="__e"><image src="/static/icons/arrow-left.png"></image></view><picker mode="date" value="{{datePickerValue}}" start="{{startDate}}" end="{{endDate}}" data-event-opts="{{[['change',[['onDateChange',['$event']]]]]}}" bindchange="__e"><view class="date-display"><text class="date-title">{{dateTitle}}</text><text class="date-value">{{formattedDate}}</text></view></picker><view data-event-opts="{{[['tap',[['changeDate',[1]]]]]}}" class="date-arrow" bindtap="__e"><image src="/static/icons/arrow-right.png"></image></view></view></view><block wx:if="{{isLoading}}"><view class="loading-container"><view class="loading-box"><view class="loading-spinner"></view><text class="loading-text">加载中...</text></view></view></block><block wx:else><view class="content-container"><view class="health-score card"><view class="section-header"><text class="section-title">健康评分</text><text class="date-info">{{formattedDate}}</text></view><view class="score-container"><view class="score-circle"><view class="score-value">{{healthReport.healthScore}}</view><view class="score-desc">健康评分</view></view><view class="score-change"><view class="{{['change-value',(healthReport.scoreChange>0)?'positive':'',(healthReport.scoreChange<0)?'negative':'']}}"><text>{{(healthReport.scoreChange>0?'+':'')+healthReport.scoreChange}}</text><view class="change-icon"><image src="{{healthReport.scoreChange>=0?'/static/icons/up.png':'/static/icons/down.png'}}"></image></view></view><text class="change-label">较上周</text></view></view></view><view class="nutrition-balance card"><view class="section-header"><text class="section-title">营养平衡</text></view><view class="radar-container"><qiun-data-charts class="vue-ref" vue-id="8ad51872-1" type="radar" opts="{{radarChartOpts}}" chartData="{{radarChartData}}" canvasId="balanceRadar" data-ref="balanceRadar" bind:__l="__l"></qiun-data-charts></view><view class="radar-legend"><block wx:for="{{nutritionBalanceItems}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="legend-item"><view class="legend-color" style="{{'background-color:'+(item.color)+';'}}"></view><text class="legend-label">{{item.label}}</text><text class="legend-value">{{item.value}}</text></view></block></view></view><view class="nutrition-compare card"><view class="section-header"><text class="section-title">本周 vs 上周</text></view><view class="compare-container"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="compare-item"><view class="compare-header"><view class="compare-icon"><image src="{{item.$orig.icon}}"></image></view><text class="compare-label">{{item.$orig.label}}</text></view><view class="compare-data"><view class="data-col"><text class="data-value">{{item.$orig.lastWeek}}</text><text class="data-desc">上周</text></view><view class="direction-indicator"><view class="{{['indicator-arrow',(item.$orig.change>0)?'increase':'',(item.$orig.change<0)?'decrease':'',(item.$orig.change===0)?'neutral':'']}}"><image src="{{item.m0}}"></image></view><text class="{{['change-rate',(item.$orig.change>0)?'increase':'',(item.$orig.change<0)?'decrease':'',(item.$orig.change===0)?'neutral':'']}}">{{''+item.m1+''}}</text></view><view class="data-col"><text class="data-value highlight">{{item.$orig.thisWeek}}</text><text class="data-desc">本周</text></view></view></view></block></view></view></view></block></view>