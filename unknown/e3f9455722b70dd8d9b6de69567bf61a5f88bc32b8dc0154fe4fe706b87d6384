<wxs src="./qf-image-cropper.wxs" module="cropper"></wxs>
<view class="image-cropper data-v-40f35364" style="{{'z-index:'+(zIndex)+';'}}" bindwheel="{{cropper.mousewheel}}"><block wx:if="{{use2d}}"><canvas class="img-canvas data-v-40f35364" style="{{'width:'+(canvansWidth+'px')+';'+('height:'+(canvansHeight+'px')+';')}}" type="2d" id="imgCanvas"></canvas></block><block wx:else><canvas class="img-canvas data-v-40f35364" style="{{'width:'+(canvansWidth+'px')+';'+('height:'+(canvansHeight+'px')+';')}}" id="imgCanvas" canvas-id="imgCanvas"></canvas></block><view class="pic-preview data-v-40f35364" id="pic-preview" change:init="{{cropper.initObserver}}" init="{{initData}}" bindtouchstart="{{cropper.touchstart}}" bindtouchmove="{{cropper.touchmove}}" bindtouchend="{{cropper.touchend}}"><block wx:if="{{imgSrc}}"><image class="crop-image data-v-40f35364" style="{{(cropper.imageStyles)}}" id="crop-image" src="{{imgSrc}}" webp="{{true}}"></image></block><block wx:for="{{maskList}}" wx:for-item="item" wx:for-index="index" wx:key="id"><view class="crop-mask-block data-v-40f35364" style="{{(cropper.maskStylesList[index])}}" id="{{item.id}}"></view></block><block wx:if="{{showBorder}}"><view class="crop-border data-v-40f35364" style="{{(cropper.borderStyles)}}" id="crop-border"></view></block><block wx:if="{{radius>0}}"><view class="crop-circle-box data-v-40f35364" style="{{(cropper.circleBoxStyles)}}" id="crop-circle-box"><view class="crop-circle data-v-40f35364" style="{{(cropper.circleStyles)}}" id="crop-circle"></view></view></block><block wx:if="{{showGrid}}"><block class="data-v-40f35364"><block wx:for="{{gridList}}" wx:for-item="item" wx:for-index="index" wx:key="id"><view class="crop-grid data-v-40f35364" style="{{(cropper.gridStylesList[index])}}" id="{{item.id}}"></view></block></block></block><block wx:if="{{showAngle}}"><block class="data-v-40f35364"><block wx:for="{{angleList}}" wx:for-item="item" wx:for-index="index" wx:key="id"><view class="crop-angle data-v-40f35364" style="{{(cropper.angleStylesList[index])}}" id="{{item.id}}"><view style="{{'width:'+(angleSize+'px')+';'+('height:'+(angleSize+'px')+';')}}" class="data-v-40f35364"></view></view></block></block></block></view><slot></slot><view class="fixed-bottom safe-area-inset-bottom data-v-40f35364" style="{{'z-index:'+(initData.area.zIndex+99)+';'}}"><block wx:if="{{(rotatable||reverseRotatable)&&!!imgSrc}}"><view class="action-bar data-v-40f35364"><block wx:if="{{reverseRotatable}}"><view class="rotate-icon data-v-40f35364" bindtap="{{cropper.rotateImage270}}"></view></block><block wx:if="{{rotatable}}"><view class="rotate-icon is-reverse data-v-40f35364" bindtap="{{cropper.rotateImage90}}"></view></block></view></block><block wx:if="{{!choosable}}"><view data-event-opts="{{[['tap',[['cropClick',['$event']]]]]}}" class="choose-btn data-v-40f35364" bindtap="__e">确定</view></block><block wx:else><block wx:if="{{!!imgSrc}}"><block class="data-v-40f35364"><view data-event-opts="{{[['tap',[['chooseImage',['$event']]]]]}}" class="rechoose data-v-40f35364" bindtap="__e">重选</view><button class="button data-v-40f35364" size="mini" data-event-opts="{{[['tap',[['cropClick',['$event']]]]]}}" bindtap="__e">确定</button></block></block><block wx:else><view data-event-opts="{{[['tap',[['chooseImage',['$event']]]]]}}" class="choose-btn data-v-40f35364" bindtap="__e">选择图片</view></block></block></view></view>