@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.nutrition-analysis {
  padding: 0 20rpx 20rpx;
  background-color: #f8f8f8;
  min-height: 100vh;
}
.safe-area {
  height: 80rpx;
  background-color: #f8f8f8;
}
.header {
  padding: 0 30rpx;
  height: 88rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  margin-bottom: 20rpx;
}
.header .title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}
.date-selector {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx 30rpx;
  background-color: #ffffff;
  margin-bottom: 20rpx;
  border-radius: 10rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  z-index: 10;
}
.date-selector .date-actions {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  padding: 0;
}
.date-selector .date-actions .date-arrow {
  width: 40rpx;
  height: 40rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}
.date-selector .date-actions .date-arrow image {
  width: 24rpx;
  height: 24rpx;
}
.date-selector .date-actions .date-display {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 10rpx 0;
  max-width: 300rpx;
}
.date-selector .date-actions .date-display .date-title {
  font-size: 40rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}
.date-selector .date-actions .date-display .date-value {
  font-size: 28rpx;
  color: #999;
}
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300rpx;
}
.loading-container .loading-box {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.loading-container .loading-box .loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #4CAF50;
  border-radius: 50%;
  -webkit-animation: spin 1s linear infinite;
          animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}
.loading-container .loading-box .loading-text {
  font-size: 28rpx;
  color: #666;
}
@-webkit-keyframes spin {
0% {
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
}
100% {
    -webkit-transform: rotate(360deg);
            transform: rotate(360deg);
}
}
@keyframes spin {
0% {
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
}
100% {
    -webkit-transform: rotate(360deg);
            transform: rotate(360deg);
}
}
.card {
  background-color: #ffffff;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  -webkit-animation: fadeIn 0.5s ease-in-out;
          animation: fadeIn 0.5s ease-in-out;
  position: relative;
  z-index: 1;
}
@-webkit-keyframes fadeIn {
from {
    opacity: 0;
    -webkit-transform: translateY(20rpx);
            transform: translateY(20rpx);
}
to {
    opacity: 1;
    -webkit-transform: translateY(0);
            transform: translateY(0);
}
}
@keyframes fadeIn {
from {
    opacity: 0;
    -webkit-transform: translateY(20rpx);
            transform: translateY(20rpx);
}
to {
    opacity: 1;
    -webkit-transform: translateY(0);
            transform: translateY(0);
}
}
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}
.section-header .section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  position: relative;
  padding-left: 20rpx;
}
.section-header .section-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
  width: 8rpx;
  height: 32rpx;
  background-color: #4CAF50;
  border-radius: 4rpx;
}
.section-header .date-info {
  font-size: 24rpx;
  color: #999;
}
.section-header .legend {
  display: flex;
}
.section-header .legend .legend-item {
  display: flex;
  align-items: center;
  margin-left: 20rpx;
}
.section-header .legend .legend-item .legend-color {
  width: 20rpx;
  height: 20rpx;
  border-radius: 4rpx;
  margin-right: 8rpx;
}
.section-header .legend .legend-item .legend-label {
  font-size: 24rpx;
  color: #666;
}
.nutrients-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
}
.nutrients-grid .nutrient-card {
  background-color: #f9f9f9;
  border-radius: 16rpx;
  padding: 20rpx;
  transition: all 0.3s ease;
}
.nutrients-grid .nutrient-card:active {
  -webkit-transform: scale(0.98);
          transform: scale(0.98);
}
.nutrients-grid .nutrient-card .nutrient-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}
.nutrients-grid .nutrient-card .nutrient-header .nutrient-label {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}
.nutrients-grid .nutrient-card .nutrient-header .nutrient-value {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}
.nutrients-grid .nutrient-card .progress-container {
  display: flex;
  align-items: center;
}
.nutrients-grid .nutrient-card .progress-container .progress-bar {
  flex: 1;
  height: 20rpx;
  background-color: #f0f0f0;
  border-radius: 10rpx;
  overflow: hidden;
  margin-right: 16rpx;
}
.nutrients-grid .nutrient-card .progress-container .progress-bar .progress {
  height: 100%;
  border-radius: 10rpx;
  transition: width 0.8s ease;
}
.nutrients-grid .nutrient-card .progress-container .percentage-text {
  font-size: 24rpx;
  color: #666;
  width: 60rpx;
  text-align: right;
}
.chart-container {
  height: 380rpx;
  padding-bottom: 20rpx;
}
.qiun-charts {
  width: 100%;
  height: 350rpx;
  z-index: 1;
}
.trend-chart {
  min-height: 450rpx;
}
.advice-content .advice-item {
  display: flex;
  margin-bottom: 30rpx;
}
.advice-content .advice-item .advice-icon {
  width: 60rpx;
  height: 60rpx;
  border-radius: 30rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 20rpx;
}
.advice-content .advice-item .advice-icon.info {
  background-color: #E3F2FD;
}
.advice-content .advice-item .advice-icon.warning {
  background-color: #FFF3E0;
}
.advice-content .advice-item .advice-icon.danger {
  background-color: #FFEBEE;
}
.advice-content .advice-item .advice-icon.success {
  background-color: #E8F5E9;
}
.advice-content .advice-item .advice-icon image {
  width: 36rpx;
  height: 36rpx;
}
.advice-content .advice-item .advice-text {
  flex: 1;
}
.advice-content .advice-item .advice-text .advice-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
  display: block;
}
.advice-content .advice-item .advice-text .advice-desc {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
}

