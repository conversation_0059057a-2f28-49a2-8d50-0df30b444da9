@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.diet-record {
  background-color: #f8f8f8;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  padding: 0 20rpx 20rpx;
}
.safe-area {
  height: 80rpx;
  /* 增加顶部安全区域高度 */
  background-color: #f8f8f8;
}
.header {
  padding: 0 30rpx;
  height: 88rpx;
  /* 与胶囊按钮高度一致 */
  display: flex;
  align-items: center;
  background-color: #f8f8f8;
}
.header .title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}
.page-content {
  flex: 1;
  box-sizing: border-box;
}
.date-selector {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx 30rpx;
  background-color: #ffffff;
  margin-bottom: 20rpx;
  border-radius: 10rpx;
}
.date-selector .date-actions {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  padding: 0;
}
.date-selector .date-actions .date-arrow {
  width: 40rpx;
  height: 40rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}
.date-selector .date-actions .date-arrow image {
  width: 24rpx;
  height: 24rpx;
}
.date-selector .date-actions .date-display {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 10rpx 0;
  max-width: 300rpx;
}
.date-selector .date-actions .date-display .date-title {
  font-size: 40rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}
.date-selector .date-actions .date-display .date-value {
  font-size: 28rpx;
  color: #999;
}
.meal-filter {
  display: flex;
  overflow-x: auto;
  padding: 15rpx 0;
  margin-bottom: 20rpx;
  white-space: nowrap;
}
.meal-filter::-webkit-scrollbar {
  display: none;
}
.meal-filter .filter-item {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  height: 68rpx;
  padding: 0 30rpx;
  background-color: #F5F5F5;
  border-radius: 34rpx;
  margin-right: 15rpx;
  transition: all 0.3s ease;
}
.meal-filter .filter-item text {
  font-size: 26rpx;
  color: #666666;
  transition: all 0.3s ease;
}
.meal-filter .filter-item.active {
  background-color: #4CAF50;
  box-shadow: 0 2rpx 8rpx rgba(76, 175, 80, 0.3);
}
.meal-filter .filter-item.active text {
  color: #FFFFFF;
  font-weight: bold;
}
.meal-filter .filter-item:active {
  -webkit-transform: scale(0.96);
          transform: scale(0.96);
  opacity: 0.9;
}
.add-food-card {
  background-color: #ffffff;
  border-radius: 16rpx;
  margin: 20rpx 0;
  padding: 6rpx;
  box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}
.add-food-card:active {
  -webkit-transform: scale(0.98);
          transform: scale(0.98);
  box-shadow: 0 2rpx 5rpx rgba(0, 0, 0, 0.1);
}
.add-food-card .add-food-content {
  display: flex;
  align-items: center;
  padding: 20rpx;
}
.add-food-card .add-food-content .add-food-icon {
  width: 80rpx;
  height: 80rpx;
  margin-right: 20rpx;
}
.add-food-card .add-food-content .add-food-icon image {
  width: 100%;
  height: 100%;
}
.add-food-card .add-food-content .add-food-info {
  flex: 1;
}
.add-food-card .add-food-content .add-food-info .add-food-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 8rpx;
}
.add-food-card .add-food-content .add-food-info .add-food-desc {
  margin-left: 10rpx;
  font-size: 24rpx;
  color: #999999;
}
.add-food-card .add-food-content .add-food-arrow {
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.add-food-card .add-food-content .add-food-arrow image {
  width: 24rpx;
  height: 24rpx;
}
.diet-list {
  margin-bottom: 20rpx;
}
.diet-list .loading-state {
  text-align: center;
  padding: 40rpx 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  min-height: 200rpx;
}
.diet-list .loading-state .loading-dots {
  display: flex;
  justify-content: center;
  margin-bottom: 15rpx;
}
.diet-list .loading-state .loading-dots .dot {
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  background-color: #4CAF50;
  margin: 0 8rpx;
  opacity: 0.6;
  -webkit-animation: dot-bounce 1.4s infinite ease-in-out both;
          animation: dot-bounce 1.4s infinite ease-in-out both;
}
.diet-list .loading-state .loading-dots .dot:nth-child(1) {
  -webkit-animation-delay: 0s;
          animation-delay: 0s;
}
.diet-list .loading-state .loading-dots .dot:nth-child(2) {
  -webkit-animation-delay: 0.2s;
          animation-delay: 0.2s;
}
.diet-list .loading-state .loading-dots .dot:nth-child(3) {
  -webkit-animation-delay: 0.4s;
          animation-delay: 0.4s;
}
.diet-list .loading-state text {
  font-size: 28rpx;
  color: #999;
}
.diet-list .empty-data-state {
  text-align: center;
  padding: 60rpx 0;
  color: #999;
  font-size: 28rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #ffffff;
  border-radius: 12rpx;
  margin: 20rpx 0;
  min-height: 300rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
}
.diet-list .empty-data-state .empty-icon {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 20rpx;
  opacity: 0.7;
}
.diet-list .empty-data-state text {
  font-size: 28rpx;
  color: #999;
}
.diet-list .meal-sections-container {
  width: 100%;
}
.diet-list .meal-sections-container .meal-section {
  background-color: #ffffff;
  margin-bottom: 30rpx;
  border-radius: 16rpx;
  overflow: hidden;
  padding: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
}
.diet-list .meal-sections-container .meal-section .meal-header {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
  padding: 0 10rpx;
}
.diet-list .meal-sections-container .meal-section .meal-header .meal-type {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
  margin-right: 20rpx;
  padding: 6rpx 0;
}
.diet-list .meal-sections-container .meal-section .meal-header .meal-info {
  flex: 1;
  display: flex;
  flex-direction: column;
}
.diet-list .meal-sections-container .meal-section .meal-header .meal-info .meal-calorie {
  font-size: 28rpx;
  color: #4CAF50;
  font-weight: bold;
  margin-bottom: 4rpx;
}
.diet-list .meal-sections-container .meal-section .meal-header .meal-info .meal-nutrition {
  display: flex;
  flex-wrap: wrap;
}
.diet-list .meal-sections-container .meal-section .meal-header .meal-info .meal-nutrition text {
  font-size: 22rpx;
  color: #999999;
  margin-right: 16rpx;
}
.diet-list .meal-sections-container .meal-section .meal-header .meal-action {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 30rpx;
  background-color: #4CAF50;
  margin-left: 10rpx;
  box-shadow: 0 2rpx 6rpx rgba(76, 175, 80, 0.3);
}
.diet-list .meal-sections-container .meal-section .meal-header .meal-action image {
  width: 32rpx;
  height: 32rpx;
  -webkit-filter: brightness(0) invert(1);
          filter: brightness(0) invert(1);
  /* 使图标变为白色 */
}
.diet-list .meal-sections-container .meal-section .meal-header .meal-action:active {
  background-color: #388E3C;
  -webkit-transform: scale(0.95);
          transform: scale(0.95);
}
.diet-list .meal-sections-container .meal-section .food-list .empty-tip {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 160rpx;
  background-color: #f9f9f9;
  border-radius: 12rpx;
  margin: 10rpx;
  box-shadow: 0 1rpx 3rpx rgba(0, 0, 0, 0.03);
  transition: all 0.3s ease;
}
.diet-list .meal-sections-container .meal-section .food-list .empty-tip .empty-food-icon {
  width: 64rpx;
  height: 64rpx;
  margin-bottom: 12rpx;
  opacity: 0.6;
}
.diet-list .meal-sections-container .meal-section .food-list .empty-tip text {
  font-size: 26rpx;
  color: #999999;
}
.diet-list .meal-sections-container .meal-section .food-list .food-item {
  display: flex;
  align-items: center;
  padding: 20rpx;
  background-color: #FFFFFF;
  border-radius: 12rpx;
  margin: 10rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
  transition: all 0.2s ease;
}
.diet-list .meal-sections-container .meal-section .food-list .food-item:active {
  -webkit-transform: scale(0.99);
          transform: scale(0.99);
  box-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.08);
}
.diet-list .meal-sections-container .meal-section .food-list .food-item .food-info {
  flex: 1;
}
.diet-list .meal-sections-container .meal-section .food-list .food-item .food-info .food-name {
  font-size: 28rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 8rpx;
}
.diet-list .meal-sections-container .meal-section .food-list .food-item .food-info .food-desc {
  font-size: 24rpx;
  color: #666666;
  margin-bottom: 10rpx;
}
.diet-list .meal-sections-container .meal-section .food-list .food-item .food-info .food-nutrition {
  display: flex;
  flex-wrap: wrap;
}
.diet-list .meal-sections-container .meal-section .food-list .food-item .food-info .food-nutrition .nutrition-item {
  font-size: 22rpx;
  color: #999999;
  margin-right: 16rpx;
  padding: 4rpx 12rpx;
  background-color: #F5F5F5;
  border-radius: 12rpx;
  margin-bottom: 4rpx;
}
.diet-list .meal-sections-container .meal-section .food-list .food-item .food-calorie {
  font-size: 28rpx;
  font-weight: bold;
  color: #4CAF50;
  padding-left: 20rpx;
}
@-webkit-keyframes dot-bounce {
0%, 80%, 100% {
    -webkit-transform: scale(0);
            transform: scale(0);
    opacity: 0.3;
}
40% {
    -webkit-transform: scale(1);
            transform: scale(1);
    opacity: 1;
}
}
@keyframes dot-bounce {
0%, 80%, 100% {
    -webkit-transform: scale(0);
            transform: scale(0);
    opacity: 0.3;
}
40% {
    -webkit-transform: scale(1);
            transform: scale(1);
    opacity: 1;
}
}
/* 添加淡入淡出过渡效果 */
.fade-enter-active, .fade-leave-active {
  transition: opacity 0.3s ease;
}
.fade-enter, .fade-leave-to {
  opacity: 0;
}

