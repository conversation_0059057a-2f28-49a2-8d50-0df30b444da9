<view class="settings"><view class="section"><view class="section-header"><text class="section-title">通知设置</text></view><view class="setting-item"><text class="setting-label">每日打卡提醒</text><switch checked="{{settings.dailyReminder}}" color="#4CAF50" data-event-opts="{{[['change',[['onSwitchChange',['$event','dailyReminder']]]]]}}" bindchange="__e"></switch></view><view class="setting-item"><text class="setting-label">营养摄入提醒</text><switch checked="{{settings.nutritionReminder}}" color="#4CAF50" data-event-opts="{{[['change',[['onSwitchChange',['$event','nutritionReminder']]]]]}}" bindchange="__e"></switch></view><view class="setting-item"><text class="setting-label">饮水提醒</text><switch checked="{{settings.waterReminder}}" color="#4CAF50" data-event-opts="{{[['change',[['onSwitchChange',['$event','waterReminder']]]]]}}" bindchange="__e"></switch></view></view><view class="section"><view class="section-header"><text class="section-title">隐私设置</text></view><view class="setting-item"><text class="setting-label">允许数据分析</text><switch checked="{{settings.allowDataAnalysis}}" color="#4CAF50" data-event-opts="{{[['change',[['onSwitchChange',['$event','allowDataAnalysis']]]]]}}" bindchange="__e"></switch></view><view class="setting-item"><text class="setting-label">允许推送个性化内容</text><switch checked="{{settings.allowPersonalization}}" color="#4CAF50" data-event-opts="{{[['change',[['onSwitchChange',['$event','allowPersonalization']]]]]}}" bindchange="__e"></switch></view></view><view class="section"><view class="section-header"><text class="section-title">应用设置</text></view><view class="setting-item"><text class="setting-label">清除缓存</text><view data-event-opts="{{[['tap',[['clearCache',['$event']]]]]}}" class="setting-action" bindtap="__e"><text>{{cacheSize}}</text><image src="/static/icons/arrow-right.png"></image></view></view><view class="setting-item"><text class="setting-label">检查更新</text><view data-event-opts="{{[['tap',[['checkUpdate',['$event']]]]]}}" class="setting-action" bindtap="__e"><text>当前版本 v1.0.0</text><image src="/static/icons/arrow-right.png"></image></view></view></view><view data-event-opts="{{[['tap',[['saveSettings',['$event']]]]]}}" class="save-btn" bindtap="__e"><text>保存设置</text></view></view>