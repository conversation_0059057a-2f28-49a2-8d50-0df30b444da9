@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.image-cropper.data-v-40f35364 {
  position: fixed;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  background-color: #000;
}
.image-cropper .img-canvas.data-v-40f35364 {
  position: absolute !important;
  -webkit-transform: translateX(-100%);
          transform: translateX(-100%);
}
.image-cropper .pic-preview.data-v-40f35364 {
  width: 100%;
  flex: 1;
  position: relative;
}
.image-cropper .pic-preview .crop-mask-block.data-v-40f35364 {
  background-color: rgba(51, 51, 51, 0.8);
  z-index: 2;
  position: fixed;
  box-sizing: border-box;
  pointer-events: none;
}
.image-cropper .pic-preview .crop-circle-box.data-v-40f35364 {
  position: fixed;
  box-sizing: border-box;
  z-index: 2;
  pointer-events: none;
  overflow: hidden;
}
.image-cropper .pic-preview .crop-circle-box .crop-circle.data-v-40f35364 {
  width: 100%;
  height: 100%;
}
.image-cropper .pic-preview .crop-image.data-v-40f35364 {
  padding: 0 !important;
  margin: 0 !important;
  border-radius: 0 !important;
  display: block !important;
  -webkit-backface-visibility: hidden;
          backface-visibility: hidden;
}
.image-cropper .pic-preview .crop-border.data-v-40f35364 {
  position: fixed;
  border: 1px solid #fff;
  box-sizing: border-box;
  z-index: 3;
  pointer-events: none;
}
.image-cropper .pic-preview .crop-grid.data-v-40f35364 {
  position: fixed;
  z-index: 3;
  border-style: dashed;
  border-color: #fff;
  pointer-events: none;
  opacity: 0.5;
}
.image-cropper .pic-preview .crop-angle.data-v-40f35364 {
  position: fixed;
  z-index: 3;
  border-style: solid;
  border-color: #fff;
  pointer-events: none;
}
.image-cropper .fixed-bottom.data-v-40f35364 {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 99;
  display: flex;
  flex-direction: row;
  background-color: #f8f8f8;
}
.image-cropper .fixed-bottom .action-bar.data-v-40f35364 {
  position: absolute;
  top: -90rpx;
  left: 10rpx;
  display: flex;
}
.image-cropper .fixed-bottom .action-bar .rotate-icon.data-v-40f35364 {
  background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAYAAABXAvmHAAAAAXNSR0IArs4c6QAABCFJREFUaEPtml3IpVMUx3//ko/ChTIyiGFSMyhllI8bc4F85yuNC2FCqLmQC1+FZORiEkUMNW7UjKjJULgxV+NzSkxDhEkZgwsyigv119J63p7zvOc8z37OmXdOb51dz82711r7/99r7bXXXucVi3xokeNnRqCvB20fDmwAlgK/5bcD+FTSr33tHXQP2H4MeHQE0A+B5yRtLiUyDQJrgVc6AAaBpyV93kXkoBMIQLbfBS5NcK8BRwDXNcD+AdwnaVMbiWkRCPBBohpxHuK7M7865sclRdgNHVMhkF6IMIpwirFEUhzo8M7lwIvASTXEqyVtH8ZgagQSbOzsDknv18HZXpHn5IL8+94IOUm7miSmSqAttjPdbgGuTrnNktYsGgLpoYuAD2qg1zRTbG8P2D4SOC6/Q7vSHPALsE/S7wWy80RsPw/ckxMfSTq/LtRJwPbxwF3ASiCUTxwHCPAnEBfVF8AWSTtL7Ng+LfWOTfmlkn6udFsJ5K15R6a4kvX6yGyUFBvTOWzHXXFzCt4g6c1OArYj9iIGh43YgR+BvztXh1PSa4cMkd0jaVmXDduPAE+k3HpJD7cSGFKvfAc8FQUX8IOk/V2L1udtB/hTgdOBW4Aba/M7Ja1qs2f7euCNlHlZUlx4/495IWQ7Jl+qGbxX0gt9AHfJ2o6zFBVoNVrDKe+F3Sm8VdK1bQQ+A85JgXckXdkFaJx527cC9TpnVdvBtl3h2iapuhsGPdBw1b9xnUvaNw7AEh3bnwDnpuwGSfeP0rN9NvAMELXRXFkxEEK2nwQeSiOtRVQJwC4Z29cAW1Nuu6TVXTrN+SaBt4ErUug2Sa/2NdhH3vZy4NvU2S/p6D768w5xI3WOrAD7LtISFpGdIhVXKfaYvjd20wP13L9M0p4DBbaFRKToSLExVkr6qs+aIwlI6iwz+izUQqC+ab29PiMwqRcmPXczD8w8MFj1zg7xXEqbpdHCw7FgWSjafZL+KcQxtpjteCeflwYulFR/J3TabSslVkj6utPChAK2f6q9uZdLitKieLQRuExSvX9ZbLRUMFs09efpUZL+KtUfVo1GW/umNHC3pOhRLtiwfSbwZS6wV9IJfRdreuBBYH0a2STp9r4G+8jbXgc8mzoDT8VSO00ClwDv1ZR7XyylC4ec7ejaLUmdsV6Aw7oSbwFXpdFdks7qA6pU1na0aR6owgeIR/1cx63UzjAC0YXYVjMQHlkn6ZtSo21ytuPZGKFagQ/xsXZ/3iGuFrYdjafXG0DiQMeBi47c9/GV3BO247UV38n5o0UAP6xmu7jFOGxjRr66On5NPBDOCBsDTapxjHY1dyOcolNXnYlx1himE53p2PmNkxosevfavhg4Izt2k7TXPwZ2S6p6QZPin/2rwcQ7OKmBohCadJGF1P8PG6aaQBKVX/8AAAAASUVORK5CYII=");
  background-size: 60% 60%;
  background-repeat: no-repeat;
  background-position: center;
  width: 80rpx;
  height: 80rpx;
}
.image-cropper .fixed-bottom .action-bar .rotate-icon.is-reverse.data-v-40f35364 {
  -webkit-transform: rotateY(180deg);
          transform: rotateY(180deg);
}
.image-cropper .fixed-bottom .rechoose.data-v-40f35364 {
  color: #007aff;
  padding: 0 15px;
  line-height: 100rpx;
}
.image-cropper .fixed-bottom .choose-btn.data-v-40f35364 {
  color: #007aff;
  text-align: center;
  line-height: 100rpx;
  flex: 1;
}
.image-cropper .fixed-bottom .button.data-v-40f35364 {
  margin: auto 15px auto auto;
  background-color: #007aff;
  color: #fff;
}
.image-cropper .safe-area-inset-bottom.data-v-40f35364 {
  padding-bottom: 0;
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}

