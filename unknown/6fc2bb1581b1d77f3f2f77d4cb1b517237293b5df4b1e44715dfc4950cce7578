
.container.data-v-e4cb3aec {
  width: 30px;
  height: 30px;
  position: relative;
}
.container.loading2.data-v-e4cb3aec {
  -webkit-transform: rotate(10deg);
          transform: rotate(10deg);
}
.container.loading2 .shape.data-v-e4cb3aec {
  border-radius: 5px;
}
.container.loading2.data-v-e4cb3aec{
  -webkit-animation: rotation 1s infinite;
          animation: rotation 1s infinite;
}
.container .shape.data-v-e4cb3aec {
  position: absolute;
  width: 10px;
  height: 10px;
  border-radius: 1px;
}
.container .shape.shape1.data-v-e4cb3aec {
  left: 0;
  background-color: #1890FF;
}
.container .shape.shape2.data-v-e4cb3aec {
  right: 0;
  background-color: #91CB74;
}
.container .shape.shape3.data-v-e4cb3aec {
  bottom: 0;
  background-color: #FAC858;
}
.container .shape.shape4.data-v-e4cb3aec {
  bottom: 0;
  right: 0;
  background-color: #EE6666;
}
.loading2 .shape1.data-v-e4cb3aec {
  -webkit-animation: animation2shape1-data-v-e4cb3aec 0.5s ease 0s infinite alternate;
          animation: animation2shape1-data-v-e4cb3aec 0.5s ease 0s infinite alternate;
}
@-webkit-keyframes animation2shape1-data-v-e4cb3aec {
from {
    -webkit-transform: translate(0, 0);
            transform: translate(0, 0);
}
to {
    -webkit-transform: translate(20px, 20px);
            transform: translate(20px, 20px);
}
}
@keyframes animation2shape1-data-v-e4cb3aec {
from {
    -webkit-transform: translate(0, 0);
            transform: translate(0, 0);
}
to {
    -webkit-transform: translate(20px, 20px);
            transform: translate(20px, 20px);
}
}
.loading2 .shape2.data-v-e4cb3aec {
  -webkit-animation: animation2shape2-data-v-e4cb3aec 0.5s ease 0s infinite alternate;
          animation: animation2shape2-data-v-e4cb3aec 0.5s ease 0s infinite alternate;
}
@-webkit-keyframes animation2shape2-data-v-e4cb3aec {
from {
    -webkit-transform: translate(0, 0);
            transform: translate(0, 0);
}
to {
    -webkit-transform: translate(-20px, 20px);
            transform: translate(-20px, 20px);
}
}
@keyframes animation2shape2-data-v-e4cb3aec {
from {
    -webkit-transform: translate(0, 0);
            transform: translate(0, 0);
}
to {
    -webkit-transform: translate(-20px, 20px);
            transform: translate(-20px, 20px);
}
}
.loading2 .shape3.data-v-e4cb3aec {
  -webkit-animation: animation2shape3-data-v-e4cb3aec 0.5s ease 0s infinite alternate;
          animation: animation2shape3-data-v-e4cb3aec 0.5s ease 0s infinite alternate;
}
@-webkit-keyframes animation2shape3-data-v-e4cb3aec {
from {
    -webkit-transform: translate(0, 0);
            transform: translate(0, 0);
}
to {
    -webkit-transform: translate(20px, -20px);
            transform: translate(20px, -20px);
}
}
@keyframes animation2shape3-data-v-e4cb3aec {
from {
    -webkit-transform: translate(0, 0);
            transform: translate(0, 0);
}
to {
    -webkit-transform: translate(20px, -20px);
            transform: translate(20px, -20px);
}
}
.loading2 .shape4.data-v-e4cb3aec {
  -webkit-animation: animation2shape4-data-v-e4cb3aec 0.5s ease 0s infinite alternate;
          animation: animation2shape4-data-v-e4cb3aec 0.5s ease 0s infinite alternate;
}
@-webkit-keyframes animation2shape4-data-v-e4cb3aec {
from {
    -webkit-transform: translate(0, 0);
            transform: translate(0, 0);
}
to {
    -webkit-transform: translate(-20px, -20px);
            transform: translate(-20px, -20px);
}
}
@keyframes animation2shape4-data-v-e4cb3aec {
from {
    -webkit-transform: translate(0, 0);
            transform: translate(0, 0);
}
to {
    -webkit-transform: translate(-20px, -20px);
            transform: translate(-20px, -20px);
}
}


