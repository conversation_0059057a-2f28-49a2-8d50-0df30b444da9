<view class="add-diet"><view class="safe-area"></view><view class="header"><view data-event-opts="{{[['tap',[['navigateBack']]]]}}" class="back-btn" bindtap="__e"><image src="/static/icons/back.png"></image></view><text class="title">添加饮食记录</text></view><view class="form-section"><view class="meal-selector"><block wx:for="{{mealTypes}}" wx:for-item="meal" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" data-event-params="{{({meal})}}" class="{{['meal-option',(selectedMeal===meal.key)?'active':'']}}" bindtap="__e"><text>{{meal.name}}</text></view></block></view><view class="form-item"><text class="form-label">日期</text><view class="form-input"><picker mode="date" value="{{selectedDate}}" data-event-opts="{{[['change',[['onDateChange',['$event']]]]]}}" bindchange="__e"><view class="picker-value"><text>{{selectedDate}}</text><image src="/static/icons/calendar.png"></image></view></picker></view></view><view class="form-item"><text class="form-label">时间</text><view class="form-input"><picker mode="time" value="{{selectedTime}}" data-event-opts="{{[['change',[['onTimeChange',['$event']]]]]}}" bindchange="__e"><view class="picker-value"><text>{{selectedTime}}</text><image src="/static/icons/clock.png"></image></view></picker></view></view><view class="food-list"><view class="section-header"><text class="section-title">食物列表</text><view data-event-opts="{{[['tap',[['showFoodSearch',['$event']]]]]}}" class="add-food-btn" bindtap="__e"><image src="/static/icons/add-food.png"></image><text>添加食物</text></view></view><block wx:if="{{$root.g0===0}}"><view class="empty-tip"><image src="/static/icons/empty.png"></image><text>暂无食物，请添加</text></view></block><block wx:for="{{$root.l0}}" wx:for-item="food" wx:for-index="index" wx:key="index"><view class="food-item"><view class="food-info"><text class="food-name">{{food.$orig.name}}</text><text class="food-desc">{{food.$orig.unitDisplay}}</text></view><view class="food-amount"><view class="amount-control"><view data-event-opts="{{[['tap',[['decreaseAmount',[index]]]]]}}" class="minus-btn" bindtap="__e"><text>-</text></view><input class="amount-input" type="number" data-event-opts="{{[['input',[['__set_model',['$0','amount','$event',[]],[[['selectedFoods','',index]]]],['recalculateNutrition',[index]]]]]}}" value="{{food.$orig.amount}}" bindinput="__e"/><view data-event-opts="{{[['tap',[['increaseAmount',[index]]]]]}}" class="plus-btn" bindtap="__e"><text>+</text></view></view></view><view class="food-nutrition-info"><text class="food-calorie">{{food.g1+"千卡"}}</text><view class="nutrition-detail"><text>{{"蛋白质:"+food.g2+"g"}}</text><text>{{"脂肪:"+food.g3+"g"}}</text><text>{{"碳水:"+food.g4+"g"}}</text></view></view><view data-event-opts="{{[['tap',[['removeFood',[index]]]]]}}" class="delete-btn" bindtap="__e"><image src="/static/icons/delete.png"></image></view></view></block></view><view class="form-item"><text class="form-label">备注</text><view class="form-input"><textarea placeholder="添加备注信息" data-event-opts="{{[['input',[['__set_model',['','remark','$event',[]]]]]]}}" value="{{remark}}" bindinput="__e"></textarea></view></view></view><view class="bottom-bar"><view class="total-calorie"><text>{{"总热量: "+totalCalorie+"千卡"}}</text></view><view data-event-opts="{{[['tap',[['saveDietRecord',['$event']]]]]}}" class="save-btn" bindtap="__e"><text>保存记录</text></view></view><block wx:if="{{showFoodModal}}"><view class="food-search-modal"><view data-event-opts="{{[['tap',[['hideFoodSearch',['$event']]]]]}}" class="modal-mask" bindtap="__e"></view><view class="modal-content"><view class="modal-header"><text class="modal-title">添加食物</text><view data-event-opts="{{[['tap',[['hideFoodSearch',['$event']]]]]}}" class="close-btn" bindtap="__e"><image src="/static/icons/close.png"></image></view></view><view class="search-box"><image src="/static/icons/search.png"></image><input type="text" placeholder="搜索食物" data-event-opts="{{[['input',[['__set_model',['','searchKeyword','$event',[]]],['searchFood',['$event']]]]]}}" value="{{searchKeyword}}" bindinput="__e"/></view><scroll-view class="category-scroll" scroll-x="{{true}}" show-scrollbar="{{false}}"><view data-event-opts="{{[['tap',[['selectCategory',[null]]]]]}}" class="{{['category-item',(selectedCategoryId===undefined)?'active':'']}}" bindtap="__e"><text>全部</text></view><block wx:for="{{foodCategories}}" wx:for-item="category" wx:for-index="__i0__" wx:key="id"><view data-event-opts="{{[['tap',[['selectCategory',['$0'],[[['foodCategories','id',category.id,'id']]]]]]]}}" class="{{['category-item',(selectedCategoryId===category.id)?'active':'']}}" bindtap="__e"><text>{{category.name}}</text></view></block></scroll-view><scroll-view class="food-scroll" scroll-y="{{true}}" lower-threshold="{{50}}" data-event-opts="{{[['scrolltolower',[['loadMoreFoods',['$event']]]]]}}" bindscrolltolower="__e"><view class="food-list-container"><block wx:for="{{searchResults}}" wx:for-item="food" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['showFoodSelectionPanel',['$0'],[[['searchResults','',index]]]]]]]}}" class="food-card" bindtap="__e"><view class="food-card-main"><view class="food-info"><text class="food-name">{{food.name}}</text></view><view class="food-nutrition"><text class="food-calorie">{{food.calories+"千卡"}}</text><text class="food-unit">{{food.measure}}</text></view></view><view class="food-detail"><view class="nutrition-item"><text class="nutrition-value">{{food.protein+"g"}}</text><text class="nutrition-label">蛋白质</text></view><view class="nutrition-item"><text class="nutrition-value">{{food.fat+"g"}}</text><text class="nutrition-label">脂肪</text></view><view class="nutrition-item"><text class="nutrition-value">{{food.carbs+"g"}}</text><text class="nutrition-label">碳水</text></view></view></view></block><block wx:if="{{isLoadingMore}}"><view class="loading-state"><view class="loading-dots"><view class="dot"></view><view class="dot"></view><view class="dot"></view></view><text>正在加载更多美食...</text></view></block><block wx:if="{{$root.g5}}"><view class="all-loaded-state"><view class="divider-line"></view><view class="all-loaded-content"><image class="end-icon" src="/static/icons/empty.png"></image><text>已经到底啦，没有更多食物了</text></view></view></block><block wx:if="{{$root.g6}}"><view class="empty-tip"><image src="/static/icons/empty.png"></image><text>未找到相关食物</text></view></block></view></scroll-view></view></view></block><block wx:if="{{showSelectionPanel}}"><view class="food-selection-panel"><view data-event-opts="{{[['tap',[['hideSelectionPanel',['$event']]]]]}}" class="modal-mask" bindtap="__e"></view><view class="selection-content"><view class="selection-header"><text class="selection-title">{{currentFood.name}}</text><view data-event-opts="{{[['tap',[['hideSelectionPanel',['$event']]]]]}}" class="close-btn" bindtap="__e"><image src="/static/icons/close.png"></image></view></view><view class="selection-tabs"><view data-event-opts="{{[['tap',[['e1',['$event']]]]]}}" class="{{['tab-item',(selectionMode==='unit')?'active':'']}}" bindtap="__e"><text>按单位添加</text></view><view data-event-opts="{{[['tap',[['e2',['$event']]]]]}}" class="{{['tab-item',(selectionMode==='grams')?'active':'']}}" bindtap="__e"><text>按克数添加</text></view></view><view class="selection-body"><block wx:if="{{selectionMode==='unit'}}"><view class="unit-selection"><view class="food-measure"><text>{{currentFood.measure+"（约"+currentFood.grams+"克）"}}</text></view><view class="amount-control"><view data-event-opts="{{[['tap',[['decreaseUnitAmount',['$event']]]]]}}" class="control-btn minus" bindtap="__e"><text>-</text></view><input class="amount-input" type="number" data-event-opts="{{[['input',[['__set_model',['','unitAmount','$event',[]]]]]]}}" value="{{unitAmount}}" bindinput="__e"/><view data-event-opts="{{[['tap',[['increaseUnitAmount',['$event']]]]]}}" class="control-btn plus" bindtap="__e"><text>+</text></view></view><view class="nutrition-preview"><view class="preview-item"><text class="preview-label">热量</text><text class="preview-value">{{$root.m0+"千卡"}}</text></view><view class="preview-item"><text class="preview-label">蛋白质</text><text class="preview-value">{{$root.m1+"g"}}</text></view><view class="preview-item"><text class="preview-label">脂肪</text><text class="preview-value">{{$root.m2+"g"}}</text></view><view class="preview-item"><text class="preview-label">碳水</text><text class="preview-value">{{$root.m3+"g"}}</text></view></view></view></block><block wx:if="{{selectionMode==='grams'}}"><view class="grams-selection"><view class="grams-input-container"><input class="grams-input" type="number" data-event-opts="{{[['input',[['__set_model',['','customGrams','$event',[]]],['updateNutritionByGrams',['$event']]]]]}}" value="{{customGrams}}" bindinput="__e"/><text class="grams-label">克</text></view><view class="nutrition-preview"><view class="preview-item"><text class="preview-label">热量</text><text class="preview-value">{{$root.m4+"千卡"}}</text></view><view class="preview-item"><text class="preview-label">蛋白质</text><text class="preview-value">{{$root.m5+"g"}}</text></view><view class="preview-item"><text class="preview-label">脂肪</text><text class="preview-value">{{$root.m6+"g"}}</text></view><view class="preview-item"><text class="preview-label">碳水</text><text class="preview-value">{{$root.m7+"g"}}</text></view></view></view></block></view><view class="selection-footer"><view data-event-opts="{{[['tap',[['hideSelectionPanel',['$event']]]]]}}" class="cancel-btn" bindtap="__e"><text>取消</text></view><view data-event-opts="{{[['tap',[['confirmAddFood',['$event']]]]]}}" class="confirm-btn" bindtap="__e"><text>添加</text></view></view></view></view></block></view>