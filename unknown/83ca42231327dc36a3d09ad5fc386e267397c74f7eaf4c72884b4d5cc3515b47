{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/HBuilderProjects/springboot-dubbo-front-wechat/pages/index/index.vue?9bc1", "webpack:///D:/HBuilderProjects/springboot-dubbo-front-wechat/pages/index/index.vue?4150", "webpack:///D:/HBuilderProjects/springboot-dubbo-front-wechat/pages/index/index.vue?5691", "webpack:///D:/HBuilderProjects/springboot-dubbo-front-wechat/pages/index/index.vue?8a2e", "uni-app:///pages/index/index.vue", "webpack:///D:/HBuilderProjects/springboot-dubbo-front-wechat/pages/index/index.vue?7862", "webpack:///D:/HBuilderProjects/springboot-dubbo-front-wechat/pages/index/index.vue?f500"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "NutritionCircle", "data", "currentDate", "lastDataFetchTime", "dataCacheTime", "computed", "userInfo", "nutritionData", "nutritionAdviceList", "dataChanged", "completionRate", "caloriePercentage", "calorieTarget", "currentCalorie", "nutritionAdvice", "onLoad", "date", "onShow", "console", "onPullDownRefresh", "uni", "title", "icon", "duration", "methods", "fetchNutritionData", "fetchNutritionAdvice", "refreshData", "then", "navigateTo", "url"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkH;AAClH;AACyD;AACL;AACc;;;AAGlE;AACmK;AACnK,gBAAgB,6KAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,gFAAM;AACR,EAAE,yFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjBA;AAAA;AAAA;AAAA;AAAkmB,CAAgB,+mBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;AC2GtnB;AACA;AAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA,eAEA;EACAC;IACAC;EACA;EACAC;IACA;MACAC;MACA;MACAC;MACA;MACAC;IACA;EACA;EACAC,wDACA;IACAC;MAAA;IAAA;EACA,KACA;IACAC;IACAC;IAAA;IACAC;EACA;IACAC;MACA;MACA;MACA;IACA;IACAC;MACA;MACA;QACA;MACA;MACA;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;QACA;MACA;MACA;IACA;EAAA,EACA;EACAC;IACA;IACA;IACA;IACA;MAAAC;IAAA;IACA;MAAAA;IAAA;EACA;EACA;EACAC;IACA;IACA;;IAEA;IACA;MACA;MACAC;MACA;IACA;MACA;MACAA;IACA;EACA;EACA;EACAC;IACAD;IACA;IACA;MACA;MACAE;MACA;MACAA;QACAC;QACAC;QACAC;MACA;IACA;EACA;EACAC,yCACA;IACAC;IACAC;EACA;IACA;IACAC;MAAA;MACA;MACA;MACA,oBACA;QAAAX;MAAA,IACA;QAAAA;MAAA,GACA,EACAY;QACA;QACA;;QAEA;QACA;UACA;QACA;MACA;IACA;IACAC;MACAT;QACAU;MACA;IACA;EAAA;AAEA;AAAA,2B;;;;;;;;;;;;;ACjOA;AAAA;AAAA;AAAA;AAA6oC,CAAgB,8lCAAG,EAAC,C;;;;;;;;;;;ACAjqC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/index/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/index/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=57280228&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/index/index.vue\"\nexport default component.exports", "export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=57280228&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.nutritionAdviceList && _vm.nutritionAdviceList.length > 0\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"dashboard\">\n\t\t<!-- 顶部安全区域 -->\n\t\t<view class=\"safe-area\"></view>\n\n\t\t<!-- 顶部标题和已完成状态 -->\n\t\t<view class=\"header\">\n\t\t\t<view class=\"left-section\">\n\t\t\t\t<text class=\"title\">首页</text>\n\t\t\t</view>\n\t\t\t<view class=\"center-section\">\n\t\t\t\t<view class=\"completed-status\">\n\t\t\t\t\t<text class=\"progress\">已完成 {{ completionRate || 0 }}%</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t<view class=\"right-section\"></view>\n\t\t</view>\n\n\t\t<!-- 日期显示 -->\n\t\t<view class=\"date-info\">\n\t\t\t<text class=\"date\">{{ currentDate }}</text>\n\t\t</view>\n\n\t\t<!-- 营养素圆环 - 2x2布局 -->\n\t\t<view class=\"nutrition-progress-container\">\n\t\t\t<view class=\"progress-row\">\n\t\t\t\t<view class=\"progress-item\">\n\t\t\t\t\t<nutrition-circle\n\t\t\t\t\t\t:percentage=\"nutritionData && nutritionData.caloriePercentage || 0\"\n\t\t\t\t\t\t:target=\"calorieTarget\"\n\t\t\t\t\t\t:current=\"nutritionData && nutritionData.calorie || 0\"\n\t\t\t\t\t\tcolor=\"#FF7043\"\n\t\t\t\t\t\tlabel=\"热量\"\n\t\t\t\t\t/>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"progress-item\">\n\t\t\t\t\t<nutrition-circle\n\t\t\t\t\t\t:percentage=\"nutritionData && nutritionData.proteinPercentage || 0\"\n\t\t\t\t\t\t:target=\"userInfo.nutritionGoals && userInfo.nutritionGoals.protein || 88\"\n\t\t\t\t\t\t:current=\"nutritionData && nutritionData.protein || 0\"\n\t\t\t\t\t\tcolor=\"#5C6BC0\"\n\t\t\t\t\t\tlabel=\"蛋白质\"\n\t\t\t\t\t/>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t<view class=\"progress-row\">\n\t\t\t\t<view class=\"progress-item\">\n\t\t\t\t\t<nutrition-circle\n\t\t\t\t\t\t:percentage=\"nutritionData && nutritionData.carbsPercentage || 0\"\n\t\t\t\t\t\t:target=\"userInfo.nutritionGoals && userInfo.nutritionGoals.carbs || 300\"\n\t\t\t\t\t\t:current=\"nutritionData && nutritionData.carbs || 0\"\n\t\t\t\t\t\tcolor=\"#26A69A\"\n\t\t\t\t\t\tlabel=\"碳水\"\n\t\t\t\t\t/>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"progress-item\">\n\t\t\t\t\t<nutrition-circle\n\t\t\t\t\t\t:percentage=\"nutritionData && nutritionData.fatPercentage || 0\"\n\t\t\t\t\t\t:target=\"userInfo.nutritionGoals && userInfo.nutritionGoals.fat || 70\"\n\t\t\t\t\t\t:current=\"nutritionData && nutritionData.fat || 0\"\n\t\t\t\t\t\tcolor=\"#EC407A\"\n\t\t\t\t\t\tlabel=\"脂肪\"\n\t\t\t\t\t/>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 快速入口 -->\n\t\t<view class=\"quick-actions\">\n\t\t\t<view class=\"action-item\" @click=\"navigateTo('/pages/diet-record/add')\">\n\t\t\t\t<view class=\"action-icon\">\n\t\t\t\t\t<image src=\"/static/icons/add-food.png\"></image>\n\t\t\t\t</view>\n\t\t\t\t<text>记录饮食</text>\n\t\t\t</view>\n\t\t\t<view class=\"action-item\" @click=\"navigateTo('/pages/health-report/index')\">\n\t\t\t\t<view class=\"action-icon\">\n\t\t\t\t\t<image src=\"/static/icons/analysis.png\"></image>\n\t\t\t\t</view>\n\t\t\t\t<text>查看报告</text>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 营养建议 -->\n\t\t<view class=\"nutrition-advice\">\n\t\t\t<view class=\"advice-header\">\n\t\t\t\t<text class=\"advice-title\">营养建议</text>\n\t\t\t</view>\n\t\t\t<view class=\"advice-content\">\n\t\t\t\t<!-- 如果有多条建议，显示列表 -->\n\t\t\t\t<view v-if=\"nutritionAdviceList && nutritionAdviceList.length > 0\" class=\"advice-list\">\n\t\t\t\t\t<view class=\"advice-item\" v-for=\"(item, index) in nutritionAdviceList\" :key=\"index\" :class=\"item.type\">\n\t\t\t\t\t\t<text class=\"advice-item-title\">{{ item.title }}</text>\n\t\t\t\t\t\t<text class=\"advice-item-desc\">{{ item.description }}</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<!-- 如果没有建议，显示默认文本 -->\n\t\t\t\t<text v-else>{{ nutritionAdvice }}</text>\n\t\t\t</view>\n\t\t</view>\n\n\n\t</view>\n</template>\n\n<script>\nimport NutritionCircle from '@/components/common/NutritionCircle.vue'\nimport { formatDate } from '@/utils/date.js'\nimport { mapState, mapActions, mapGetters } from 'vuex'\n\nexport default {\n\tcomponents: {\n\t\tNutritionCircle\n\t},\n\tdata() {\n\t\treturn {\n\t\t\tcurrentDate: '',\n\t\t\t// 数据缓存时间戳，用于判断是否需要重新获取数据\n\t\t\tlastDataFetchTime: 0,\n\t\t\t// 缓存刷新间隔，单位为毫秒（默认2分钟，首页可以设置短一些）\n\t\t\tdataCacheTime: 2 * 60 * 1000\n\t\t}\n\t},\n\tcomputed: {\n\t\t...mapState({\n\t\t\tuserInfo: state => state.user.userInfo,\n\t\t}),\n\t\t...mapGetters({\n\t\t\tnutritionData: 'nutrition/nutritionData',\n\t\t\tnutritionAdviceList: 'nutrition/nutritionAdvice', // 添加营养建议getter\n\t\t\tdataChanged: 'nutrition/dataChanged' // 添加数据变更标志\n\t\t}),\n\t\tcompletionRate() {\n\t\t\tconst calorieTarget = this.calorieTarget || 1;\n\t\t\tconst currentCalorie = this.currentCalorie || 0;\n\t\t\treturn Math.round((currentCalorie / calorieTarget) * 100) || 0;\n\t\t},\n\t\tcaloriePercentage() {\n\t\t\t// 优先使用nutritionData中的caloriePercentage，如果有的话\n\t\t\tif (this.nutritionData && this.nutritionData.caloriePercentage !== undefined) {\n\t\t\t\treturn Math.min(this.nutritionData.caloriePercentage || 0, 100);\n\t\t\t}\n\t\t\t// 回退到自己计算的比例\n\t\t\treturn Math.min(this.completionRate || 0, 100);\n\t\t},\n\t\tcalorieTarget() {\n\t\t\treturn this.userInfo.calorieTarget || 2200\n\t\t},\n\t\tcurrentCalorie() {\n\t\t\treturn this.nutritionData && this.nutritionData.calorie || 0\n\t\t},\n\t\t// 获取第一条营养建议的描述，如果没有则显示默认文本\n\t\tnutritionAdvice() {\n\t\t\tif (this.nutritionAdviceList && this.nutritionAdviceList.length > 0) {\n\t\t\t\treturn this.nutritionAdviceList[0].description\n\t\t\t}\n\t\t\treturn '保持均衡饮食，多吃蔬果，适量运动'\n\t\t}\n\t},\n\tonLoad() {\n\t\tthis.currentDate = formatDate(new Date(), 'M月d日')\n\t\tconst today = formatDate(new Date(), 'yyyy-MM-dd')\n\t\t// 同时获取营养数据和营养建议\n\t\tthis.fetchNutritionData({ date: today })\n\t\tthis.fetchNutritionAdvice({ date: today })\n\t},\n\t// 添加onShow生命周期，当从其他页面返回时更新数据\n\tonShow() {\n\t\tconst currentTime = Date.now()\n\t\tconst timeSinceLastFetch = currentTime - this.lastDataFetchTime\n\n\t\t// 检查是否需要刷新数据：首次加载、缓存过期或数据已变更\n\t\tif (this.lastDataFetchTime === 0 || timeSinceLastFetch > this.dataCacheTime || this.dataChanged) {\n\t\t\t// 如果是首次加载、缓存已过期或数据已变更，则重新获取数据\n\t\t\tconsole.log('首页：刷新数据', this.dataChanged ? '（数据已变更）' : '（缓存过期）')\n\t\t\tthis.refreshData()\n\t\t} else {\n\t\t\t// 缓存有效，不重新请求数据\n\t\t\tconsole.log('首页：使用缓存数据，跳过网络请求')\n\t\t}\n\t},\n\t// 添加下拉刷新处理方法\n\tonPullDownRefresh() {\n\t\tconsole.log('首页：下拉刷新')\n\t\t// 刷新数据\n\t\tthis.refreshData().then(() => {\n\t\t\t// 停止下拉刷新动画\n\t\t\tuni.stopPullDownRefresh()\n\t\t\t// 显示刷新成功提示\n\t\t\tuni.showToast({\n\t\t\t\ttitle: '刷新成功',\n\t\t\t\ticon: 'success',\n\t\t\t\tduration: 1000\n\t\t\t})\n\t\t})\n\t},\n\tmethods: {\n\t\t...mapActions({\n\t\t\tfetchNutritionData: 'nutrition/fetchNutritionData',\n\t\t\tfetchNutritionAdvice: 'nutrition/fetchNutritionAdvice'\n\t\t}),\n\t\t// 刷新数据的统一方法\n\t\trefreshData() {\n\t\t\tconst today = formatDate(new Date(), 'yyyy-MM-dd')\n\t\t\t// 返回Promise以便链式调用\n\t\t\treturn Promise.all([\n\t\t\t\tthis.fetchNutritionData({ date: today }),\n\t\t\t\tthis.fetchNutritionAdvice({ date: today })\n\t\t\t])\n\t\t\t\t.then(() => {\n\t\t\t\t\t// 更新数据缓存时间戳\n\t\t\t\t\tthis.lastDataFetchTime = Date.now()\n\n\t\t\t\t\t// 重置数据变更标志\n\t\t\t\t\tif (this.dataChanged) {\n\t\t\t\t\t\tthis.$store.dispatch('nutrition/setDataChanged', false)\n\t\t\t\t\t}\n\t\t\t\t})\n\t\t},\n\t\tnavigateTo(url) {\n\t\t\tuni.navigateTo({\n\t\t\t\turl\n\t\t\t})\n\t\t}\n\t}\n}\n</script>\n\n<style lang=\"scss\">\n.dashboard {\n\tpadding: 0 20rpx 20rpx;\n\tbackground-color: #4CAF50;\n\tbackground-image: linear-gradient(to bottom, #4CAF50, #43A047);\n\tmin-height: 100vh;\n}\n\n.safe-area {\n\theight: 80rpx; /* 增加顶部安全区域高度 */\n\tbackground-color: #4CAF50;\n}\n\n.header {\n\tpadding: 0 30rpx;\n\tdisplay: flex;\n\talign-items: center;\n\theight: 88rpx;  /* 与胶囊按钮高度一致 */\n\tmargin-bottom: 10rpx;\n\n\t.left-section {\n\t\twidth: 120rpx;\n\n\t\t.title {\n\t\t\tfont-size: 36rpx;\n\t\t\tfont-weight: bold;\n\t\t\tcolor: #ffffff;\n\t\t\ttext-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);\n\t\t}\n\t}\n\n\t.center-section {\n\t\tflex: 1;\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\n\t\t.completed-status {\n\t\t\theight: 60rpx;\n\t\t\tpadding: 0 20rpx;\n\t\t\tbackground-color: rgba(255, 255, 255, 0.15);\n\t\t\tborder-radius: 30rpx;\n\t\t\tdisplay: flex;\n\t\t\talign-items: center;\n\t\t\tjustify-content: center;\n\t\t\tbox-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);\n\n\t\t\t.progress {\n\t\t\t\tfont-size: 26rpx;\n\t\t\t\tcolor: #ffffff;\n\t\t\t\ttext-shadow: 0 1px 1px rgba(0, 0, 0, 0.1);\n\t\t\t}\n\t\t}\n\t}\n\n\t.right-section {\n\t\twidth: 120rpx;\n\t}\n}\n\n.date-info {\n\tpadding: 0 30rpx;\n\tmargin-bottom: 15rpx;\n\n\t.date {\n\t\tfont-size: 30rpx;\n\t\tcolor: #ffffff;\n\t\topacity: 0.95;\n\t\ttext-shadow: 0 1px 1px rgba(0, 0, 0, 0.1);\n\t}\n}\n\n.nutrition-progress-container {\n\tmargin: 20rpx 0 30rpx;\n\tpadding: 0 20rpx;\n\n\t.progress-row {\n\t\tdisplay: flex;\n\t\tjustify-content: space-between;\n\t\tmargin-bottom: 25rpx;\n\n\t\t&:last-child {\n\t\t\tmargin-bottom: 0;\n\t\t}\n\n\t\t.progress-item {\n\t\t\twidth: 48%; /* 每个项目占用行宽的48%，留出间距 */\n\t\t\tbackground-color: rgba(0, 0, 0, 0.06);\n\t\t\tborder-radius: 16rpx;\n\t\t\tpadding: 20rpx 0;\n\t\t\tbox-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);\n\t\t}\n\t}\n}\n\n.quick-actions {\n\tdisplay: flex;\n\tjustify-content: space-around;\n\tmargin: 40rpx 0;\n\n\t.action-item {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\talign-items: center;\n\t\tbackground-color: rgba(255, 255, 255, 0.15);\n\t\tborder-radius: 16rpx;\n\t\tpadding: 24rpx 15rpx;\n\t\twidth: 200rpx;\n\t\tbox-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);\n\t\ttransition: all 0.3s ease;\n\t\tborder: 1rpx solid rgba(255, 255, 255, 0.2);\n\n\t\t.action-icon {\n\t\t\twidth: 80rpx;\n\t\t\theight: 80rpx;\n\t\t\tmargin-bottom: 15rpx;\n\t\t\tbackground-color: rgba(255, 255, 255, 0.25);\n\t\t\tborder-radius: 50%;\n\t\t\tdisplay: flex;\n\t\t\tjustify-content: center;\n\t\t\talign-items: center;\n\t\t\tbox-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);\n\n\t\t\timage {\n\t\t\t\twidth: 50rpx;\n\t\t\t\theight: 50rpx;\n\t\t\t}\n\t\t}\n\n\t\ttext {\n\t\t\tfont-size: 28rpx;\n\t\t\tcolor: #ffffff;\n\t\t\tfont-weight: 500;\n\t\t\ttext-shadow: 0 1px 1px rgba(0, 0, 0, 0.1);\n\t\t}\n\n\t\t&:active {\n\t\t\ttransform: scale(0.96);\n\t\t\tbox-shadow: 0 2rpx 5rpx rgba(0, 0, 0, 0.1);\n\t\t}\n\t}\n}\n\n.nutrition-advice {\n\tbackground-color: #ffffff;\n\tborder-radius: 16rpx;\n\tpadding: 25rpx;\n\tmargin-bottom: 30rpx;\n\tbox-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);\n\n\t.advice-header {\n\t\tmargin-bottom: 15rpx;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\n\t\t.advice-title {\n\t\t\tfont-size: 32rpx;\n\t\t\tfont-weight: bold;\n\t\t\tcolor: #333333;\n\t\t\tposition: relative;\n\t\t\tpadding-left: 20rpx;\n\n\t\t\t&::before {\n\t\t\t\tcontent: '';\n\t\t\t\tposition: absolute;\n\t\t\t\tleft: 0;\n\t\t\t\ttop: 50%;\n\t\t\t\ttransform: translateY(-50%);\n\t\t\t\twidth: 8rpx;\n\t\t\t\theight: 28rpx;\n\t\t\t\tbackground-color: #4CAF50;\n\t\t\t\tborder-radius: 4rpx;\n\t\t\t}\n\t\t}\n\t}\n\n\t.advice-content {\n\t\tpadding: 10rpx 0;\n\n\t\ttext {\n\t\t\tfont-size: 28rpx;\n\t\t\tcolor: #666666;\n\t\t\tline-height: 1.6;\n\t\t}\n\n\t\t.advice-list {\n\t\t\t.advice-item {\n\t\t\t\tmargin-bottom: 20rpx;\n\t\t\t\tpadding: 15rpx;\n\t\t\t\tborder-radius: 10rpx;\n\t\t\t\tbackground-color: #f9f9f9;\n\t\t\t\tborder-left: 4rpx solid #4CAF50;\n\n\t\t\t\t&:last-child {\n\t\t\t\t\tmargin-bottom: 0;\n\t\t\t\t}\n\n\t\t\t\t&.warning {\n\t\t\t\t\tborder-left-color: #FF9800;\n\t\t\t\t}\n\n\t\t\t\t&.danger {\n\t\t\t\t\tborder-left-color: #F44336;\n\t\t\t\t}\n\n\t\t\t\t&.success {\n\t\t\t\t\tborder-left-color: #4CAF50;\n\t\t\t\t}\n\n\t\t\t\t&.info {\n\t\t\t\t\tborder-left-color: #2196F3;\n\t\t\t\t}\n\n\t\t\t\t.advice-item-title {\n\t\t\t\t\tfont-size: 28rpx;\n\t\t\t\t\tfont-weight: bold;\n\t\t\t\t\tcolor: #333333;\n\t\t\t\t\tmargin-bottom: 8rpx;\n\t\t\t\t\tdisplay: block;\n\t\t\t\t}\n\n\t\t\t\t.advice-item-desc {\n\t\t\t\t\tfont-size: 26rpx;\n\t\t\t\t\tcolor: #666666;\n\t\t\t\t\tline-height: 1.5;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n}\n\n\n</style>\n", "import mod from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1751160935249\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}