<view class="mine"><view class="safe-area"></view><view class="user-info"><view data-event-opts="{{[['tap',[['navigateTo',['/pages/mine/profile']]]]]}}" class="avatar-container" bindtap="__e"><image class="avatar" src="{{avatarPath}}"></image></view><view class="user-detail"><text class="nickname">{{userInfo.username||'未登录'}}</text></view><view data-event-opts="{{[['tap',[['navigateTo',['/pages/mine/profile']]]]]}}" class="edit-btn" bindtap="__e"><image src="/static/icons/edit.png"></image></view></view><view class="menu-list"><view class="menu-section"><view data-event-opts="{{[['tap',[['navigateTo',['/pages/mine/goal']]]]]}}" class="menu-item" bindtap="__e"><view class="menu-icon"><image src="/static/icons/goal.png"></image></view><view class="menu-content"><text class="menu-title">饮食目标</text><text class="menu-desc">设置每日营养摄入目标</text></view><view class="menu-arrow"><image src="/static/icons/arrow-right.png"></image></view></view><view data-event-opts="{{[['tap',[['navigateTo',['/pages/mine/settings']]]]]}}" class="menu-item" bindtap="__e"><view class="menu-icon"><image src="/static/icons/settings.png"></image></view><view class="menu-content"><text class="menu-title">设置</text><text class="menu-desc">通知、隐私等设置</text></view><view class="menu-arrow"><image src="/static/icons/arrow-right.png"></image></view></view></view><view class="menu-section"><view data-event-opts="{{[['tap',[['showFeedback']]]]}}" class="menu-item" bindtap="__e"><view class="menu-icon"><image src="/static/icons/feedback.png"></image></view><view class="menu-content"><text class="menu-title">意见反馈</text><text class="menu-desc">帮助我们改进产品</text></view><view class="menu-arrow"><image src="/static/icons/arrow-right.png"></image></view></view><view data-event-opts="{{[['tap',[['showAbout']]]]}}" class="menu-item" bindtap="__e"><view class="menu-icon"><image src="/static/icons/about.png"></image></view><view class="menu-content"><text class="menu-title">关于我们</text><text class="menu-desc">了解更多信息</text></view><view class="menu-arrow"><image src="/static/icons/arrow-right.png"></image></view></view></view><block wx:if="{{isLogin}}"><view data-event-opts="{{[['tap',[['logout',['$event']]]]]}}" class="logout-btn" bindtap="__e"><text>退出登录</text></view></block></view></view>