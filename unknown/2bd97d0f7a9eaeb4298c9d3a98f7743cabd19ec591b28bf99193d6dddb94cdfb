<view class="goal-setting"><view class="section"><view class="section-header"><text class="section-title">基础目标</text></view><view class="form-item"><text class="form-label">每日热量目标</text><view class="form-input"><input type="number" placeholder="输入目标热量" data-event-opts="{{[['input',[['__set_model',['','calorieTarget','$event',[]]]]]]}}" value="{{calorieTarget}}" bindinput="__e"/><text class="unit">千卡</text></view></view></view><view class="section"><view class="section-header"><text class="section-title">营养素目标</text></view><view class="form-item"><text class="form-label">蛋白质</text><view class="form-input"><input type="number" placeholder="输入蛋白质目标" data-event-opts="{{[['input',[['__set_model',['$0','protein','$event',[]],['nutritionGoals']]]]]}}" value="{{nutritionGoals.protein}}" bindinput="__e"/><text class="unit">g</text></view></view><view class="form-item"><text class="form-label">碳水化合物</text><view class="form-input"><input type="number" placeholder="输入碳水目标" data-event-opts="{{[['input',[['__set_model',['$0','carbs','$event',[]],['nutritionGoals']]]]]}}" value="{{nutritionGoals.carbs}}" bindinput="__e"/><text class="unit">g</text></view></view><view class="form-item"><text class="form-label">脂肪</text><view class="form-input"><input type="number" placeholder="输入脂肪目标" data-event-opts="{{[['input',[['__set_model',['$0','fat','$event',[]],['nutritionGoals']]]]]}}" value="{{nutritionGoals.fat}}" bindinput="__e"/><text class="unit">g</text></view></view></view><view class="section"><view class="section-header"><text class="section-title">饮食偏好</text></view><view class="preference-list"><block wx:for="{{dietPreferences}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['togglePreference',[index]]]]]}}" class="preference-item" bindtap="__e"><text class="preference-name">{{item.name}}</text><switch checked="{{item.selected}}" color="#4CAF50" data-event-opts="{{[['change',[['onSwitchChange',['$event',index]]]]]}}" bindchange="__e"></switch></view></block></view></view><view data-event-opts="{{[['tap',[['saveGoals',['$event']]]]]}}" class="save-btn" bindtap="__e"><text>保存设置</text></view></view>