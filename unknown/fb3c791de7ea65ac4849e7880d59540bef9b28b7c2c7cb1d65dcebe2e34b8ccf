{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/HBuilderProjects/springboot-dubbo-front-wechat/pages/diet-record/add.vue?5305", "webpack:///D:/HBuilderProjects/springboot-dubbo-front-wechat/pages/diet-record/add.vue?97c1", "webpack:///D:/HBuilderProjects/springboot-dubbo-front-wechat/pages/diet-record/add.vue?4e04", "webpack:///D:/HBuilderProjects/springboot-dubbo-front-wechat/pages/diet-record/add.vue?ac48", "uni-app:///pages/diet-record/add.vue", "webpack:///D:/HBuilderProjects/springboot-dubbo-front-wechat/pages/diet-record/add.vue?2ee9", "webpack:///D:/HBuilderProjects/springboot-dubbo-front-wechat/pages/diet-record/add.vue?a7bd"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "selectedMeal", "selectedDate", "selectedTime", "selectedFoods", "remark", "mealTypes", "key", "name", "showFoodModal", "searchKeyword", "selectedCategoryId", "showSelectionPanel", "selectionMode", "unitAmount", "customGrams", "computed", "searchResults", "foodCategories", "isLoadingMore", "currentFood", "hasMore", "totalCalorie", "reduce", "toFixed", "created", "methods", "addDietRecord", "getFoodList", "getFoodCategories", "getFoodDetail", "set<PERSON><PERSON><PERSON><PERSON>ood", "onDateChange", "onTimeChange", "fetchFoodCategories", "showFoodSearch", "hideFoodSearch", "selectCategory", "searchFood", "keyword", "categoryId", "reset", "loadMoreFoods", "showFoodSelectionPanel", "hideSelectionPanel", "increaseUnitAmount", "decreaseUnitAmount", "getUnitCalories", "getUnitProtein", "getUnitFat", "getUnitCarbs", "updateNutritionByGrams", "getGramsCalories", "getGramsProtein", "getGramsFat", "getGramsCarbs", "confirmAdd<PERSON>ood", "id", "unit", "unitDisplay", "amount", "grams", "calories", "protein", "fat", "carbs", "baseCalories", "baseProtein", "baseFat", "baseCarbs", "baseGrams", "added", "increaseAmount", "decreaseAmount", "recalculateNutrition", "food", "removeFood", "saveDietRecord", "date", "time", "mealType", "foods", "foodId", "showToast", "uni", "title", "icon", "navigateBack"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,YAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAgH;AAChH;AACuD;AACL;AACc;;;AAGhE;AACmK;AACnK,gBAAgB,6KAAU;AAC1B,EAAE,yEAAM;AACR,EAAE,8EAAM;AACR,EAAE,uFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,kFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC/FA;AAAA;AAAA;AAAA;AAAgmB,CAAgB,6mBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;ACkVpnB;AACA;AAAA;AAAA;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC,YACA;QAAAC;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,EACA;MACA;MACAC;MACAC;MACAC;MAAA;;MAEA;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC,wDACA;IACAC;MAAA;IAAA;IACAC;MAAA;IAAA;IACAC;MAAA;IAAA;IACAC;MAAA;IAAA;EACA,KACA;IACAC;EACA;IACAC;MACA,0BACAC;QAAA;MAAA,MACAC;IACA;EAAA,EACA;EACAC;IACA;EACA;EACAC,yCACA;IACAC;IACAC;IACAC;IACAC;IACAC;EACA;IAEA;IACAC;MAAA;IAAA;IACAC;MAAA;IAAA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA;MACA;IACA;IACAC;MAAA;MACA;QACAC;QACAC;QACAC;MACA;IACA;IACAC;MACA;QACA;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;MACA;MACA;IACA;IACAC;MACA;MACA;IACA;IAEA;IACAC;MAAA;IAAA;IACAC;MAAA;IAAA;IACAC;MAAA;IAAA;IACAC;MAAA;IAAA;IACAC;MAAA;IAAA;IACAC;MAAA;IAAA;IAEA;IACAC;MACA;IAAA,CACA;IACAC;MACA,oDACA,qFACA;IACA;IACAC;MACA,oDACA,oFACA;IACA;IACAC;MACA,oDACA,gFACA;IACA;IACAC;MACA,oDACA,kFACA;IACA;IAEA;IACAC;MACA;MAEA;MACA;QACAC;QACAjD;QACAkD;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;MAEA;QACAC;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QAEAA;QACAA;QACAA;QACAA;QACAA;MACA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QAEAA;QACAA;QACAA;QACAA;QACAA;MACA;MAEA;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;IACA;IACAC;MACA;QACA;QACA;MACA;IACA;IACAC;MACA;MACA;QACAC;QACAA;QACAA;QACAA;QACAA;MACA;QACAA;QACAA;QACAA;QACAA;QACAA;MACA;IACA;IACAC;MACA;IACA;IAEA;IACAC;MACA;QACA;QACA;MACA;MACA;QACAC;QACAC;QACAC;QACAC;UAAA;YACAC;YACA1E;YACAoD;YACAF;YACAI;YACAC;YACAC;YACAC;YACAJ;UACA;QAAA;QACAxD;QACAiB;MACA;MACA;MACA;IACA;IAEA6D;MAAA;MACAC;QAAAC;QAAAC;MAAA;IACA;IACAC;MACAH;IACA;EAAA;AAEA;AAAA,2B;;;;;;;;;;;;;AC5lBA;AAAA;AAAA;AAAA;AAA2oC,CAAgB,4lCAAG,EAAC,C;;;;;;;;;;;ACA/pC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/diet-record/add.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/diet-record/add.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./add.vue?vue&type=template&id=4392ebf3&\"\nvar renderjs\nimport script from \"./add.vue?vue&type=script&lang=js&\"\nexport * from \"./add.vue?vue&type=script&lang=js&\"\nimport style0 from \"./add.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/diet-record/add.vue\"\nexport default component.exports", "export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./add.vue?vue&type=template&id=4392ebf3&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.selectedFoods.length\n  var l0 = _vm.__map(_vm.selectedFoods, function (food, index) {\n    var $orig = _vm.__get_orig(food)\n    var g1 = food.calories.toFixed(0)\n    var g2 = food.protein.toFixed(1)\n    var g3 = food.fat.toFixed(1)\n    var g4 = food.carbs.toFixed(1)\n    return {\n      $orig: $orig,\n      g1: g1,\n      g2: g2,\n      g3: g3,\n      g4: g4,\n    }\n  })\n  var g5 = _vm.showFoodModal\n    ? !_vm.hasMore && _vm.searchResults.length > 0\n    : null\n  var g6 = _vm.showFoodModal\n    ? _vm.searchResults.length === 0 && !_vm.isLoadingMore\n    : null\n  var m0 =\n    _vm.showSelectionPanel && _vm.selectionMode === \"unit\"\n      ? _vm.getUnitCalories()\n      : null\n  var m1 =\n    _vm.showSelectionPanel && _vm.selectionMode === \"unit\"\n      ? _vm.getUnitProtein()\n      : null\n  var m2 =\n    _vm.showSelectionPanel && _vm.selectionMode === \"unit\"\n      ? _vm.getUnitFat()\n      : null\n  var m3 =\n    _vm.showSelectionPanel && _vm.selectionMode === \"unit\"\n      ? _vm.getUnitCarbs()\n      : null\n  var m4 =\n    _vm.showSelectionPanel && _vm.selectionMode === \"grams\"\n      ? _vm.getGramsCalories()\n      : null\n  var m5 =\n    _vm.showSelectionPanel && _vm.selectionMode === \"grams\"\n      ? _vm.getGramsProtein()\n      : null\n  var m6 =\n    _vm.showSelectionPanel && _vm.selectionMode === \"grams\"\n      ? _vm.getGramsFat()\n      : null\n  var m7 =\n    _vm.showSelectionPanel && _vm.selectionMode === \"grams\"\n      ? _vm.getGramsCarbs()\n      : null\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event, meal) {\n      var _temp = arguments[arguments.length - 1].currentTarget.dataset,\n        _temp2 = _temp.eventParams || _temp[\"event-params\"],\n        meal = _temp2.meal\n      var _temp, _temp2\n      _vm.selectedMeal = meal.key\n    }\n    _vm.e1 = function ($event) {\n      _vm.selectionMode = \"unit\"\n    }\n    _vm.e2 = function ($event) {\n      _vm.selectionMode = \"grams\"\n    }\n  }\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        l0: l0,\n        g5: g5,\n        g6: g6,\n        m0: m0,\n        m1: m1,\n        m2: m2,\n        m3: m3,\n        m4: m4,\n        m5: m5,\n        m6: m6,\n        m7: m7,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./add.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./add.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"add-diet\">\n    <!-- 添加顶部安全区域 -->\n    <view class=\"safe-area\"></view>\n\n    <view class=\"header\">\n      <view class=\"back-btn\" @click=\"navigateBack()\">\n        <image src=\"/static/icons/back.png\"></image>\n      </view>\n      <text class=\"title\">添加饮食记录</text>\n    </view>\n\n    <view class=\"form-section\">\n      <!-- 餐次选择 -->\n      <view class=\"meal-selector\">\n        <view\n          class=\"meal-option\"\n          v-for=\"(meal, index) in mealTypes\"\n          :key=\"index\"\n          :class=\"{ active: selectedMeal === meal.key }\"\n          @click=\"selectedMeal = meal.key\"\n        >\n          <text>{{ meal.name }}</text>\n        </view>\n      </view>\n\n      <!-- 日期选择 -->\n      <view class=\"form-item\">\n        <text class=\"form-label\">日期</text>\n        <view class=\"form-input\">\n          <picker\n            mode=\"date\"\n            :value=\"selectedDate\"\n            @change=\"onDateChange\"\n          >\n            <view class=\"picker-value\">\n              <text>{{ selectedDate }}</text>\n              <image src=\"/static/icons/calendar.png\"></image>\n            </view>\n          </picker>\n        </view>\n      </view>\n\n      <!-- 时间选择 -->\n      <view class=\"form-item\">\n        <text class=\"form-label\">时间</text>\n        <view class=\"form-input\">\n          <picker\n            mode=\"time\"\n            :value=\"selectedTime\"\n            @change=\"onTimeChange\"\n          >\n            <view class=\"picker-value\">\n              <text>{{ selectedTime }}</text>\n              <image src=\"/static/icons/clock.png\"></image>\n            </view>\n          </picker>\n        </view>\n      </view>\n\n      <!-- 食物列表 -->\n      <view class=\"food-list\">\n        <view class=\"section-header\">\n          <text class=\"section-title\">食物列表</text>\n          <view class=\"add-food-btn\" @click=\"showFoodSearch\">\n            <image src=\"/static/icons/add-food.png\"></image>\n            <text>添加食物</text>\n          </view>\n        </view>\n\n        <view class=\"empty-tip\" v-if=\"selectedFoods.length === 0\">\n          <image src=\"/static/icons/empty.png\"></image>\n          <text>暂无食物，请添加</text>\n        </view>\n\n        <view class=\"food-item\" v-for=\"(food, index) in selectedFoods\" :key=\"index\">\n          <view class=\"food-info\">\n            <text class=\"food-name\">{{ food.name }}</text>\n            <text class=\"food-desc\">{{ food.unitDisplay }}</text>\n          </view>\n          <view class=\"food-amount\">\n            <view class=\"amount-control\">\n              <view class=\"minus-btn\" @click=\"decreaseAmount(index)\">\n                <text>-</text>\n              </view>\n              <input type=\"number\" v-model=\"food.amount\" class=\"amount-input\" @input=\"recalculateNutrition(index)\" />\n              <view class=\"plus-btn\" @click=\"increaseAmount(index)\">\n                <text>+</text>\n              </view>\n            </view>\n          </view>\n          <view class=\"food-nutrition-info\">\n            <text class=\"food-calorie\">{{ food.calories.toFixed(0) }}千卡</text>\n            <view class=\"nutrition-detail\">\n              <text>蛋白质:{{ food.protein.toFixed(1) }}g</text>\n              <text>脂肪:{{ food.fat.toFixed(1) }}g</text>\n              <text>碳水:{{ food.carbs.toFixed(1) }}g</text>\n            </view>\n          </view>\n          <view class=\"delete-btn\" @click=\"removeFood(index)\">\n            <image src=\"/static/icons/delete.png\"></image>\n          </view>\n        </view>\n      </view>\n\n      <!-- 备注 -->\n      <view class=\"form-item\">\n        <text class=\"form-label\">备注</text>\n        <view class=\"form-input\">\n          <textarea v-model=\"remark\" placeholder=\"添加备注信息\" />\n        </view>\n      </view>\n    </view>\n\n    <!-- 底部操作栏 -->\n    <view class=\"bottom-bar\">\n      <view class=\"total-calorie\">\n        <text>总热量: {{ totalCalorie }}千卡</text>\n      </view>\n      <view class=\"save-btn\" @click=\"saveDietRecord\">\n        <text>保存记录</text>\n      </view>\n    </view>\n\n    <!-- 食物搜索弹窗 -->\n    <view class=\"food-search-modal\" v-if=\"showFoodModal\">\n      <view class=\"modal-mask\" @click=\"hideFoodSearch\"></view>\n      <view class=\"modal-content\">\n        <view class=\"modal-header\">\n          <text class=\"modal-title\">添加食物</text>\n          <view class=\"close-btn\" @click=\"hideFoodSearch\">\n            <image src=\"/static/icons/close.png\"></image>\n          </view>\n        </view>\n\n        <view class=\"search-box\">\n          <image src=\"/static/icons/search.png\"></image>\n          <input\n            type=\"text\"\n            v-model=\"searchKeyword\"\n            placeholder=\"搜索食物\"\n            @input=\"searchFood\"\n          />\n        </view>\n\n        <!-- 分类筛选 -->\n        <scroll-view scroll-x class=\"category-scroll\" :show-scrollbar=\"false\">\n          <view\n            class=\"category-item\"\n            :class=\"{ active: selectedCategoryId === undefined }\"\n            @click=\"selectCategory(null)\"\n          >\n            <text>全部</text>\n          </view>\n          <view\n            class=\"category-item\"\n            v-for=\"category in foodCategories\"\n            :key=\"category.id\"\n            :class=\"{ active: selectedCategoryId === category.id }\"\n            @click=\"selectCategory(category.id)\"\n          >\n            <text>{{ category.name }}</text>\n          </view>\n        </scroll-view>\n\n        <!-- 食物列表 -->\n        <scroll-view\n          scroll-y\n          class=\"food-scroll\"\n          @scrolltolower=\"loadMoreFoods\"\n          :lower-threshold=\"50\"\n        >\n          <view class=\"food-list-container\">\n            <view class=\"food-card\" v-for=\"(food, index) in searchResults\" :key=\"index\" @click=\"showFoodSelectionPanel(food)\">\n              <view class=\"food-card-main\">\n                <view class=\"food-info\">\n                  <text class=\"food-name\">{{ food.name }}</text>\n                  <!-- 已移除 food.desc -->\n                </view>\n                <view class=\"food-nutrition\">\n                  <text class=\"food-calorie\">{{ food.calories }}千卡</text>\n                  <text class=\"food-unit\">{{ food.measure }}</text>\n                </view>\n              </view>\n              <view class=\"food-detail\">\n                <view class=\"nutrition-item\">\n                  <text class=\"nutrition-value\">{{ food.protein }}g</text>\n                  <text class=\"nutrition-label\">蛋白质</text>\n                </view>\n                <view class=\"nutrition-item\">\n                  <text class=\"nutrition-value\">{{ food.fat }}g</text>\n                  <text class=\"nutrition-label\">脂肪</text>\n                </view>\n                <view class=\"nutrition-item\">\n                  <text class=\"nutrition-value\">{{ food.carbs }}g</text>\n                  <text class=\"nutrition-label\">碳水</text>\n                </view>\n              </view>\n            </view>\n\n            <!-- 加载状态 -->\n            <view class=\"loading-state\" v-if=\"isLoadingMore\">\n              <view class=\"loading-dots\">\n                <view class=\"dot\"></view>\n                <view class=\"dot\"></view>\n                <view class=\"dot\"></view>\n              </view>\n              <text>正在加载更多美食...</text>\n            </view>\n\n            <!-- 全部加载完成 -->\n            <view class=\"all-loaded-state\" v-if=\"!hasMore && searchResults.length > 0\">\n              <view class=\"divider-line\"></view>\n              <view class=\"all-loaded-content\">\n                <image src=\"/static/icons/empty.png\" class=\"end-icon\"></image>\n                <text>已经到底啦，没有更多食物了</text>\n              </view>\n            </view>\n\n            <!-- 无结果提示 -->\n            <view class=\"empty-tip\" v-if=\"searchResults.length === 0 && !isLoadingMore\">\n              <image src=\"/static/icons/empty.png\"></image>\n              <text>未找到相关食物</text>\n            </view>\n          </view>\n        </scroll-view>\n      </view>\n    </view>\n\n    <!-- 食物选择面板 -->\n    <view class=\"food-selection-panel\" v-if=\"showSelectionPanel\">\n      <view class=\"modal-mask\" @click=\"hideSelectionPanel\"></view>\n      <view class=\"selection-content\">\n        <view class=\"selection-header\">\n          <text class=\"selection-title\">{{ currentFood.name }}</text>\n          <view class=\"close-btn\" @click=\"hideSelectionPanel\">\n            <image src=\"/static/icons/close.png\"></image>\n          </view>\n        </view>\n\n        <view class=\"selection-tabs\">\n          <view\n            class=\"tab-item\"\n            :class=\"{ active: selectionMode === 'unit' }\"\n            @click=\"selectionMode = 'unit'\"\n          >\n            <text>按单位添加</text>\n          </view>\n          <view\n            class=\"tab-item\"\n            :class=\"{ active: selectionMode === 'grams' }\"\n            @click=\"selectionMode = 'grams'\"\n          >\n            <text>按克数添加</text>\n          </view>\n        </view>\n\n        <view class=\"selection-body\">\n          <!-- 单位选择模式 -->\n          <view class=\"unit-selection\" v-if=\"selectionMode === 'unit'\">\n            <view class=\"food-measure\">\n              <text>{{ currentFood.measure }}（约{{ currentFood.grams }}克）</text>\n            </view>\n            <view class=\"amount-control\">\n              <view class=\"control-btn minus\" @click=\"decreaseUnitAmount\">\n                <text>-</text>\n              </view>\n              <input type=\"number\" v-model=\"unitAmount\" class=\"amount-input\" />\n              <view class=\"control-btn plus\" @click=\"increaseUnitAmount\">\n                <text>+</text>\n              </view>\n            </view>\n            <view class=\"nutrition-preview\">\n              <view class=\"preview-item\">\n                <text class=\"preview-label\">热量</text>\n                <text class=\"preview-value\">{{ getUnitCalories() }}千卡</text>\n              </view>\n              <view class=\"preview-item\">\n                <text class=\"preview-label\">蛋白质</text>\n                <text class=\"preview-value\">{{ getUnitProtein() }}g</text>\n              </view>\n              <view class=\"preview-item\">\n                <text class=\"preview-label\">脂肪</text>\n                <text class=\"preview-value\">{{ getUnitFat() }}g</text>\n              </view>\n              <view class=\"preview-item\">\n                <text class=\"preview-label\">碳水</text>\n                <text class=\"preview-value\">{{ getUnitCarbs() }}g</text>\n              </view>\n            </view>\n          </view>\n\n          <!-- 克数选择模式 -->\n          <view class=\"grams-selection\" v-if=\"selectionMode === 'grams'\">\n            <view class=\"grams-input-container\">\n              <input\n                type=\"number\"\n                v-model=\"customGrams\"\n                class=\"grams-input\"\n                @input=\"updateNutritionByGrams\"\n              />\n              <text class=\"grams-label\">克</text>\n            </view>\n            <view class=\"nutrition-preview\">\n              <view class=\"preview-item\">\n                <text class=\"preview-label\">热量</text>\n                <text class=\"preview-value\">{{ getGramsCalories() }}千卡</text>\n              </view>\n              <view class=\"preview-item\">\n                <text class=\"preview-label\">蛋白质</text>\n                <text class=\"preview-value\">{{ getGramsProtein() }}g</text>\n              </view>\n              <view class=\"preview-item\">\n                <text class=\"preview-label\">脂肪</text>\n                <text class=\"preview-value\">{{ getGramsFat() }}g</text>\n              </view>\n              <view class=\"preview-item\">\n                <text class=\"preview-label\">碳水</text>\n                <text class=\"preview-value\">{{ getGramsCarbs() }}g</text>\n              </view>\n            </view>\n          </view>\n        </view>\n\n        <view class=\"selection-footer\">\n          <view class=\"cancel-btn\" @click=\"hideSelectionPanel\">\n            <text>取消</text>\n          </view>\n          <view class=\"confirm-btn\" @click=\"confirmAddFood\">\n            <text>添加</text>\n          </view>\n        </view>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script>\nimport { mapActions, mapState, mapGetters } from 'vuex'\nimport { formatDate } from '@/utils/date'\n\nexport default {\n  data() {\n    return {\n      selectedMeal: 'breakfast',\n      selectedDate: formatDate(new Date(), 'yyyy-MM-dd'),\n      selectedTime: formatDate(new Date(), 'hh:mm'),\n      selectedFoods: [],\n      remark: '',\n      mealTypes: [\n        { key: 'breakfast', name: '早餐' },\n        { key: 'lunch',     name: '午餐' },\n        { key: 'dinner',    name: '晚餐' },\n        { key: 'snacks',    name: '加餐' }\n      ],\n      // 食物搜索相关\n      showFoodModal: false,\n      searchKeyword: '',\n      selectedCategoryId: undefined, // 使用undefined而不是null\n\n      // 食物选择面板相关\n      showSelectionPanel: false,\n      selectionMode: 'unit',\n      unitAmount: 1,\n      customGrams: 100\n    }\n  },\n  computed: {\n    ...mapState({\n      searchResults: state => state.food.foodList,\n      foodCategories: state => state.food.foodCategories,\n      isLoadingMore:  state => state.food.loading,\n      currentFood:    state => state.food.currentFood\n    }),\n    ...mapGetters({\n      hasMore: 'food/hasMoreFood'\n    }),\n    totalCalorie() {\n      return this.selectedFoods\n        .reduce((sum, f) => sum + f.calories, 0)\n        .toFixed(0)\n    }\n  },\n  created() {\n    this.fetchFoodCategories()\n  },\n  methods: {\n    ...mapActions({\n      addDietRecord:    'diet/addDietRecord',\n      getFoodList:      'food/getFoodList',\n      getFoodCategories:'food/getFoodCategories',\n      getFoodDetail:    'food/getFoodDetail',\n      setCurrentFood:   'food/setCurrentFood'\n    }),\n\n    // 表单操作\n    onDateChange(e) { this.selectedDate = e.detail.value },\n    onTimeChange(e) { this.selectedTime = e.detail.value },\n\n    // 获取分类\n    fetchFoodCategories() {\n      this.getFoodCategories()\n    },\n\n    // 搜索逻辑\n    showFoodSearch() {\n      this.showFoodModal = true\n      this.searchKeyword = ''\n      this.selectedCategoryId = undefined // 使用undefined而不是null\n      this.searchFood(true)\n    },\n    hideFoodSearch() {\n      this.showFoodModal = false\n    },\n    selectCategory(categoryId) {\n      // 如果选择\"全部\"，将selectedCategoryId设置为undefined而不是null\n      this.selectedCategoryId = categoryId === null ? undefined : categoryId\n      this.searchFood(true)\n    },\n    searchFood(reset = false) {\n      this.getFoodList({\n        keyword:  this.searchKeyword,\n        categoryId: this.selectedCategoryId,\n        reset\n      })\n    },\n    loadMoreFoods() {\n      if (this.hasMore && !this.isLoadingMore) {\n        this.searchFood(false)\n      }\n    },\n\n    // 打开/关闭选择面板\n    showFoodSelectionPanel(food) {\n      this.setCurrentFood(food)\n      this.showSelectionPanel = true\n      this.selectionMode = 'unit'\n      this.unitAmount = 1\n      this.customGrams = food.grams\n    },\n    hideSelectionPanel() {\n      this.showSelectionPanel = false\n      this.setCurrentFood(null)\n    },\n\n    // 单位模式\n    increaseUnitAmount() { this.unitAmount++ },\n    decreaseUnitAmount() { if (this.unitAmount > 1) this.unitAmount-- },\n    getUnitCalories() { return this.currentFood ? (this.currentFood.calories * this.unitAmount).toFixed(1) : 0 },\n    getUnitProtein()  { return this.currentFood ? (this.currentFood.protein  * this.unitAmount).toFixed(1) : 0 },\n    getUnitFat()      { return this.currentFood ? (this.currentFood.fat      * this.unitAmount).toFixed(1) : 0 },\n    getUnitCarbs()    { return this.currentFood ? (this.currentFood.carbs    * this.unitAmount).toFixed(1) : 0 },\n\n    // 克数模式\n    updateNutritionByGrams() {\n      // 计算在 getGramsXxx 中实时完成\n    },\n    getGramsCalories() {\n      return this.currentFood && this.currentFood.grams\n        ? (this.currentFood.calories * this.customGrams / this.currentFood.grams).toFixed(1)\n        : 0\n    },\n    getGramsProtein() {\n      return this.currentFood && this.currentFood.grams\n        ? (this.currentFood.protein * this.customGrams / this.currentFood.grams).toFixed(1)\n        : 0\n    },\n    getGramsFat() {\n      return this.currentFood && this.currentFood.grams\n        ? (this.currentFood.fat * this.customGrams / this.currentFood.grams).toFixed(1)\n        : 0\n    },\n    getGramsCarbs() {\n      return this.currentFood && this.currentFood.grams\n        ? (this.currentFood.carbs * this.customGrams / this.currentFood.grams).toFixed(1)\n        : 0\n    },\n\n    // 确认添加\n    confirmAddFood() {\n      if (!this.currentFood) return\n\n      const f = this.currentFood\n      const added = {\n        id: f.id,\n        name: f.name,\n        unit: '',\n        unitDisplay: '',\n        amount: 0,\n        grams: 0,\n        calories: 0,\n        protein: 0,\n        fat: 0,\n        carbs: 0,\n        baseCalories: 0,\n        baseProtein: 0,\n        baseFat: 0,\n        baseCarbs: 0,\n        baseGrams: 0\n      }\n\n      if (this.selectionMode === 'unit') {\n        added.unit = f.measure\n        added.unitDisplay = `单位/${f.measure}`\n        added.amount = this.unitAmount\n        added.grams    = f.grams * this.unitAmount\n        added.calories = f.calories * this.unitAmount\n        added.protein  = f.protein  * this.unitAmount\n        added.fat      = f.fat      * this.unitAmount\n        added.carbs    = f.carbs    * this.unitAmount\n\n        added.baseCalories = f.calories\n        added.baseProtein  = f.protein\n        added.baseFat      = f.fat\n        added.baseCarbs    = f.carbs\n        added.baseGrams    = f.grams\n      } else {\n        added.unit = '克'\n        added.unitDisplay = '单位/克'\n        added.amount = this.customGrams\n        added.grams    = this.customGrams\n        added.calories = f.calories * this.customGrams / f.grams\n        added.protein  = f.protein  * this.customGrams / f.grams\n        added.fat      = f.fat      * this.customGrams / f.grams\n        added.carbs    = f.carbs    * this.customGrams / f.grams\n\n        added.baseCalories = f.calories / f.grams\n        added.baseProtein  = f.protein  / f.grams\n        added.baseFat      = f.fat      / f.grams\n        added.baseCarbs    = f.carbs    / f.grams\n        added.baseGrams    = 1\n      }\n\n      this.selectedFoods.push(added)\n      this.hideSelectionPanel()\n      this.hideFoodSearch()\n    },\n\n    // 列表操作\n    increaseAmount(index) {\n      this.selectedFoods[index].amount++\n      this.recalculateNutrition(index)\n    },\n    decreaseAmount(index) {\n      if (this.selectedFoods[index].amount > 1) {\n        this.selectedFoods[index].amount--\n        this.recalculateNutrition(index)\n      }\n    },\n    recalculateNutrition(i) {\n      const food = this.selectedFoods[i]\n      if (food.unit === '克') {\n        food.grams    = food.amount\n        food.calories = food.baseCalories * food.amount\n        food.protein  = food.baseProtein  * food.amount\n        food.fat      = food.baseFat      * food.amount\n        food.carbs    = food.baseCarbs    * food.amount\n      } else {\n        food.grams    = food.baseGrams    * food.amount\n        food.calories = food.baseCalories * food.amount\n        food.protein  = food.baseProtein  * food.amount\n        food.fat      = food.baseFat      * food.amount\n        food.carbs    = food.baseCarbs    * food.amount\n      }\n    },\n    removeFood(index) {\n      this.selectedFoods.splice(index, 1)\n    },\n\n    // 保存记录\n    saveDietRecord() {\n      if (this.selectedFoods.length === 0) {\n        this.showToast('请先添加食物')\n        return\n      }\n      const record = {\n        date:        this.selectedDate,\n        time:        this.selectedTime,\n        mealType:    this.selectedMeal,\n        foods:       this.selectedFoods.map(f => ({\n          foodId:   f.id,\n          name:     f.name,\n          amount:   f.amount,\n          unit:     f.unit,\n          calories: f.calories,\n          protein:  f.protein,\n          fat:      f.fat,\n          carbs:    f.carbs,\n          grams:    f.grams\n        })),\n        remark:      this.remark,\n        totalCalorie: parseFloat(this.totalCalorie)\n      }\n      // 直接调用store中的方法，不再自己处理导航和提示\n      this.addDietRecord(record)\n    },\n\n    showToast(title, icon = 'none') {\n      uni.showToast({ title, icon })\n    },\n    navigateBack() {\n      uni.navigateBack()\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\">\n.add-diet {\n  min-height: 100vh;\n  background-color: #f8f8f8;\n  padding: 0 20rpx 120rpx;\n}\n\n.safe-area {\n  height: 80rpx;\n  background-color: #f8f8f8;\n}\n\n.header {\n  display: flex;\n  align-items: center;\n  height: 88rpx;\n  padding: 0 30rpx;\n\n  .back-btn {\n    width: 60rpx;\n    height: 60rpx;\n    display: flex;\n    justify-content: center;\n    align-items: center;\n\n    image {\n      width: 36rpx;\n      height: 36rpx;\n    }\n  }\n\n  .title {\n    flex: 1;\n    text-align: center;\n    font-size: 32rpx;\n    font-weight: bold;\n    color: #333;\n  }\n}\n\n.form-section {\n  padding-bottom: 30rpx;\n}\n\n.meal-selector {\n  display: flex;\n  background-color: #ffffff;\n  border-radius: 20rpx;\n  margin-bottom: 20rpx;\n  overflow: hidden;\n  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);\n  animation: fadeIn 0.5s ease-in-out;\n\n  .meal-option {\n    flex: 1;\n    height: 80rpx;\n    display: flex;\n    justify-content: center;\n    align-items: center;\n    transition: all 0.3s ease;\n\n    text {\n      font-size: 28rpx;\n      color: #666;\n      transition: all 0.3s ease;\n    }\n\n    &.active {\n      background: linear-gradient(135deg, #4CAF50, #8BC34A);\n\n      text {\n        color: #ffffff;\n        font-weight: 500;\n      }\n    }\n\n    &:active {\n      opacity: 0.8;\n    }\n  }\n}\n\n.form-item {\n  background-color: #ffffff;\n  border-radius: 20rpx;\n  padding: 30rpx;\n  margin-bottom: 20rpx;\n  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);\n  animation: fadeIn 0.5s ease-in-out;\n\n  .form-label {\n    font-size: 28rpx;\n    font-weight: 500;\n    color: #333;\n    margin-bottom: 20rpx;\n    display: block;\n    position: relative;\n    padding-left: 20rpx;\n\n    &::before {\n      content: '';\n      position: absolute;\n      left: 0;\n      top: 50%;\n      transform: translateY(-50%);\n      width: 8rpx;\n      height: 28rpx;\n      background-color: #4CAF50;\n      border-radius: 4rpx;\n    }\n  }\n\n  .form-input {\n    .picker-value {\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      height: 80rpx;\n      background-color: #f9f9f9;\n      border-radius: 12rpx;\n      padding: 0 20rpx;\n\n      text {\n        font-size: 28rpx;\n        color: #333;\n      }\n\n      image {\n        width: 36rpx;\n        height: 36rpx;\n      }\n    }\n\n    textarea {\n      width: 100%;\n      height: 160rpx;\n      font-size: 28rpx;\n      background-color: #f9f9f9;\n      border-radius: 12rpx;\n      padding: 20rpx;\n    }\n  }\n}\n\n.food-list {\n  background-color: #ffffff;\n  border-radius: 20rpx;\n  padding: 30rpx;\n  margin-bottom: 20rpx;\n  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);\n  animation: fadeIn 0.5s ease-in-out;\n\n  .section-header {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    margin-bottom: 30rpx;\n\n    .section-title {\n      font-size: 32rpx;\n      font-weight: bold;\n      color: #333;\n      position: relative;\n      padding-left: 20rpx;\n\n      &::before {\n        content: '';\n        position: absolute;\n        left: 0;\n        top: 50%;\n        transform: translateY(-50%);\n        width: 8rpx;\n        height: 32rpx;\n        background-color: #4CAF50;\n        border-radius: 4rpx;\n      }\n    }\n\n    .add-food-btn {\n      display: flex;\n      align-items: center;\n      background-color: rgba(76, 175, 80, 0.1);\n      padding: 10rpx 20rpx;\n      border-radius: 30rpx;\n      transition: all 0.3s ease;\n\n      &:active {\n        transform: scale(0.95);\n      }\n\n      image {\n        width: 32rpx;\n        height: 32rpx;\n        margin-right: 8rpx;\n      }\n\n      text {\n        font-size: 28rpx;\n        color: #4CAF50;\n        font-weight: 500;\n      }\n    }\n  }\n\n  .empty-tip {\n    display: flex;\n    flex-direction: column;\n    align-items: center;\n    padding: 80rpx 0;\n\n    image {\n      width: 140rpx;\n      height: 140rpx;\n      margin-bottom: 30rpx;\n      opacity: 0.6;\n    }\n\n    text {\n      font-size: 28rpx;\n      color: #999;\n    }\n  }\n\n  .food-item {\n    display: flex;\n    align-items: center;\n    margin-bottom: 30rpx;\n    padding: 30rpx;\n    border-radius: 16rpx;\n    background-color: #f9f9f9;\n    transition: all 0.3s ease;\n\n    &:last-child {\n      margin-bottom: 0;\n    }\n\n    &:active {\n      transform: scale(0.98);\n    }\n\n    .food-info {\n      flex: 1;\n      margin-right: 20rpx;\n\n      .food-name {\n        font-size: 30rpx;\n        color: #333;\n        font-weight: 500;\n        margin-bottom: 10rpx;\n        display: block;\n      }\n\n      .food-desc {\n        font-size: 24rpx;\n        color: #999;\n      }\n    }\n\n    .food-amount {\n      display: flex;\n      align-items: center;\n      margin-right: 20rpx;\n\n      .amount-control {\n        display: flex;\n        align-items: center;\n\n        .minus-btn, .plus-btn {\n          width: 60rpx;\n          height: 60rpx;\n          border-radius: 30rpx;\n          display: flex;\n          justify-content: center;\n          align-items: center;\n          transition: all 0.3s ease;\n\n          &.minus-btn {\n            background-color: rgba(244, 67, 54, 0.1);\n\n            &:active {\n              background-color: rgba(244, 67, 54, 0.2);\n            }\n\n            text {\n              color: #F44336;\n            }\n          }\n\n          &.plus-btn {\n            background-color: rgba(76, 175, 80, 0.1);\n\n            &:active {\n              background-color: rgba(76, 175, 80, 0.2);\n            }\n\n            text {\n              color: #4CAF50;\n            }\n          }\n\n          text {\n            font-size: 36rpx;\n            font-weight: bold;\n          }\n        }\n\n        .amount-input {\n          width: 80rpx;\n          text-align: center;\n          margin: 0 15rpx;\n          font-size: 30rpx;\n          font-weight: 500;\n          color: #333;\n        }\n      }\n    }\n\n    .food-nutrition-info {\n      margin-right: 20rpx;\n\n      .food-calorie {\n        font-size: 30rpx;\n        color: #FF9800;\n        font-weight: bold;\n        display: block;\n        text-align: right;\n        margin-bottom: 10rpx;\n      }\n\n      .nutrition-detail {\n        display: flex;\n        flex-direction: column;\n\n        text {\n          font-size: 22rpx;\n          color: #666;\n          margin-top: 6rpx;\n        }\n      }\n    }\n\n    .delete-btn {\n      width: 70rpx;\n      height: 70rpx;\n      display: flex;\n      justify-content: center;\n      align-items: center;\n      border-radius: 35rpx;\n      background-color: rgba(244, 67, 54, 0.1);\n      transition: all 0.3s ease;\n\n      &:active {\n        background-color: rgba(244, 67, 54, 0.2);\n      }\n\n      image {\n        width: 36rpx;\n        height: 36rpx;\n      }\n    }\n  }\n}\n\n.bottom-bar {\n  position: fixed;\n  left: 0;\n  bottom: 0;\n  right: 0;\n  height: 120rpx;\n  background-color: #ffffff;\n  display: flex;\n  align-items: center;\n  padding: 0 30rpx;\n  box-shadow: 0 -6rpx 20rpx rgba(0, 0, 0, 0.05);\n  z-index: 100;\n\n  .total-calorie {\n    flex: 1;\n\n    text {\n      font-size: 32rpx;\n      color: #FF9800;\n      font-weight: bold;\n      background: linear-gradient(90deg, #FF9800, #FF5722);\n      -webkit-background-clip: text;\n      -webkit-text-fill-color: transparent;\n    }\n  }\n\n  .save-btn {\n    width: 240rpx;\n    height: 80rpx;\n    background: linear-gradient(135deg, #4CAF50, #8BC34A);\n    border-radius: 40rpx;\n    display: flex;\n    justify-content: center;\n    align-items: center;\n    box-shadow: 0 6rpx 12rpx rgba(76, 175, 80, 0.2);\n    transition: all 0.3s ease;\n\n    &:active {\n      transform: scale(0.95);\n      box-shadow: 0 3rpx 6rpx rgba(76, 175, 80, 0.2);\n    }\n\n    text {\n      font-size: 30rpx;\n      color: #ffffff;\n      font-weight: 500;\n    }\n  }\n}\n\n.food-search-modal {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  z-index: 1000;\n\n  .modal-mask {\n    position: absolute;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    background-color: rgba(0, 0, 0, 0.5);\n    animation: fadeIn 0.3s ease;\n  }\n\n  .modal-content {\n    position: absolute;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    background-color: #ffffff;\n    border-radius: 30rpx 30rpx 0 0;\n    overflow: hidden;\n    max-height: 85vh;\n    display: flex;\n    flex-direction: column;\n    animation: slideUp 0.3s ease-out;\n    box-shadow: 0 -10rpx 30rpx rgba(0, 0, 0, 0.1);\n    transition: height 0.3s ease;\n\n    .modal-header {\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      height: 100rpx;\n      padding: 0 30rpx;\n      border-bottom: 1rpx solid #f0f0f0;\n\n      .modal-title {\n        font-size: 34rpx;\n        font-weight: bold;\n        color: #333;\n      }\n\n      .close-btn {\n        width: 70rpx;\n        height: 70rpx;\n        display: flex;\n        justify-content: center;\n        align-items: center;\n        border-radius: 35rpx;\n        transition: all 0.3s ease;\n\n        &:active {\n          background-color: #f5f5f5;\n        }\n\n        image {\n          width: 32rpx;\n          height: 32rpx;\n        }\n      }\n    }\n\n    .search-box {\n      display: flex;\n      align-items: center;\n      padding: 20rpx 30rpx;\n      margin-bottom: 10rpx;\n\n      image {\n        width: 36rpx;\n        height: 36rpx;\n        margin-right: 15rpx;\n        opacity: 0.6;\n        position: absolute;\n        left: 50rpx;\n        z-index: 1;\n      }\n\n      input {\n        flex: 1;\n        height: 70rpx;\n        background-color: #f5f5f5;\n        border-radius: 35rpx;\n        padding: 0 30rpx 0 56rpx;\n        font-size: 28rpx;\n        position: relative;\n        box-shadow: inset 0 2rpx 5rpx rgba(0, 0, 0, 0.03);\n\n        &:focus {\n          background-color: #f0f0f0;\n        }\n      }\n    }\n\n    .category-scroll {\n      white-space: nowrap;\n      margin: 0 30rpx 20rpx;\n      height: 80rpx;\n\n      .category-item {\n        display: inline-block;\n        height: 64rpx;\n        padding: 0 30rpx;\n        margin-right: 15rpx;\n        border-radius: 32rpx;\n        background-color: #f5f5f5;\n        transition: all 0.3s ease;\n        position: relative;\n        overflow: hidden;\n\n        &:active {\n          transform: scale(0.95);\n        }\n\n        &.active {\n          background: linear-gradient(135deg, #4CAF50, #8BC34A);\n          box-shadow: 0 4rpx 8rpx rgba(76, 175, 80, 0.2);\n\n          &:after {\n            content: '';\n            position: absolute;\n            bottom: 0;\n            left: 0;\n            width: 100%;\n            height: 100%;\n            background: rgba(255, 255, 255, 0.1);\n            border-radius: 32rpx;\n          }\n\n          text {\n            color: #ffffff;\n            font-weight: 500;\n          }\n        }\n\n        text {\n          font-size: 26rpx;\n          color: #666;\n          line-height: 64rpx;\n        }\n      }\n    }\n\n    .food-scroll {\n      height: 650rpx;\n      min-height: 400rpx;\n      &::-webkit-scrollbar {\n        width: 0;\n        height: 0;\n        display: none;\n      }\n\n      .food-list-container {\n        padding: 10rpx 30rpx 30rpx;\n\n        .food-card {\n          background-color: #ffffff;\n          border-radius: 16rpx;\n          padding: 25rpx;\n          margin-bottom: 20rpx;\n          box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);\n          transition: all 0.3s ease;\n          border: 1rpx solid rgba(0, 0, 0, 0.03);\n\n          &:active {\n            transform: scale(0.98);\n            box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.03);\n          }\n\n          .food-card-main {\n            display: flex;\n            justify-content: space-between;\n            margin-bottom: 15rpx;\n\n            .food-info {\n              flex: 1;\n\n              .food-name {\n                font-size: 30rpx;\n                font-weight: 500;\n                color: #333;\n                margin-bottom: 12rpx;\n                display: block;\n              }\n            }\n\n            .food-nutrition {\n              text-align: right;\n              margin-left: 20rpx;\n\n              .food-calorie {\n                font-size: 28rpx;\n                color: #FF9800;\n                font-weight: bold;\n                display: block;\n                margin-bottom: 10rpx;\n                background: linear-gradient(90deg, #FF9800, #FF5722);\n                -webkit-background-clip: text;\n                -webkit-text-fill-color: transparent;\n              }\n\n              .food-unit {\n                font-size: 24rpx;\n                color: #999;\n              }\n            }\n          }\n\n          .food-detail {\n            display: flex;\n            justify-content: space-between;\n            background-color: #f9f9f9;\n            border-radius: 12rpx;\n            padding: 15rpx;\n\n            .nutrition-item {\n              text-align: center;\n              padding: 0 10rpx;\n\n              .nutrition-value {\n                font-size: 26rpx;\n                color: #333;\n                font-weight: 500;\n                display: block;\n                margin-bottom: 5rpx;\n              }\n\n              .nutrition-label {\n                font-size: 22rpx;\n                color: #666;\n              }\n            }\n          }\n        }\n      }\n    }\n  }\n}\n\n.food-selection-panel {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  z-index: 1000;\n\n  .modal-mask {\n    position: absolute;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    background-color: rgba(0, 0, 0, 0.5);\n  }\n\n  .selection-content {\n    position: absolute;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    background-color: #ffffff;\n    border-radius: 20rpx 20rpx 0 0;\n    overflow: hidden;\n\n    .selection-header {\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      height: 88rpx;\n      padding: 0 30rpx;\n      border-bottom: 1rpx solid #f0f0f0;\n\n      .selection-title {\n        font-size: 32rpx;\n        font-weight: bold;\n        color: #333;\n      }\n\n      .close-btn {\n        width: 60rpx;\n        height: 60rpx;\n        display: flex;\n        justify-content: center;\n        align-items: center;\n\n        image {\n          width: 32rpx;\n          height: 32rpx;\n        }\n      }\n    }\n\n    .selection-tabs {\n      display: flex;\n      height: 80rpx;\n      border-bottom: 1rpx solid #f0f0f0;\n\n      .tab-item {\n        flex: 1;\n        display: flex;\n        justify-content: center;\n        align-items: center;\n        position: relative;\n\n        text {\n          font-size: 28rpx;\n          color: #666;\n        }\n\n        &.active {\n          text {\n            color: #4CAF50;\n            font-weight: 500;\n          }\n\n          &:after {\n            content: '';\n            position: absolute;\n            bottom: 0;\n            left: 50%;\n            transform: translateX(-50%);\n            width: 40rpx;\n            height: 4rpx;\n            background-color: #4CAF50;\n          }\n        }\n      }\n    }\n\n    .selection-body {\n      padding: 30rpx;\n\n      .food-measure {\n        text-align: center;\n        margin-bottom: 20rpx;\n\n        text {\n          font-size: 28rpx;\n          color: #666;\n        }\n      }\n\n      .amount-control {\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        margin-bottom: 30rpx;\n\n        .control-btn {\n          width: 80rpx;\n          height: 80rpx;\n          border-radius: 40rpx;\n          display: flex;\n          justify-content: center;\n          align-items: center;\n          background-color: #f5f5f5;\n\n          text {\n            font-size: 36rpx;\n            color: #333;\n          }\n        }\n\n        .amount-input {\n          width: 100rpx;\n          height: 80rpx;\n          text-align: center;\n          font-size: 32rpx;\n          margin: 0 20rpx;\n        }\n      }\n\n      .grams-input-container {\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        margin-bottom: 30rpx;\n\n        .grams-input {\n          width: 200rpx;\n          height: 80rpx;\n          background-color: #f5f5f5;\n          border-radius: 10rpx;\n          text-align: center;\n          font-size: 32rpx;\n        }\n\n        .grams-label {\n          font-size: 28rpx;\n          color: #666;\n          margin-left: 10rpx;\n        }\n      }\n\n      .nutrition-preview {\n        background-color: #f8f8f8;\n        border-radius: 10rpx;\n        padding: 20rpx;\n        display: flex;\n        flex-wrap: wrap;\n\n        .preview-item {\n          width: 50%;\n          margin-bottom: 20rpx;\n\n          .preview-label {\n            font-size: 24rpx;\n            color: #999;\n            display: block;\n            margin-bottom: 5rpx;\n          }\n\n          .preview-value {\n            font-size: 28rpx;\n            color: #333;\n            font-weight: 500;\n          }\n        }\n      }\n    }\n\n    .selection-footer {\n      display: flex;\n      height: 100rpx;\n      border-top: 1rpx solid #f0f0f0;\n\n      .cancel-btn {\n        width: 50%;\n        display: flex;\n        justify-content: center;\n        align-items: center;\n\n        text {\n          font-size: 30rpx;\n          color: #666;\n        }\n      }\n\n      .confirm-btn {\n        width: 50%;\n        display: flex;\n        justify-content: center;\n        align-items: center;\n        background-color: #4CAF50;\n\n        text {\n          font-size: 30rpx;\n          color: #ffffff;\n        }\n      }\n    }\n  }\n}\n\n/* 加载和动画等，全都保留 */\n</style>\n", "import mod from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./add.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./add.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1751160935239\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}