@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.profile {
  min-height: 100vh;
  background-color: #f8f8f8;
}
/* 裁剪组件相关样式 */
.cropper-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.8);
  z-index: 999;
}
/* 模态框样式 */
.modal-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 998;
}
.modal-container {
  position: fixed;
  left: 50%;
  top: 50%;
  -webkit-transform: translate(-50%, -50%);
          transform: translate(-50%, -50%);
  width: 80%;
  background-color: #fff;
  border-radius: 12rpx;
  z-index: 999;
  overflow: hidden;
}
.modal-header {
  padding: 30rpx;
  text-align: center;
  font-size: 32rpx;
  font-weight: bold;
  border-bottom: 1rpx solid #eee;
}
.modal-body {
  padding: 30rpx;
}
.modal-input {
  width: 100%;
  height: 80rpx;
  background-color: #f8f8f8;
  border-radius: 8rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
}
.modal-footer {
  display: flex;
  border-top: 1rpx solid #eee;
}
.modal-btn {
  flex: 1;
  height: 90rpx;
  line-height: 90rpx;
  text-align: center;
  font-size: 32rpx;
  border-radius: 0;
}
.modal-btn.cancel {
  color: #999;
  border-right: 1rpx solid #eee;
}
.modal-btn.confirm {
  color: #4CAF50;
}
/* 个人信息列表样式 */
.info-list {
  margin-top: 20rpx;
  background-color: #ffffff;
}
.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f5f5f5;
}
.info-item .item-label {
  font-size: 30rpx;
  color: #333;
}
.info-item .item-content {
  display: flex;
  align-items: center;
}
.info-item .item-content .item-value {
  font-size: 28rpx;
  color: #666;
  margin-right: 20rpx;
}
.info-item .item-content .item-value.disabled {
  color: #999;
}
.info-item .item-content .arrow-icon {
  width: 32rpx;
  height: 32rpx;
}
.info-item .avatar-wrapper {
  display: flex;
  align-items: center;
}
.info-item .avatar-small {
  width: 80rpx;
  height: 80rpx;
  border-radius: 40rpx;
  margin-right: 20rpx;
}

