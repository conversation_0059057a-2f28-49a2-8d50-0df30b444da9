<view class="login"><view class="custom-nav"><view class="nav-title">登录</view></view><view class="logo-container"><image class="logo" src="/static/images/logo.png"></image><text class="app-name">饮食记录</text></view><block wx:if="{{loginType==='email'}}"><view class="login-form"><view class="form-item"><input class="input" placeholder="请输入邮箱" data-event-opts="{{[['input',[['__set_model',['','email','$event',[]]]]]]}}" value="{{email}}" bindinput="__e"/></view><view class="form-item password-input"><input class="input" password="{{!showPassword}}" placeholder="请输入密码" data-event-opts="{{[['input',[['__set_model',['','password','$event',[]]]]]]}}" value="{{password}}" bindinput="__e"/><view data-event-opts="{{[['tap',[['togglePassword',['$event']]]]]}}" class="toggle-password" catchtap="__e"><image class="eye-icon" src="{{showPassword?'/static/icons/eye-open.png':'/static/icons/eye-close.png'}}" mode="aspectFit"></image></view></view><button class="login-btn" disabled="{{loading}}" data-event-opts="{{[['tap',[['emailLogin',['$event']]]]]}}" bindtap="__e"><block wx:if="{{!loading}}"><text>登录</text></block><block wx:else><view class="loading"></view></block></button><view data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" class="switch-login-type" bindtap="__e"><text>使用微信一键登录</text></view></view></block><block wx:else><view class="wechat-login"><button class="wechat-btn" open-type="getUserInfo" disabled="{{loading}}" data-event-opts="{{[['getuserinfo',[['getWXUserInfo',['$event']]]]]}}" bindgetuserinfo="__e"><image class="wechat-icon" src="/static/icons/wechat.png"></image><block wx:if="{{!loading}}"><text>微信一键登录</text></block><block wx:else><view class="loading"></view></block></button><view data-event-opts="{{[['tap',[['e1',['$event']]]]]}}" class="switch-login-type" bindtap="__e"><text>使用邮箱密码登录</text></view></view></block><view class="agreement"><text class="agreement-text">登录即表示您同意</text><text data-event-opts="{{[['tap',[['showUserAgreement',['$event']]]]]}}" class="agreement-link" bindtap="__e">《用户协议》</text><text class="agreement-text">和</text><text data-event-opts="{{[['tap',[['showPrivacyPolicy',['$event']]]]]}}" class="agreement-link" bindtap="__e">《隐私政策》</text></view></view>