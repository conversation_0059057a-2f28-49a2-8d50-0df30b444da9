@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.mine {
  min-height: 100vh;
  background-color: #f8f8f8;
}
.safe-area {
  height: 80rpx;
  /* 与其他页面保持一致的顶部安全区域高度 */
  background-color: #4CAF50;
}
.user-info {
  position: relative;
  display: flex;
  align-items: center;
  padding: 20rpx 30rpx 30rpx;
  background-color: #4CAF50;
}
.user-info .avatar-container {
  margin-right: 25rpx;
}
.user-info .avatar-container .avatar {
  width: 110rpx;
  height: 110rpx;
  border-radius: 55rpx;
  border: 4rpx solid #ffffff;
}
.user-info .user-detail {
  flex: 1;
}
.user-info .user-detail .nickname {
  font-size: 34rpx;
  font-weight: bold;
  color: #ffffff;
  margin-bottom: 15rpx;
}
.user-info .user-detail .user-stats {
  display: flex;
}
.user-info .user-detail .user-stats .stat-item {
  margin-right: 25rpx;
}
.user-info .user-detail .user-stats .stat-item .stat-value {
  font-size: 30rpx;
  color: #ffffff;
  font-weight: bold;
  display: block;
}
.user-info .user-detail .user-stats .stat-item .stat-label {
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.8);
}
.user-info .edit-btn {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}
.user-info .edit-btn image {
  width: 36rpx;
  height: 36rpx;
}
.menu-list {
  padding: 20rpx;
}
.menu-list .menu-section {
  background-color: #ffffff;
  border-radius: 10rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
}
.menu-list .menu-item {
  display: flex;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}
.menu-list .menu-item:last-child {
  border-bottom: none;
}
.menu-list .menu-item .menu-icon {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 20rpx;
}
.menu-list .menu-item .menu-icon image {
  width: 40rpx;
  height: 40rpx;
}
.menu-list .menu-item .menu-content {
  flex: 1;
}
.menu-list .menu-item .menu-content .menu-title {
  font-size: 30rpx;
  color: #333;
  margin-bottom: 6rpx;
  display: block;
}
.menu-list .menu-item .menu-content .menu-desc {
  font-size: 24rpx;
  color: #999;
}
.menu-list .menu-item .menu-arrow {
  width: 40rpx;
  height: 40rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}
.menu-list .menu-item .menu-arrow image {
  width: 24rpx;
  height: 24rpx;
}
.menu-list .logout-btn {
  margin-top: 60rpx;
  background-color: #ffffff;
  border-radius: 10rpx;
  padding: 30rpx;
  text-align: center;
}
.menu-list .logout-btn text {
  font-size: 32rpx;
  color: #FF5252;
}

