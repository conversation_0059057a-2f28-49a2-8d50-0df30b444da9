<view class="nutrition-analysis"><view class="safe-area"></view><view class="header"><text class="title">营养分析</text></view><view class="date-selector"><view class="date-actions"><view data-event-opts="{{[['tap',[['changeDate',[-1]]]]]}}" class="date-arrow" bindtap="__e"><image src="/static/icons/arrow-left.png"></image></view><picker mode="date" value="{{datePickerValue}}" end="{{endDate}}" data-event-opts="{{[['change',[['onDateChange',['$event']]]]]}}" bindchange="__e"><view class="date-display"><text class="date-title">{{dateTitle}}</text><text class="date-value">{{formattedDate}}</text></view></picker><view data-event-opts="{{[['tap',[['changeDate',[1]]]]]}}" class="date-arrow" bindtap="__e"><image src="/static/icons/arrow-right.png"></image></view></view></view><block wx:if="{{isLoading}}"><view class="loading-container"><view class="loading-box"><view class="loading-spinner"></view><text class="loading-text">加载中...</text></view></view></block><block wx:else><view class="content-container"><view class="core-nutrients card"><view class="section-header"><text class="section-title">核心营养素</text><text class="date-info">{{formattedDate}}</text></view><view class="nutrients-grid"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="nutrient-card"><view class="nutrient-header"><text class="nutrient-label">{{item.$orig.label}}</text><text class="nutrient-value">{{item.$orig.value}}</text></view><view class="progress-container"><view class="progress-bar"><view class="progress" style="{{'width:'+(item.$orig.percentage+'%')+';'+('background-color:'+(item.$orig.color)+';')}}"></view></view><text class="percentage-text">{{item.m0}}</text></view></view></block></view></view><view class="trend-chart card"><view class="section-header"><text class="section-title">卡路里周趋势</text><view class="legend"><view class="legend-item"><view class="legend-color" style="{{'background-color:'+(calorieColor)+';'}}"></view><text class="legend-label">热量</text></view></view></view><view class="chart-container"><block wx:if="{{showCalorieChart}}"><qiun-data-charts class="vue-ref" vue-id="7da64b0e-1" type="line" opts="{{calorieChartOpts}}" chartData="{{calorieChartData}}" canvasId="calorieChart" data-ref="calorieChart" bind:__l="__l"></qiun-data-charts></block></view></view><view class="trend-chart card"><view class="section-header"><text class="section-title">营养素周趋势</text><view class="legend"><block wx:for="{{nutrientLegendItems}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="legend-item"><view class="legend-color" style="{{'background-color:'+(item.color)+';'}}"></view><text class="legend-label">{{item.label}}</text></view></block></view></view><view class="chart-container"><block wx:if="{{showNutrientChart}}"><qiun-data-charts class="vue-ref" vue-id="7da64b0e-2" type="line" opts="{{nutrientChartOpts}}" chartData="{{nutrientChartData}}" canvasId="nutrientChart" data-ref="nutrientChart" bind:__l="__l"></qiun-data-charts></block></view></view><view class="nutrition-advice card"><view class="section-header"><text class="section-title">营养建议</text></view><view class="advice-content"><block wx:for="{{$root.l1}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="advice-item"><view class="{{['advice-icon',item.$orig.type||'info']}}"><image src="{{item.m1}}"></image></view><view class="advice-text"><text class="advice-title">{{item.$orig.title}}</text><text class="advice-desc">{{item.$orig.description}}</text></view></view></block></view></view></view></block></view>