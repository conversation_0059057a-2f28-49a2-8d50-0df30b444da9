@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.login {
  min-height: 100vh;
  padding: 40rpx;
  padding-top: 0;
  /* 移除上边距，为导航栏留出空间 */
  background-color: #ffffff;
  display: flex;
  flex-direction: column;
  align-items: center;
}
/* 自定义导航栏样式 */
.custom-nav {
  width: 100%;
  padding-top: 25px;
  height: 70px;
  display: flex;
  align-items: flex-end;
  justify-content: center;
  background-color: #ffffff;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 999;
}
.nav-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  padding-bottom: 15px;
}
.logo-container {
  margin-top: calc(25px + 70px + 40rpx);
  margin-bottom: 60rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.logo-container .logo {
  width: 160rpx;
  height: 160rpx;
  margin-bottom: 20rpx;
}
.logo-container .app-name {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}
.login-form {
  width: 100%;
}
.login-form .form-item {
  margin-bottom: 30rpx;
}
.login-form .form-item .input {
  width: 100%;
  height: 90rpx;
  background-color: #f8f8f8;
  border-radius: 45rpx;
  padding: 0 40rpx;
  font-size: 28rpx;
}
.login-form .login-btn {
  width: 100%;
  height: 90rpx;
  background-color: #4CAF50;
  color: #ffffff;
  border-radius: 45rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 32rpx;
  font-weight: bold;
  margin-top: 40rpx;
  margin-bottom: 30rpx;
}
.login-form .login-btn[disabled] {
  background-color: #a5d6a7;
}
.wechat-login {
  width: 100%;
}
.wechat-login .wechat-btn {
  width: 100%;
  height: 90rpx;
  background-color: #07C160;
  color: #ffffff;
  border-radius: 45rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 30rpx;
}
.wechat-login .wechat-btn[disabled] {
  background-color: #7fccb5;
}
.wechat-login .wechat-btn .wechat-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 10rpx;
}
.switch-login-type {
  text-align: center;
  margin-bottom: 40rpx;
}
.switch-login-type text {
  font-size: 28rpx;
  color: #4CAF50;
}
.agreement {
  position: absolute;
  bottom: 60rpx;
  left: 0;
  right: 0;
  text-align: center;
}
.agreement .agreement-text {
  font-size: 24rpx;
  color: #999;
}
.agreement .agreement-link {
  font-size: 24rpx;
  color: #4CAF50;
}
.loading {
  width: 40rpx;
  height: 40rpx;
  border: 4rpx solid #ffffff;
  border-radius: 50%;
  border-top-color: transparent;
  -webkit-animation: spin 1s linear infinite;
          animation: spin 1s linear infinite;
}
@-webkit-keyframes spin {
from {
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
}
to {
    -webkit-transform: rotate(360deg);
            transform: rotate(360deg);
}
}
@keyframes spin {
from {
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
}
to {
    -webkit-transform: rotate(360deg);
            transform: rotate(360deg);
}
}
.password-input {
  position: relative;
}
.password-input .toggle-password {
  position: absolute;
  right: 40rpx;
  top: 50%;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2;
  background-color: transparent;
}
.password-input .toggle-password .eye-icon {
  width: 32rpx;
  height: 32rpx;
}

