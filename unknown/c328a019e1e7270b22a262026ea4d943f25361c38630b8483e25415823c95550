package v1

import "time"

// FoodQueryReq 食物查询请求
type FoodQueryReq struct {
	Current    int  `form:"current" binding:"omitempty,min=1" validate:"omitempty,min=1"`                // 当前页，默认1
	Size       int  `form:"size" binding:"omitempty,min=1,max=100" validate:"omitempty,min=1,max=100"`   // 每页大小，默认10
	Keyword    string `form:"keyword" binding:"omitempty,max=100" validate:"omitempty,max=100"`            // 关键词搜索
	CategoryID *int `form:"categoryId" binding:"omitempty,min=1" validate:"omitempty,min=1"`             // 分类ID筛选
}

// FoodCreateReq 创建食物请求
type FoodCreateReq struct {
	Name        string   `json:"name" binding:"required,max=255" validate:"required,max=255"`              // 食物名称（与Java版本字段名一致）
	Measure     *string  `json:"measure" binding:"omitempty,max=100" validate:"omitempty,max=100"`         // 计量单位
	Grams       *float64 `json:"grams" binding:"omitempty,min=0" validate:"omitempty,min=0"`               // 克数
	Calories    *float64 `json:"calories" binding:"omitempty,min=0" validate:"omitempty,min=0"`            // 卡路里
	Protein     *float64 `json:"protein" binding:"omitempty,min=0" validate:"omitempty,min=0"`             // 蛋白质含量
	Fat         *float64 `json:"fat" binding:"omitempty,min=0" validate:"omitempty,min=0"`                 // 总脂肪含量
	SatFat      *float64 `json:"satFat" binding:"omitempty,min=0" validate:"omitempty,min=0"`              // 饱和脂肪含量
	Fiber       *string  `json:"fiber" binding:"omitempty,max=20" validate:"omitempty,max=20"`             // 纤维含量
	Carbs       *float64 `json:"carbs" binding:"omitempty,min=0" validate:"omitempty,min=0"`               // 碳水化合物含量
	ImageURL    *string  `json:"imageUrl" binding:"omitempty,max=255" validate:"omitempty,max=255,url"`    // 食物图片URL
	CategoryID  *int     `json:"categoryId" binding:"omitempty,min=1" validate:"omitempty,min=1"`          // 分类ID
}

// FoodUpdateReq 更新食物请求
type FoodUpdateReq struct {
	ID          int      `json:"-"`                                                                         // 食物ID（从URL路径获取，不从JSON解析）
	Name        string   `json:"name" binding:"required,max=255" validate:"required,max=255"`              // 食物名称（与Java版本字段名一致）
	Measure     *string  `json:"measure" binding:"omitempty,max=100" validate:"omitempty,max=100"`         // 计量单位
	Grams       *float64 `json:"grams" binding:"omitempty,min=0" validate:"omitempty,min=0"`               // 克数
	Calories    *float64 `json:"calories" binding:"omitempty,min=0" validate:"omitempty,min=0"`            // 卡路里
	Protein     *float64 `json:"protein" binding:"omitempty,min=0" validate:"omitempty,min=0"`             // 蛋白质含量
	Fat         *float64 `json:"fat" binding:"omitempty,min=0" validate:"omitempty,min=0"`                 // 总脂肪含量
	SatFat      *float64 `json:"satFat" binding:"omitempty,min=0" validate:"omitempty,min=0"`              // 饱和脂肪含量
	Fiber       *string  `json:"fiber" binding:"omitempty,max=20" validate:"omitempty,max=20"`             // 纤维含量
	Carbs       *float64 `json:"carbs" binding:"omitempty,min=0" validate:"omitempty,min=0"`               // 碳水化合物含量
	ImageURL    *string  `json:"imageUrl" binding:"omitempty,max=255" validate:"omitempty,max=255,url"`    // 食物图片URL
	CategoryID  *int     `json:"categoryId" binding:"omitempty,min=1" validate:"omitempty,min=1"`          // 分类ID
}

// FoodResponse 食物响应
type FoodResponse struct {
	ID         int      `json:"id"`                   // 食物ID
	Name       *string  `json:"name"`                 // 食物名称 (与Java保持一致)
	Measure    *string  `json:"measure"`              // 份量描述
	Grams      *float64 `json:"grams"`                // 克数
	Calories   *float64 `json:"calories"`             // 卡路里
	Protein    *float64 `json:"protein"`              // 蛋白质含量(g)
	Fat        *float64 `json:"fat"`                  // 总脂肪含量(g)
	SatFat     *float64 `json:"satFat"`               // 饱和脂肪含量(g)
	Carbs      *float64 `json:"carbs"`                // 碳水化合物含量(g)
	Category   *string  `json:"category"`             // 分类名称 (与Java保持一致)
	CategoryID *int     `json:"categoryId"`           // 分类ID
	Desc       *string  `json:"desc"`                 // 描述信息
	Unit       *string  `json:"unit"`                 // 单位
	ImageURL   *string  `json:"imageUrl"`             // 食物图片URL
	Remark     *string  `json:"remark"`               // 备注
}

// FoodListResponse 食物列表响应（用于分页）
type FoodListResponse struct {
	Total   int64           `json:"total"`   // 总记录数
	Records []*FoodResponse `json:"records"` // 食物列表
	Current int             `json:"current"` // 当前页码
	Size    int             `json:"size"`    // 每页大小
}

// FoodCategoryQueryReq 食物分类查询请求
type FoodCategoryQueryReq struct {
	Current int    `form:"current" binding:"omitempty,min=1" validate:"omitempty,min=1"`              // 当前页，默认1
	Size    int    `form:"size" binding:"omitempty,min=1,max=100" validate:"omitempty,min=1,max=100"` // 每页大小，默认10
	Keyword string `form:"keyword" binding:"omitempty,max=100" validate:"omitempty,max=100"`          // 关键词搜索
}

// FoodCategoryCreateReq 创建食物分类请求
type FoodCategoryCreateReq struct {
	Name        string  `json:"name" binding:"required,max=100" validate:"required,max=100"`             // 分类名称
	Description *string `json:"description" binding:"omitempty,max=255" validate:"omitempty,max=255"`    // 分类描述
	Color       *string `json:"color" binding:"omitempty,max=20" validate:"omitempty,max=20"`            // 分类颜色
	SortOrder   int     `json:"sortOrder" binding:"omitempty,min=0" validate:"omitempty,min=0"`          // 排序顺序
}

// FoodCategoryUpdateReq 更新食物分类请求
type FoodCategoryUpdateReq struct {
	ID          int     `json:"-"`                                                                        // 分类ID（从URL路径获取，不从JSON解析）
	Name        string  `json:"name" binding:"required,max=100" validate:"required,max=100"`             // 分类名称
	Description *string `json:"description" binding:"omitempty,max=255" validate:"omitempty,max=255"`    // 分类描述
	Color       *string `json:"color" binding:"omitempty,max=20" validate:"omitempty,max=20"`            // 分类颜色
	SortOrder   int     `json:"sortOrder" binding:"omitempty,min=0" validate:"omitempty,min=0"`          // 排序顺序
}

// FoodCategoryResponse 食物分类响应
type FoodCategoryResponse struct {
	ID          int        `json:"id"`          // 分类ID
	Name        string     `json:"name"`        // 分类名称
	Description *string    `json:"description"` // 分类描述
	Color       *string    `json:"color"`       // 分类颜色
	SortOrder   int        `json:"sortOrder"`   // 排序顺序
	FoodCount   int        `json:"foodCount"`   // 该分类下的食物数量（与Java版本保持一致）
	CreatedAt   time.Time  `json:"createdAt"`   // 创建时间
	UpdatedAt   time.Time  `json:"updatedAt"`   // 更新时间
}

// FoodCategoryListResponse 食物分类列表响应（用于分页）
type FoodCategoryListResponse struct {
	Total   int64                   `json:"total"`   // 总记录数
	Records []*FoodCategoryResponse `json:"records"` // 分类列表
	Current int                     `json:"current"` // 当前页码
	Size    int                     `json:"size"`    // 每页大小
}

// UploadImageReq 上传图片请求
type UploadImageReq struct {
	FoodID int `json:"foodId" binding:"required,min=1" validate:"required,min=1"` // 食物ID
}

// UploadImageResponse 上传图片响应
type UploadImageResponse struct {
	UploadURL string `json:"uploadUrl"` // 预签名上传URL
	ImageURL  string `json:"imageUrl"`  // 图片访问URL
}

// FoodImportReq 批量导入食物请求（与Java版本保持一致）
type FoodImportReq struct {
	Foods []*FoodResponse `json:"foods" binding:"required,min=1" validate:"required,min=1"` // 食物数据列表
}

// FoodImportResponse 批量导入食物响应（与Java版本保持一致）
type FoodImportResponse struct {
	SuccessCount  int      `json:"successCount"`  // 成功导入数量
	FailCount     int      `json:"failCount"`     // 失败数量
	ErrorMessages []string `json:"errorMessages"` // 错误信息列表
}
