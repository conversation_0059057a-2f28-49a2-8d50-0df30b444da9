{"version": 3, "sources": ["webpack:///D:/HBuilderProjects/springboot-dubbo-front-wechat/pages/mine/profile.vue?4d27", "uni-app:///pages/mine/profile.vue", "webpack:///D:/HBuilderProjects/springboot-dubbo-front-wechat/pages/mine/profile.vue?81bd", "webpack:///D:/HBuilderProjects/springboot-dubbo-front-wechat/pages/mine/profile.vue?d9db", "uni-app:///main.js", null, "webpack:///D:/HBuilderProjects/springboot-dubbo-front-wechat/pages/mine/profile.vue?3cee", "webpack:///D:/HBuilderProjects/springboot-dubbo-front-wechat/pages/mine/profile.vue?1293"], "names": ["components", "QfImageCropper", "data", "userId", "username", "email", "showCropper", "tempImagePath", "croppedImagePath", "showModal", "modalTitle", "modalInputValue", "current<PERSON><PERSON>", "showUploadMask", "computed", "userInfo", "is<PERSON>ogin", "maskedEmail", "avatarSource", "onLoad", "methods", "updateUserInfo", "initData", "openModal", "cancelModal", "confirmModal", "saveField", "userData", "uni", "title", "then", "icon", "catch", "console", "chooseAvatarAndUpload", "count", "sizeType", "sourceType", "success", "fail", "reject", "tempFiles", "tempFile<PERSON>ath", "maxSize", "onCropComplete", "uploadCroppedImage", "fileType", "contentType", "uploadRes", "fs", "filePath", "position", "wx", "url", "method", "header", "responseType", "resolve", "uploadResult", "avatar<PERSON><PERSON>", "avatar", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAomB,CAAgB,inBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;ACyDxnB;AACA;AAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA,eAGA;EACAA;IACAC;EACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;IACA;EACA;;EACAC,0CACA;IACAC;MAAA;IAAA;IACAC;MAAA;IAAA;EACA;IACAC;MACA;;MAEA;MACA;MACA;;MAEA;MACA;MACA;MAEA;IACA;IACA;IACAC;MACA;IACA;EAAA,EACA;EACAC;IACA;IACA;EACA;EACAC,yCACA;IACAC;EACA;IACAC;MACA;MACA;MACA;MACA;IACA;IACA;IACAC;MACA;MACA;MACA;MACA;IACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;MACA;QACA;MACA;QACA;MACA;;MAEA;MACA;;MAEA;MACA;IACA;IACA;IACAC;MACA;MACA;;MAEA;MACA;QACAC;MACA;QACAA;MACA;;MAEA;MACAC;QAAAC;MAAA;MAEA,8BACAC;QACAF;QACAA;UACAC;UACAE;QACA;MACA,GACAC;QACAJ;QACAA;UACAC;UACAE;QACA;QACAE;MACA;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAGA;kBACAN;oBACAO;oBAAA;oBACAC;oBAAA;oBACAC;oBAAA;oBACAC;oBACAC;sBACA;sBACA;wBACA;wBACAN;wBACA;wBACA;sBACA;sBACA;sBACAO;oBACA;kBACA;gBACA;cAAA;gBAAA;gBAlBAC;gBAAA,MAqBA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAIAC,kCAEA;gBACAC;gBAAA,MACAF;kBAAA;kBAAA;gBAAA;gBACAb;kBACAC;kBACAE;gBACA;gBAAA;cAAA;gBAIA;gBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAE;gBACA;gBACAL;kBACAC;kBACAE;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IACA;IACAa;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,IAEA;kBAAA;kBAAA;gBAAA;gBACAjB;kBACAC;kBACAE;gBACA;gBAAA;cAAA;gBAIA;gBACA;gBACAH;kBACAC;gBACA;;gBAEA;gBACAiB;gBAAA,eAEAA;gBAAA,kCACA,8BACA,+BAGA,8BAGA;gBAAA;cAAA;gBALAC;gBAAA;cAAA;gBAGAA;gBAAA;cAAA;gBAGAA;gBAAA;cAAA;gBAGAA;cAAA;gBAAA;gBAAA;gBAAA,OAKA;cAAA;gBAAAC;gBAAA,kBACAA,4FAEA;gBAAA;gBAAA,OACA;kBACA;kBACA;kBACAC;oBACAC;oBACAC;oBACAb;sBACA;sBACA;;sBAEA;sBACAc;wBACAC;wBACAC;wBACApD;wBAAA;wBACAqD;0BACA;wBACA;;wBACAC;wBACAlB;0BACA;4BACAmB;0BACA;4BACAxB;4BACAA;4BACAO;0BACA;wBACA;wBACAD;0BACAN;0BACAO;wBACA;sBACA;oBACA;oBACAD;sBACAN;sBACAO;oBACA;kBACA;gBACA;cAAA;gBAvCAkB;gBAyCAzB;;gBAEA;gBAAA;gBAAA,OACA;cAAA;gBAAA0B;gBAAA,MACAA;kBAAA;kBAAA;gBAAA;gBACA;gBACA,2EACA;kBACAC;gBAAA,GACA;;gBAEA;gBACAhC;gBACAA;kBACAC;kBACAE;gBACA;gBAAA;gBAAA;cAAA;gBAAA,MAEA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAE;gBAAA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAIAA;gBACAL;gBACAA;kBACAC;kBACAE;gBACA;cAAA;gBAAA;gBAEA;gBACA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;EAAA;AAEA;AAAA,2B;;;;;;;;;;;;;ACpWA;AAAA;AAAA;AAAA;AAA+oC,CAAgB,gmCAAG,EAAC,C;;;;;;;;;;;ACAnqC;AACA,OAAO,KAAU,EAAE,kBAKd;;;;;;;;;;;;;;;;ACNL;AAGA;AACA;AAHA;AACAqB,EAAE,CAACS,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,gBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAoH;AACpH;AAC2D;AACL;AACc;;;AAGpE;AACmK;AACnK,gBAAgB,6KAAU;AAC1B,EAAE,6EAAM;AACR,EAAE,kFAAM;AACR,EAAE,2FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,sFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,wOAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "pages/mine/profile.js", "sourcesContent": ["import mod from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./profile.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./profile.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"profile\">\n    <!-- 遮罩层和裁剪组件 -->\n    <view class=\"cropper-mask\" v-if=\"showCropper\"></view>\n    <qf-image-cropper v-if=\"showCropper\" :src=\"tempImagePath\" :width=\"300\" :height=\"300\" :radius=\"150\" @crop=\"onCropComplete\" :zIndex=\"1000\"></qf-image-cropper>\n\n    <!-- 模态框组件 -->\n    <view class=\"modal-mask\" v-if=\"showModal\"></view>\n    <view class=\"modal-container\" v-if=\"showModal\">\n      <view class=\"modal-header\">\n        <text>修改{{ modalTitle }}</text>\n      </view>\n      <view class=\"modal-body\">\n        <input class=\"modal-input\" v-model=\"modalInputValue\" :placeholder=\"'请输入' + modalTitle\" />\n      </view>\n      <view class=\"modal-footer\">\n        <button class=\"modal-btn cancel\" @click=\"cancelModal\">取消</button>\n        <button class=\"modal-btn confirm\" @click=\"confirmModal\">确定</button>\n      </view>\n    </view>\n\n    <!-- 上传遮罩层 -->\n    <view class=\"modal-mask\" v-if=\"showUploadMask\"></view>\n\n    <!-- 个人信息列表 -->\n    <view class=\"info-list\">\n      <!-- 头像项 -->\n      <view class=\"info-item\" @click=\"chooseAvatarAndUpload\">\n        <text class=\"item-label\">头像</text>\n        <view class=\"item-content avatar-wrapper\">\n          <image class=\"avatar-small\" :src=\"avatarSource\"></image>\n          <image class=\"arrow-icon\" src=\"/static/icons/arrow-right.png\"></image>\n        </view>\n      </view>\n\n      <!-- 用户名项 -->\n      <view class=\"info-item\" @click=\"openModal('用户名', username)\">\n        <text class=\"item-label\">用户名</text>\n        <view class=\"item-content\">\n          <text class=\"item-value\">{{ username }}</text>\n          <image class=\"arrow-icon\" src=\"/static/icons/arrow-right.png\"></image>\n        </view>\n      </view>\n\n      <!-- 邮箱项 -->\n      <view class=\"info-item\" @click=\"openModal('邮箱', email)\">\n        <text class=\"item-label\">邮箱</text>\n        <view class=\"item-content\">\n          <text class=\"item-value\">{{ maskedEmail }}</text>\n          <image class=\"arrow-icon\" src=\"/static/icons/arrow-right.png\"></image>\n        </view>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script>\nimport { mapState, mapActions } from 'vuex'\nimport { getAvatarUploadUrl, getUserAvatar } from '@/api/user'\nimport QfImageCropper from '@/components/qf-image-cropper/qf-image-cropper'\n\nexport default {\n  components: {\n    QfImageCropper\n  },\n  data() {\n    return {\n      userId: '',\n      username: '',\n      email: '',\n      showCropper: false, // 控制裁剪组件的显示\n      tempImagePath: '', // 临时图片路径\n      croppedImagePath: '', // 裁剪后的图片路径\n      showModal: false, // 控制模态框显示\n      modalTitle: '', // 模态框标题\n      modalInputValue: '', // 模态框输入值\n      currentField: '', // 当前编辑的字段\n      showUploadMask: false // 控制上传遮罩层显示\n    }\n  },\n  computed: {\n    ...mapState({\n      userInfo: state => state.user.userInfo,\n      isLogin: state => state.user.isLogin\n    }),\n    maskedEmail() {\n      if (!this.email) return '';\n\n      // 找到@符号的位置\n      const atIndex = this.email.indexOf('@');\n      if (atIndex <= 1) return this.email; // 邮箱格式异常或过短\n\n      // 取邮箱的前两个字符和@后面的部分，中间用****替代\n      const prefix = this.email.substring(0, 2);\n      const suffix = this.email.substring(atIndex);\n\n      return prefix + '****' + suffix;\n    },\n    // 简化的头像源：直接使用远程URL或默认头像\n    avatarSource() {\n      return this.userInfo.avatar || '/static/images/default-avatar.png';\n    }\n  },\n  onLoad() {\n    // 初始化数据\n    this.initData()\n  },\n  methods: {\n    ...mapActions({\n      updateUserInfo: 'user/updateUserInfo'\n    }),\n    initData() {\n      // 从 store 中获取数据\n      this.userId = this.userInfo.id || ''\n      this.username = this.userInfo.username || ''\n      this.email = this.userInfo.email || ''\n    },\n    // 打开模态框\n    openModal(title, value) {\n      this.modalTitle = title\n      this.modalInputValue = value\n      this.currentField = title === '用户名' ? 'username' : 'email'\n      this.showModal = true\n    },\n    // 取消模态框\n    cancelModal() {\n      this.showModal = false\n    },\n    // 确认模态框\n    confirmModal() {\n      // 根据当前编辑的字段更新数据\n      if (this.currentField === 'username') {\n        this.username = this.modalInputValue\n      } else if (this.currentField === 'email') {\n        this.email = this.modalInputValue\n      }\n\n      // 保存单项修改\n      this.saveField(this.currentField, this.modalInputValue)\n\n      // 关闭模态框\n      this.showModal = false\n    },\n    // 保存单个字段\n    saveField(field, value) {\n      // 只传递需要更新的字段，不传递整个用户信息对象\n      const userData = {}\n\n      // 更新对应字段\n      if (field === 'username') {\n        userData.username = value  // 修正：应该是username而不是nickname\n      } else if (field === 'email') {\n        userData.email = value\n      }\n\n      // 调用 store action 更新用户信息\n      uni.showLoading({ title: '保存中...' })\n\n      this.updateUserInfo(userData)\n        .then(() => {\n          uni.hideLoading()\n          uni.showToast({\n            title: '保存成功',\n            icon: 'success'\n          })\n        })\n        .catch(err => {\n          uni.hideLoading()\n          uni.showToast({\n            title: '保存失败',\n            icon: 'none'\n          })\n          console.error('保存用户信息失败', err)\n        })\n    },\n    async chooseAvatarAndUpload() {\n      try {\n        // 1. 选择图片\n        const { tempFiles } = await new Promise((resolve, reject) => {\n          uni.chooseImage({\n            count: 1, // 最多选择1张图片\n            sizeType: ['compressed'], // 压缩图片\n            sourceType: ['album', 'camera'], // 可以从相册或相机选择\n            success: resolve,\n            fail: (error) => {\n              // 检查是否为用户取消操作\n              if (error && (error.errMsg === 'chooseImage:fail cancel' || error.errMsg.includes('cancel'))) {\n                // 用户取消，不做任何处理，静默返回\n                console.log('用户取消了选择图片');\n                // 不触发reject，直接返回\n                return;\n              }\n              // 其他错误正常reject\n              reject(error);\n            }\n          })\n        })\n\n        // 如果没有选择文件，直接返回\n        if (!tempFiles || tempFiles.length === 0) {\n          return;\n        }\n\n        const tempFilePath = tempFiles[0].path\n\n        // 2. 检查图片大小\n        const maxSize = 5 * 1024 * 1024 // 5MB\n        if (tempFiles[0].size > maxSize) {\n          uni.showToast({\n            title: '图片大小不能超过5MB',\n            icon: 'none'\n          })\n          return\n        }\n\n        // 3. 显示裁剪组件\n        this.tempImagePath = tempFilePath\n        this.showCropper = true\n\n      } catch (error) {\n        console.error('选择图片失败', error)\n        // 只在非用户取消的情况下显示错误提示\n        uni.showToast({\n          title: '选择图片失败',\n          icon: 'none'\n        })\n      }\n    },\n    // 裁剪完成回调\n    onCropComplete(e) {\n      // 获取裁剪后的图片临时路径\n      this.croppedImagePath = e.tempFilePath\n      // 隐藏裁剪组件\n      this.showCropper = false\n      // 上传裁剪后的图片\n      this.uploadCroppedImage()\n    },\n    // 上传裁剪后的图片\n    async uploadCroppedImage() {\n      try {\n        if (!this.croppedImagePath) {\n          uni.showToast({\n            title: '裁剪图片失败',\n            icon: 'none'\n          })\n          return\n        }\n\n        // 1. 显示上传中的提示和遮罩\n        this.showUploadMask = true\n        uni.showLoading({\n          title: '上传中...'\n        })\n\n        // 2. 获取图片类型\n        const fileType = this.croppedImagePath.substring(this.croppedImagePath.lastIndexOf('.') + 1)\n        let contentType\n        switch (fileType.toLowerCase()) {\n          case 'jpg':\n          case 'jpeg':\n            contentType = 'image/jpeg'\n            break\n          case 'png':\n            contentType = 'image/png'\n            break\n          case 'gif':\n            contentType = 'image/gif'\n            break\n          default:\n            contentType = 'image/jpeg' // 裁剪后的图片格式可能不明确，默认使用jpeg\n        }\n\n        try {\n          // 3. 获取预签名上传URL (直接传递contentType作为数据而非查询参数)\n          const uploadRes = await getAvatarUploadUrl(contentType)\n          const { uploadUrl, fileName } = uploadRes.data\n\n          // 4. 使用微信小程序的方式构建二进制上传请求\n          const uploadResult = await new Promise((resolve, reject) => {\n            // 读取文件为ArrayBuffer\n            const fs = uni.getFileSystemManager()\n            fs.readFile({\n              filePath: this.croppedImagePath,\n              position: 0,\n              success: function(readRes) {\n                // 获取ArrayBuffer数据\n                const buffer = readRes.data\n\n                // 使用微信原生API直接发送请求\n                wx.request({\n                  url: uploadUrl,\n                  method: 'PUT',\n                  data: buffer, // 直接传递二进制数据\n                  header: {\n                    'Content-Type': contentType // 确保与签名请求中的Content-Type匹配\n                  },\n                  responseType: 'text',\n                  success: function(res) {\n                    if (res.statusCode >= 200 && res.statusCode < 300) {\n                      resolve(res)\n                    } else {\n                      console.error('上传失败状态码:', res.statusCode)\n                      console.error('上传失败响应:', res.data)\n                      reject(new Error(`上传失败: 状态码 ${res.statusCode}`))\n                    }\n                  },\n                  fail: function(err) {\n                    console.error('上传请求失败:', err)\n                    reject(new Error('上传请求失败: ' + JSON.stringify(err)))\n                  }\n                })\n              },\n              fail: function(err) {\n                console.error('读取文件失败:', err)\n                reject(new Error('读取文件失败: ' + JSON.stringify(err)))\n              }\n            })\n          })\n\n          console.log('上传结果:', uploadResult)\n\n          // 5. 获取新的头像URL\n          const avatarRes = await getUserAvatar()\n          if (avatarRes.data && avatarRes.data.avatarUrl) {\n            // 6. 更新用户头像信息\n            this.$store.commit('user/SET_USER_INFO', {\n              ...this.userInfo,\n              avatar: avatarRes.data.avatarUrl\n            })\n\n            // 7. 显示成功提示\n            uni.hideLoading()\n            uni.showToast({\n              title: '头像上传成功',\n              icon: 'success'\n            })\n          } else {\n            throw new Error('获取头像URL失败')\n          }\n        } catch (uploadError) {\n          console.error('上传过程出错:', uploadError)\n          throw uploadError\n        }\n      } catch (error) {\n        console.error('头像上传失败', error)\n        uni.hideLoading()\n        uni.showToast({\n          title: '头像上传失败',\n          icon: 'none'\n        })\n      } finally {\n        // 隐藏遮罩\n        this.showUploadMask = false\n      }\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\">\n.profile {\n  min-height: 100vh;\n  background-color: #f8f8f8;\n}\n\n/* 裁剪组件相关样式 */\n.cropper-mask {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-color: rgba(0, 0, 0, 0.8);\n  z-index: 999;\n}\n\n/* 模态框样式 */\n.modal-mask {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-color: rgba(0, 0, 0, 0.5);\n  z-index: 998;\n}\n\n.modal-container {\n  position: fixed;\n  left: 50%;\n  top: 50%;\n  transform: translate(-50%, -50%);\n  width: 80%;\n  background-color: #fff;\n  border-radius: 12rpx;\n  z-index: 999;\n  overflow: hidden;\n}\n\n.modal-header {\n  padding: 30rpx;\n  text-align: center;\n  font-size: 32rpx;\n  font-weight: bold;\n  border-bottom: 1rpx solid #eee;\n}\n\n.modal-body {\n  padding: 30rpx;\n}\n\n.modal-input {\n  width: 100%;\n  height: 80rpx;\n  background-color: #f8f8f8;\n  border-radius: 8rpx;\n  padding: 0 20rpx;\n  font-size: 28rpx;\n}\n\n.modal-footer {\n  display: flex;\n  border-top: 1rpx solid #eee;\n}\n\n.modal-btn {\n  flex: 1;\n  height: 90rpx;\n  line-height: 90rpx;\n  text-align: center;\n  font-size: 32rpx;\n  border-radius: 0;\n\n  &.cancel {\n    color: #999;\n    border-right: 1rpx solid #eee;\n  }\n\n  &.confirm {\n    color: #4CAF50;\n  }\n}\n\n/* 个人信息列表样式 */\n.info-list {\n  margin-top: 20rpx;\n  background-color: #ffffff;\n}\n\n.info-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 30rpx;\n  border-bottom: 1rpx solid #f5f5f5;\n\n  .item-label {\n    font-size: 30rpx;\n    color: #333;\n  }\n\n  .item-content {\n    display: flex;\n    align-items: center;\n\n    .item-value {\n      font-size: 28rpx;\n      color: #666;\n      margin-right: 20rpx;\n\n      &.disabled {\n        color: #999;\n      }\n    }\n\n    .arrow-icon {\n      width: 32rpx;\n      height: 32rpx;\n    }\n  }\n\n  .avatar-wrapper {\n    display: flex;\n    align-items: center;\n  }\n\n  .avatar-small {\n    width: 80rpx;\n    height: 80rpx;\n    border-radius: 40rpx;\n    margin-right: 20rpx;\n  }\n}\n</style>", "import mod from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./profile.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./profile.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1751164239603\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  ", "import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/mine/profile.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./profile.vue?vue&type=template&id=ac738334&\"\nvar renderjs\nimport script from \"./profile.vue?vue&type=script&lang=js&\"\nexport * from \"./profile.vue?vue&type=script&lang=js&\"\nimport style0 from \"./profile.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/mine/profile.vue\"\nexport default component.exports", "export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./profile.vue?vue&type=template&id=ac738334&\"", "var components\ntry {\n  components = {\n    qfImageCropper: function () {\n      return import(\n        /* webpackChunkName: \"components/qf-image-cropper/qf-image-cropper\" */ \"@/components/qf-image-cropper/qf-image-cropper.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }"], "sourceRoot": ""}