package v1

import "time"

// DashboardStatsResponse 仪表盘统计数据响应
type DashboardStatsResponse struct {
	// 总用户数
	TotalUsers int64 `json:"totalUsers"`

	// 今日饮食记录数
	TodayRecords int `json:"todayRecords"`

	// 营养达标率（百分比）
	NutritionComplianceRate float64 `json:"nutritionComplianceRate"`

	// 推荐准确率（百分比）
	RecommendationAccuracy int `json:"recommendationAccuracy"`

	// 统计日期
	StatisticsDate string `json:"statisticsDate"` // 格式：yyyy-MM-dd

	// 活跃用户数（可选）
	ActiveUsers *int64 `json:"activeUsers,omitempty"`

	// 本周新增用户数（可选）
	WeeklyNewUsers *int64 `json:"weeklyNewUsers,omitempty"`

	// 本月新增用户数（可选）
	MonthlyNewUsers *int64 `json:"monthlyNewUsers,omitempty"`
}

// PopularFoodItem 热门食物项
type PopularFoodItem struct {
	// 食物名称
	Name string `json:"name"`

	// 使用次数
	Count int64 `json:"count"`
}

// PopularFoodResponse 热门食物响应（完整版本，如果需要更多字段）
type PopularFoodResponse struct {
	// 食物ID
	FoodID *int64 `json:"foodId,omitempty"`

	// 食物名称
	FoodName string `json:"foodName"`

	// 食物图片URL
	ImageURL *string `json:"imageUrl,omitempty"`

	// 使用次数
	UsageCount int64 `json:"usageCount"`

	// 使用用户数
	UserCount *int64 `json:"userCount,omitempty"`

	// 排名
	Rank *int `json:"rank,omitempty"`

	// 热量（每100g）
	Calorie *int `json:"calorie,omitempty"`

	// 分类名称
	CategoryName *string `json:"categoryName,omitempty"`
}

// DashboardStatsRequest 仪表盘统计数据请求
type DashboardStatsRequest struct {
	// 查询日期，可选，默认为今天
	Date *string `json:"date,omitempty"` // 格式：yyyy-MM-dd
}

// NutritionTrendRequest 营养趋势数据请求
type NutritionTrendRequest struct {
	// 时间周期：week(周)、month(月)、quarter(季度)
	Period string `json:"period" binding:"required,oneof=week month quarter"`
}

// PopularFoodsRequest 热门食物请求
type PopularFoodsRequest struct {
	// 时间周期：week(周)、month(月)、quarter(季度)
	Period string `json:"period" binding:"oneof=week month quarter"`

	// 返回数量限制
	Limit int `json:"limit" binding:"min=1,max=50"`
}

// DietRecordQueryRequest 饮食记录查询请求（用于最新记录）
type DietRecordQueryRequest struct {
	// 用户ID（可选，管理员查询时使用）
	UserID *int64 `form:"userId"`

	// 开始日期（可选）
	StartDate *string `form:"startDate"` // 格式：yyyy-MM-dd

	// 结束日期（可选）
	EndDate *string `form:"endDate"` // 格式：yyyy-MM-dd

	// 餐次类型（可选）
	MealType *string `form:"mealType"`

	// 页码
	Page int `form:"page" binding:"omitempty,min=1"`

	// 每页大小
	Size int `form:"size" binding:"omitempty,min=1,max=100"`
}

// DashboardStatsCommand 仪表盘统计数据命令（内部使用）
type DashboardStatsCommand struct {
	Date time.Time
}

// NutritionTrendCommand 营养趋势命令（内部使用）
type NutritionTrendCommand struct {
	Period string
}

// PopularFoodsCommand 热门食物命令（内部使用）
type PopularFoodsCommand struct {
	Period string
	Limit  int
}
