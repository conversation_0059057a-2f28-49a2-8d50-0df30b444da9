server:
  port: 8086

spring:
  application:
    name: user-service
  datasource:
    url: **********************************************************************
    username: root
    password: 123456
#    driver-class-name: com.mysql.cj.jdbc.Driver
  redis:
    host: localhost
    port: 6379

dubbo:
  application:
    name: user-service
  protocol:
    name: dubbo
    port: -1
  registry:
    address: zookeeper://localhost:2181
  provider:
    version: 3.1.0    # 服务提供者使用的版本
  consumer:
    version: 3.1.0    # 服务消费者默认使用的版本

jwt:
  expiration: 86400000
  secret: your-secret-key-should-be-at-least-256-bits-long
logging:
  level:
    com.example.*: info
    com.example.common.cache: warn
    com.example.common.event.cache: warn
