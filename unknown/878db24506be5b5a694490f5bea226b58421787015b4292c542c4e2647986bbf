@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 全局样式 */
page {
  font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', Helvetica, Segoe UI, Arial, Roboto, 'PingFang SC', 'miui', 'Hiragino Sans GB', 'Microsoft Yahei', sans-serif;
  font-size: 28rpx;
  line-height: 1.5;
  color: #333;
  background-color: #f8f8f8;
  /* 安全区域自适应 */
  padding-bottom: constant(safe-area-inset-bottom);
  /* iOS 11.0 */
  padding-bottom: env(safe-area-inset-bottom);
  /* iOS 11.2+ */
}
/* 胶囊按钮区域样式 */
.status-bar {
  height: 25px;
  width: 100%;
}
.capsule-area {
  height: 44px;
  width: 100%;
  position: relative;
}
/* 底部安全区域适配 */
.safe-bottom {
  padding-bottom: constant(safe-area-inset-bottom);
  /* iOS 11.0 */
  padding-bottom: env(safe-area-inset-bottom);
  /* iOS 11.2+ */
}
/* 顶部安全区域适配 */
.safe-top {
  padding-top: constant(safe-area-inset-top);
  /* iOS 11.0 */
  padding-top: env(safe-area-inset-top);
  /* iOS 11.2+ */
}
/* 清除默认样式 */
view, text, navigator, input, textarea, button {
  box-sizing: border-box;
}
/* 通用样式 */
.container {
  padding: 20rpx;
}
.flex-row {
  display: flex;
  flex-direction: row;
}
.flex-column {
  display: flex;
  flex-direction: column;
}
.flex-center {
  display: flex;
  justify-content: center;
  align-items: center;
}
.flex-between {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.flex-around {
  display: flex;
  justify-content: space-around;
  align-items: center;
}
.flex-1 {
  flex: 1;
}
/* 颜色 */
.primary-color {
  color: #4CAF50;
}
.primary-bg {
  background-color: #4CAF50;
}
.text-white {
  color: #ffffff;
}
.text-gray {
  color: #999999;
}
/* 边距 */
.mt-10 {
  margin-top: 10rpx;
}
.mt-20 {
  margin-top: 20rpx;
}
.mb-10 {
  margin-bottom: 10rpx;
}
.mb-20 {
  margin-bottom: 20rpx;
}
.ml-10 {
  margin-left: 10rpx;
}
.mr-10 {
  margin-right: 10rpx;
}
.p-20 {
  padding: 20rpx;
}
/* 卡片样式 */
.card {
  background-color: #ffffff;
  border-radius: 10rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}
/* 按钮样式 */
.btn {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20rpx 30rpx;
  border-radius: 10rpx;
  font-size: 30rpx;
}
.btn-primary {
  background-color: #4CAF50;
  color: #ffffff;
}
.btn-outline {
  border: 1rpx solid #4CAF50;
  color: #4CAF50;
}

