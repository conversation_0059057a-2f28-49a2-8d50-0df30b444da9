server:
  port: 8085

spring:
  application:
    name: auth-service
  redis:
    host: localhost
    port: 6379

dubbo:
  application:
    name: auth-service
  protocol:
    name: dubbo
    port: -1
  registry:
    address: zookeeper://localhost:2181
  provider:
    version: 3.1.0    # 服务提供者使用的版本
  consumer:
    version: 3.1.0    # 服务消费者默认使用的版本

jwt:
  expiration: 86400000
  secret: your-secret-key-should-be-at-least-256-bits-long

wechat:
  appid: ${WECHAT_APPID:84ff2a7512b9e90a3eb836c}
  secret: ${WECHAT_SECRET:84ff2a7512b9e90a3eb836c}
  login-url: https://api.weixin.qq.com/sns/jscode2session

# 添加日志配置
logging:
  level:
    com.example.*: info
    com.example.common.cache: warn
    com.example.common.event.cache: warn

