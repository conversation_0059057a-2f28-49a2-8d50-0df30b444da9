package entities

import "time"

// Food 食物实体 - 包含业务逻辑和数据库映射
type Food struct {
	ID         int      `gorm:"column:id;primaryKey;autoIncrement;type:int" json:"id"`
	FoodName   *string  `gorm:"column:food_name;type:varchar(255)" json:"foodName"`
	Measure    *string  `gorm:"column:measure;type:varchar(100)" json:"measure"`
	Grams      *float64 `gorm:"column:grams;type:double;comment:克数。约定：0.01 代表微量。" json:"grams"`
	Calories   *float64 `gorm:"column:calories;type:double;comment:卡路里。约定：0.01 代表微量。" json:"calories"`
	Protein    *float64 `gorm:"column:protein;type:double;comment:蛋白质含量(克)。约定：0.01 代表微量。" json:"protein"`
	Fat        *float64 `gorm:"column:fat;type:double;comment:总脂肪含量(克)。约定：0.01 代表微量。" json:"fat"`
	SatFat     *float64 `gorm:"column:sat_fat;type:double;comment:饱和脂肪含量(克)。约定：0.01 代表微量。" json:"satFat"`
	Fiber      *string  `gorm:"column:fiber;type:varchar(20)" json:"fiber"`
	Carbs      *float64 `gorm:"column:carbs;type:double;comment:碳水化合物含量(克)。约定：0.01 代表微量。" json:"carbs"`
	ImageURL   *string  `gorm:"column:image_url;type:varchar(255);comment:食物图片URL" json:"imageUrl"`
	CategoryID *int     `gorm:"column:category_id;type:int;comment:分类ID" json:"categoryId"`
	
	// 关联字段 - 使用数据库中已存在的外键约束
	Category *FoodCategory `gorm:"foreignKey:CategoryID;references:ID" json:"category,omitempty"`
}

// TableName 指定表名
func (Food) TableName() string {
	return "food"
}

// NewFood 创建新食物实体
func NewFood(foodName, measure string) *Food {
	return &Food{
		FoodName: &foodName,
		Measure:  &measure,
	}
}

// SetNutritionInfo 设置营养信息
func (f *Food) SetNutritionInfo(grams, calories, protein, fat, satFat, carbs *float64, fiber *string) {
	f.Grams = grams
	f.Calories = calories
	f.Protein = protein
	f.Fat = fat
	f.SatFat = satFat
	f.Carbs = carbs
	f.Fiber = fiber
}

// SetImageURL 设置食物图片URL
func (f *Food) SetImageURL(url string) {
	f.ImageURL = &url
}

// SetCategory 设置食物分类
func (f *Food) SetCategory(categoryID int) {
	f.CategoryID = &categoryID
}

// HasImage 检查是否有图片
func (f *Food) HasImage() bool {
	return f.ImageURL != nil && *f.ImageURL != ""
}

// GetCaloriesPer100g 获取每100克的卡路里
func (f *Food) GetCaloriesPer100g() float64 {
	if f.Calories == nil || f.Grams == nil || *f.Grams == 0 {
		return 0
	}
	return (*f.Calories / *f.Grams) * 100
}

// GetProteinPer100g 获取每100克的蛋白质含量
func (f *Food) GetProteinPer100g() float64 {
	if f.Protein == nil || f.Grams == nil || *f.Grams == 0 {
		return 0
	}
	return (*f.Protein / *f.Grams) * 100
}

// GetFatPer100g 获取每100克的脂肪含量
func (f *Food) GetFatPer100g() float64 {
	if f.Fat == nil || f.Grams == nil || *f.Grams == 0 {
		return 0
	}
	return (*f.Fat / *f.Grams) * 100
}

// GetCarbsPer100g 获取每100克的碳水化合物含量
func (f *Food) GetCarbsPer100g() float64 {
	if f.Carbs == nil || f.Grams == nil || *f.Grams == 0 {
		return 0
	}
	return (*f.Carbs / *f.Grams) * 100
}

// FoodCategory 食物分类实体 - 包含业务逻辑和数据库映射
type FoodCategory struct {
	ID          int        `gorm:"column:id;primaryKey;autoIncrement;type:int" json:"id"`
	Name        string     `gorm:"column:name;type:varchar(100);not null;uniqueIndex:uk_category_name;comment:分类名称" json:"name"`
	Description *string    `gorm:"column:description;type:varchar(255);comment:分类描述" json:"description"`
	Color       *string    `gorm:"column:color;type:varchar(20);comment:分类颜色" json:"color"`
	SortOrder   int        `gorm:"column:sort_order;type:int;default:0;comment:排序顺序" json:"sortOrder"`
	CreatedAt   time.Time  `gorm:"column:created_at;type:datetime;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"createdAt"`
	UpdatedAt   time.Time  `gorm:"column:updated_at;type:datetime;not null;default:CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP;comment:更新时间" json:"updatedAt"`
}

// TableName 指定表名
func (FoodCategory) TableName() string {
	return "food_category"
}

// NewFoodCategory 创建新食物分类实体
func NewFoodCategory(name string) *FoodCategory {
	now := time.Now()
	return &FoodCategory{
		Name:      name,
		SortOrder: 0,
		CreatedAt: now,
		UpdatedAt: now,
	}
}

// SetDescription 设置分类描述
func (fc *FoodCategory) SetDescription(description string) {
	fc.Description = &description
}

// SetColor 设置分类颜色
func (fc *FoodCategory) SetColor(color string) {
	fc.Color = &color
}

// SetSortOrder 设置排序顺序
func (fc *FoodCategory) SetSortOrder(order int) {
	fc.SortOrder = order
	fc.UpdatedAt = time.Now()
}

// IsValidSortOrder 验证排序顺序是否有效
func (fc *FoodCategory) IsValidSortOrder() bool {
	return fc.SortOrder >= 0
}

// UpdateInfo 更新分类信息
func (fc *FoodCategory) UpdateInfo(name string, description *string, color *string, sortOrder int) {
	fc.Name = name
	fc.Description = description
	fc.Color = color
	fc.SortOrder = sortOrder
	fc.UpdatedAt = time.Now()
}

// HasDescription 检查是否有描述
func (fc *FoodCategory) HasDescription() bool {
	return fc.Description != nil && *fc.Description != ""
}

// HasColor 检查是否有颜色
func (fc *FoodCategory) HasColor() bool {
	return fc.Color != nil && *fc.Color != ""
}
