
.container.data-v-e4e769ee {
  width: 30px;
  height: 30px;
  position: relative;
}
.container.loading1.data-v-e4e769ee {
  -webkit-transform: rotate(45deg);
          transform: rotate(45deg);
}
.container .shape.data-v-e4e769ee {
  position: absolute;
  width: 10px;
  height: 10px;
  border-radius: 1px;
}
.container .shape.shape1.data-v-e4e769ee {
  left: 0;
  background-color: #1890FF;
}
.container .shape.shape2.data-v-e4e769ee {
  right: 0;
  background-color: #91CB74;
}
.container .shape.shape3.data-v-e4e769ee {
  bottom: 0;
  background-color: #FAC858;
}
.container .shape.shape4.data-v-e4e769ee {
  bottom: 0;
  right: 0;
  background-color: #EE6666;
}
.loading1 .shape1.data-v-e4e769ee {
  -webkit-animation: animation1shape1-data-v-e4e769ee 0.5s ease 0s infinite alternate;
          animation: animation1shape1-data-v-e4e769ee 0.5s ease 0s infinite alternate;
}
@-webkit-keyframes animation1shape1-data-v-e4e769ee {
from {
    -webkit-transform: translate(0, 0);
            transform: translate(0, 0);
}
to {
    -webkit-transform: translate(16px, 16px);
            transform: translate(16px, 16px);
}
}
@keyframes animation1shape1-data-v-e4e769ee {
from {
    -webkit-transform: translate(0, 0);
            transform: translate(0, 0);
}
to {
    -webkit-transform: translate(16px, 16px);
            transform: translate(16px, 16px);
}
}
.loading1 .shape2.data-v-e4e769ee {
  -webkit-animation: animation1shape2-data-v-e4e769ee 0.5s ease 0s infinite alternate;
          animation: animation1shape2-data-v-e4e769ee 0.5s ease 0s infinite alternate;
}
@-webkit-keyframes animation1shape2-data-v-e4e769ee {
from {
    -webkit-transform: translate(0, 0);
            transform: translate(0, 0);
}
to {
    -webkit-transform: translate(-16px, 16px);
            transform: translate(-16px, 16px);
}
}
@keyframes animation1shape2-data-v-e4e769ee {
from {
    -webkit-transform: translate(0, 0);
            transform: translate(0, 0);
}
to {
    -webkit-transform: translate(-16px, 16px);
            transform: translate(-16px, 16px);
}
}
.loading1 .shape3.data-v-e4e769ee {
  -webkit-animation: animation1shape3-data-v-e4e769ee 0.5s ease 0s infinite alternate;
          animation: animation1shape3-data-v-e4e769ee 0.5s ease 0s infinite alternate;
}
@-webkit-keyframes animation1shape3-data-v-e4e769ee {
from {
    -webkit-transform: translate(0, 0);
            transform: translate(0, 0);
}
to {
    -webkit-transform: translate(16px, -16px);
            transform: translate(16px, -16px);
}
}
@keyframes animation1shape3-data-v-e4e769ee {
from {
    -webkit-transform: translate(0, 0);
            transform: translate(0, 0);
}
to {
    -webkit-transform: translate(16px, -16px);
            transform: translate(16px, -16px);
}
}
.loading1 .shape4.data-v-e4e769ee {
  -webkit-animation: animation1shape4-data-v-e4e769ee 0.5s ease 0s infinite alternate;
          animation: animation1shape4-data-v-e4e769ee 0.5s ease 0s infinite alternate;
}
@-webkit-keyframes animation1shape4-data-v-e4e769ee {
from {
    -webkit-transform: translate(0, 0);
            transform: translate(0, 0);
}
to {
    -webkit-transform: translate(-16px, -16px);
            transform: translate(-16px, -16px);
}
}
@keyframes animation1shape4-data-v-e4e769ee {
from {
    -webkit-transform: translate(0, 0);
            transform: translate(0, 0);
}
to {
    -webkit-transform: translate(-16px, -16px);
            transform: translate(-16px, -16px);
}
}



