package entities

import "time"

// NutritionAdvice 营养建议实体 - 包含业务逻辑和数据库映射
type NutritionAdvice struct {
	ID            int64     `gorm:"column:id;primaryKey;autoIncrement;type:bigint" json:"id"`
	Type          string    `gorm:"column:type;type:varchar(20);not null;comment:建议类型: warning, info, danger, success" json:"type"`
	Title         string    `gorm:"column:title;type:varchar(100);not null;comment:建议标题" json:"title"`
	Description   string    `gorm:"column:description;type:varchar(500);not null;comment:建议详情" json:"description"`
	ConditionType string    `gorm:"column:condition_type;type:varchar(20);not null;comment:条件类型: protein, carbs, fat, calorie" json:"conditionType"`
	MinPercentage *int      `gorm:"column:min_percentage;type:int;comment:最小百分比阈值" json:"minPercentage"`
	MaxPercentage *int      `gorm:"column:max_percentage;type:int;comment:最大百分比阈值" json:"maxPercentage"`
	IsDefault     bool      `gorm:"column:is_default;type:tinyint(1);not null;default:0;comment:是否为默认建议" json:"isDefault"`
	Priority      int       `gorm:"column:priority;type:int;not null;default:0;comment:优先级，数字越大优先级越高" json:"priority"`
	Status        int8      `gorm:"column:status;type:tinyint(1);not null;default:1;comment:状态：1-启用，0-禁用" json:"status"`
	CreatedAt     time.Time `gorm:"column:created_at;type:datetime;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"createdAt"`
	UpdatedAt     time.Time `gorm:"column:updated_at;type:datetime;not null;default:CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP;comment:更新时间" json:"updatedAt"`
}

// TableName 指定表名
func (NutritionAdvice) TableName() string {
	return "nutrition_advice"
}

// NewNutritionAdvice 创建新营养建议实体
func NewNutritionAdvice(adviceType, title, description, conditionType string, priority int) *NutritionAdvice {
	now := time.Now()
	return &NutritionAdvice{
		Type:          adviceType,
		Title:         title,
		Description:   description,
		ConditionType: conditionType,
		IsDefault:     false,
		Priority:      priority,
		Status:        1, // 默认启用
		CreatedAt:     now,
		UpdatedAt:     now,
	}
}

// SetPercentageRange 设置百分比范围
func (na *NutritionAdvice) SetPercentageRange(minPercentage, maxPercentage *int) {
	na.MinPercentage = minPercentage
	na.MaxPercentage = maxPercentage
	na.UpdatedAt = time.Now()
}

// SetAsDefault 设置为默认建议
func (na *NutritionAdvice) SetAsDefault(isDefault bool) {
	na.IsDefault = isDefault
	na.UpdatedAt = time.Now()
}

// SetStatus 设置状态
func (na *NutritionAdvice) SetStatus(status int8) {
	na.Status = status
	na.UpdatedAt = time.Now()
}



// UpdateContent 更新建议内容
func (na *NutritionAdvice) UpdateContent(title, description string) {
	na.Title = title
	na.Description = description
	na.UpdatedAt = time.Now()
}

// UpdatePriority 更新优先级
func (na *NutritionAdvice) UpdatePriority(priority int) {
	na.Priority = priority
	na.UpdatedAt = time.Now()
}


