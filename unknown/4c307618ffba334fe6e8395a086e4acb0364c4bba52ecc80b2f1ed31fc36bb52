<view class="nutrition-circle"><view class="circle-container"><canvas class="circle-canvas" style="{{'width:'+(size+'rpx')+';'+('height:'+(size+'rpx')+';')}}" type="2d" id="{{canvasId}}" data-event-opts="{{[['touchend',[['redraw',['$event']]]]]}}" bindtouchend="__e"></canvas><view class="content"><view class="percentage"><text class="percentage-value">{{formattedPercentage}}</text><text class="percentage-symbol">%</text></view><text class="label">{{label}}</text><view class="value-info"><text>{{$root.f0+unitText}}</text></view></view></view><view class="target-info"><text>{{"目标: "+$root.f1+unitText}}</text></view></view>