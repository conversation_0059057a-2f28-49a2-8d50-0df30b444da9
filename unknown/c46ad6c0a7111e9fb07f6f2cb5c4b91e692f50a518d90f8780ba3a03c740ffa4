{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/HBuilderProjects/springboot-dubbo-front-wechat/pages/diet-record/index.vue?57ac", "webpack:///D:/HBuilderProjects/springboot-dubbo-front-wechat/pages/diet-record/index.vue?834c", "webpack:///D:/HBuilderProjects/springboot-dubbo-front-wechat/pages/diet-record/index.vue?2e43", "webpack:///D:/HBuilderProjects/springboot-dubbo-front-wechat/pages/diet-record/index.vue?0df6", "uni-app:///pages/diet-record/index.vue", "webpack:///D:/HBuilderProjects/springboot-dubbo-front-wechat/pages/diet-record/index.vue?7dfe", "webpack:///D:/HBuilderProjects/springboot-dubbo-front-wechat/pages/diet-record/index.vue?ab2c"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "selectedDate", "datePickerValue", "maxDate", "mealTypes", "breakfast", "lunch", "dinner", "snacks", "cachedDietRecords", "lastLoadTime", "contentHeight", "loading", "selectedMealType", "computed", "dietRecords", "isDataReady", "dateTitle", "today", "selectedDateObj", "formattedDate", "mealRecords", "watch", "handler", "deep", "onLoad", "onReady", "onShow", "onPullDownRefresh", "console", "uni", "title", "icon", "duration", "methods", "fetchDietRecords", "navigateTo", "url", "getMealTypeName", "getMealTotalCalorie", "getMealTotalNutrition", "meal", "total", "calcContentHeight", "query", "that", "onDateChange", "changeDate", "newDate", "loadDietRecords", "date", "reset", "then", "catch", "filterByMealType", "formatNutrition", "formatFoodAmount"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkH;AAClH;AACyD;AACL;AACc;;;AAGlE;AACmK;AACnK,gBAAgB,6KAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,gFAAM;AACR,EAAE,yFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AChEA;AAAA;AAAA;AAAA;AAAkmB,CAAgB,+mBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;ACyItnB;AACA;AAAA;AAAA;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;QACAC;QACAC;QACAC;QACAC;MACA;MACA;MACAC;QACAJ;QACAC;QACAC;QACAC;MACA;MACAE;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;IACA;EACA;;EACAC,0CACA;IACAC;MAAA;IAAA;IACAH;MAAA;IAAA;EACA;IACA;IACAI;MAAA;MACA;MACA;;MAEA;MACA;QACA;MACA;;MAEA;MACA,wDACA;QAAA;MAAA,EACA;IACA;IACAC;MACA;MACA;MACAC;MAEA;MACAC;MAEA;MAEA;MACA;MACA;MAEA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA;QACAhB;QACAC;QACAC;QACAC;MACA;;MAEA;MACA;QACAH;QACAC;QACAC;QACAC;MACA;IACA;EAAA,EACA;EACAc;IACA;IACAP;MACAQ;QACA;UACA;QACA;MACA;MACAC;IACA;EACA;EACAC;IACA;IACA;EACA;EACAC;IACA;IACA;EACA;EACAC;IACA;IACA;IACA;EACA;EACA;EACAC;IACAC;IACA;IACA;MACA;MACAC;MACA;MACAA;QACAC;QACAC;QACAC;MACA;IACA;EACA;EACAC,yCACA;IACAC;EACA;IACAC;MACAN;QACAO;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;QAAA;MAAA;IACA;IACAC;MACA;MACAC;QACAC;MACA;MACA;IACA;IACA;IACAC;MACA;MACA;MACAC;MACAA;MACAA;QACA;QACA;QACA;QACA;;QAEA;QACAC;MACA;IACA;IACA;IACAC;MACA;MACA;MACA;MACA;IACA;IACA;IACAC;MACA;MACAC;;MAEA;MACA;MACA9B;;MAEA;MACA8B;MAEA;QACA;QACAlB;UACAC;UACAC;UACAC;QACA;QACA;MACA;MAEA;MACA;MACA;MACA;IACA;IACA;IACAgB;MAAA;MAAA;MACA;MACA;;MAEA;MACA;MACA;QACA;MACA;;MAEA;MACA;QACAC;QACAC;MACA;;MAEA;MACA,qCACAC;QACA;UACA;UACA;UACA;UACA;QACA;QACA;MACA,GACAC;QACAvB;UACAC;UACAC;UACAC;QACA;QACA;MACA;IACA;IACA;IACAqB;MACA;MACA;;MAEA;MACA;IACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;;MAEA;MACA;MACA;;MAEA;MACA;IACA;EAAA;AAEA;AAAA,2B;;;;;;;;;;;;;AC1YA;AAAA;AAAA;AAAA;AAA6oC,CAAgB,8lCAAG,EAAC,C;;;;;;;;;;;ACAjqC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/diet-record/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/diet-record/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=8d6495b8&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/diet-record/index.vue\"\nexport default component.exports", "export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=8d6495b8&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l1 = _vm.__map(Object.keys(_vm.mealTypes), function (mealType, index) {\n    var $orig = _vm.__get_orig(mealType)\n    var m0 = _vm.getMealTypeName(mealType)\n    var m1 = _vm.getMealTotalCalorie(_vm.mealRecords[mealType] || [])\n    var m2 = _vm.getMealTotalNutrition(\n      _vm.mealRecords[mealType] || [],\n      \"protein\"\n    )\n    var m3 = _vm.getMealTotalNutrition(_vm.mealRecords[mealType] || [], \"carbs\")\n    var m4 = _vm.getMealTotalNutrition(_vm.mealRecords[mealType] || [], \"fat\")\n    var g0 =\n      !_vm.mealRecords[mealType] || _vm.mealRecords[mealType].length === 0\n    var l0 = _vm.__map(_vm.mealRecords[mealType], function (food, foodIndex) {\n      var $orig = _vm.__get_orig(food)\n      var m5 = _vm.formatFoodAmount(food)\n      var m6 = _vm.formatNutrition(food.protein)\n      var m7 = _vm.formatNutrition(food.carbs)\n      var m8 = _vm.formatNutrition(food.fat)\n      var m9 = _vm.formatNutrition(food.calorie)\n      return {\n        $orig: $orig,\n        m5: m5,\n        m6: m6,\n        m7: m7,\n        m8: m8,\n        m9: m9,\n      }\n    })\n    return {\n      $orig: $orig,\n      m0: m0,\n      m1: m1,\n      m2: m2,\n      m3: m3,\n      m4: m4,\n      g0: g0,\n      l0: l0,\n    }\n  })\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event, mealType) {\n      var _temp = arguments[arguments.length - 1].currentTarget.dataset,\n        _temp2 = _temp.eventParams || _temp[\"event-params\"],\n        mealType = _temp2.mealType\n      var _temp, _temp2\n      return _vm.navigateTo(\"/pages/diet-record/add?mealType=\" + mealType)\n    }\n  }\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l1: l1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\r\n  <view class=\"diet-record\">\r\n    <!-- 顶部安全区域 -->\r\n    <view class=\"safe-area\"></view>\r\n\r\n    <!-- 顶部标题 -->\r\n    <view class=\"header\">\r\n      <text class=\"title\">饮食记录</text>\r\n    </view>\r\n\r\n    <!-- 使用scroll-view包裹内容区域 -->\r\n    <scroll-view scroll-y class=\"page-content\" :style=\"{ height: contentHeight + 'px' }\">\r\n      <!-- 日期选择器 -->\r\n      <view class=\"date-selector\">\r\n        <view class=\"date-actions\">\r\n          <view class=\"date-arrow\" @click=\"changeDate(-1)\">\r\n            <image src=\"/static/icons/arrow-left.png\"></image>\r\n          </view>\r\n          <picker mode=\"date\" :value=\"datePickerValue\" :end=\"maxDate\" @change=\"onDateChange\">\r\n            <view class=\"date-display\">\r\n              <text class=\"date-title\">{{ dateTitle }}</text>\r\n              <text class=\"date-value\">{{ formattedDate }}</text>\r\n            </view>\r\n          </picker>\r\n          <view class=\"date-arrow\" @click=\"changeDate(1)\">\r\n            <image src=\"/static/icons/arrow-right.png\"></image>\r\n          </view>\r\n        </view>\r\n      </view>\r\n\r\n      <!-- 餐次过滤 -->\r\n      <view class=\"meal-filter\">\r\n        <view\r\n          class=\"filter-item\"\r\n          :class=\"{ active: selectedMealType === '' }\"\r\n          @click=\"filterByMealType('')\"\r\n        >\r\n          <text>全部</text>\r\n        </view>\r\n        <view\r\n          class=\"filter-item\"\r\n          v-for=\"(name, type) in mealTypes\"\r\n          :key=\"type\"\r\n          :class=\"{ active: selectedMealType === type }\"\r\n          @click=\"filterByMealType(type)\"\r\n        >\r\n          <text>{{ name }}</text>\r\n        </view>\r\n      </view>\r\n\r\n      <!-- 添加饮食按钮（卡片式设计） -->\r\n      <view class=\"add-food-card\" @click=\"navigateTo('/pages/diet-record/add')\">\r\n        <view class=\"add-food-content\">\r\n          <view class=\"add-food-icon\">\r\n            <image src=\"/static/icons/add-food.png\"></image>\r\n          </view>\r\n          <view class=\"add-food-info\">\r\n            <text class=\"add-food-title\">记录今日饮食</text>\r\n            <text class=\"add-food-desc\">点击添加您的饮食记录</text>\r\n          </view>\r\n          <view class=\"add-food-arrow\">\r\n            <image src=\"/static/icons/arrow-right.png\"></image>\r\n          </view>\r\n        </view>\r\n      </view>\r\n\r\n      <!-- 饮食列表 -->\r\n      <view class=\"diet-list\">\r\n        <view v-if=\"loading && !isDataReady\" class=\"loading-state\">\r\n          <view class=\"loading-dots\">\r\n            <view class=\"dot\"></view>\r\n            <view class=\"dot\"></view>\r\n            <view class=\"dot\"></view>\r\n          </view>\r\n          <text>加载中...</text>\r\n        </view>\r\n        <view v-else-if=\"!loading && !isDataReady\" class=\"empty-data-state\">\r\n          <image src=\"/static/icons/empty.png\" class=\"empty-icon\"></image>\r\n          <text>暂无数据</text>\r\n        </view>\r\n        <view v-show=\"isDataReady\" class=\"meal-sections-container\">\r\n          <template v-for=\"(mealType, index) in Object.keys(mealTypes)\">\r\n            <transition :key=\"mealType\" name=\"fade\">\r\n              <view\r\n                v-show=\"selectedMealType === '' || selectedMealType === mealType\"\r\n                :key=\"mealType\"\r\n                class=\"meal-section\"\r\n              >\r\n                <view class=\"meal-header\">\r\n                  <text class=\"meal-type\">{{ getMealTypeName(mealType) }}</text>\r\n                  <view class=\"meal-info\">\r\n                    <text class=\"meal-calorie\">{{ getMealTotalCalorie(mealRecords[mealType] || []) }} 千卡</text>\r\n                    <view class=\"meal-nutrition\">\r\n                      <text>蛋白质 {{ getMealTotalNutrition(mealRecords[mealType] || [], 'protein') }}g</text>\r\n                      <text>碳水 {{ getMealTotalNutrition(mealRecords[mealType] || [], 'carbs') }}g</text>\r\n                      <text>脂肪 {{ getMealTotalNutrition(mealRecords[mealType] || [], 'fat') }}g</text>\r\n                    </view>\r\n                  </view>\r\n                  <view class=\"meal-action\" @click=\"navigateTo(`/pages/diet-record/add?mealType=${mealType}`)\">\r\n                    <image src=\"/static/icons/add-small.png\"></image>\r\n                  </view>\r\n                </view>\r\n\r\n                <view class=\"food-list\">\r\n                  <view class=\"empty-tip\" v-if=\"!mealRecords[mealType] || mealRecords[mealType].length === 0\">\r\n                    <image src=\"/static/icons/empty.png\" class=\"empty-food-icon\"></image>\r\n                    <text>暂无记录</text>\r\n                  </view>\r\n                  <view\r\n                    class=\"food-item\"\r\n                    v-for=\"(food, foodIndex) in mealRecords[mealType]\"\r\n                    :key=\"foodIndex\"\r\n                  >\r\n                    <view class=\"food-info\">\r\n                      <text class=\"food-name\">{{ food.name }}</text>\r\n                      <text class=\"food-desc\">{{ formatFoodAmount(food) }}</text>\r\n                      <view class=\"food-nutrition\">\r\n                        <text class=\"nutrition-item\">蛋白质: {{ formatNutrition(food.protein) }}g</text>\r\n                        <text class=\"nutrition-item\">碳水: {{ formatNutrition(food.carbs) }}g</text>\r\n                        <text class=\"nutrition-item\">脂肪: {{ formatNutrition(food.fat) }}g</text>\r\n                      </view>\r\n                    </view>\r\n                    <view class=\"food-calorie\">\r\n                      <text>{{ formatNutrition(food.calorie) }} 千卡</text>\r\n                    </view>\r\n                  </view>\r\n                </view>\r\n              </view>\r\n            </transition>\r\n          </template>\r\n        </view>\r\n      </view>\r\n    </scroll-view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nimport { formatDate } from '@/utils/date.js'\r\nimport { mapState, mapActions } from 'vuex'\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      selectedDate: new Date(),\r\n      datePickerValue: formatDate(new Date(), 'yyyy-MM-dd'), // 日期选择器的值\r\n      maxDate: formatDate(new Date(), 'yyyy-MM-dd'), // 日期选择器的最大可选日期（今天）\r\n      mealTypes: {\r\n        breakfast: '早餐',\r\n        lunch: '午餐',\r\n        dinner: '晚餐',\r\n        snacks: '加餐'\r\n      },\r\n      // 缓存的完整饮食记录数据\r\n      cachedDietRecords: {\r\n        breakfast: [],\r\n        lunch: [],\r\n        dinner: [],\r\n        snacks: []\r\n      },\r\n      lastLoadTime: 0, // 上次加载数据的时间戳\r\n      contentHeight: 0, // 内容区域高度\r\n      loading: false, // 加载状态\r\n      selectedMealType: '' // 选中的餐次\r\n    }\r\n  },\r\n  computed: {\r\n    ...mapState({\r\n      dietRecords: state => state.diet.dietRecords,\r\n      loading: state => state.diet.loading\r\n    }),\r\n    // 数据是否准备好显示\r\n    isDataReady() {\r\n      // 检查 dietRecords 中的各个餐次是否已定义\r\n      if (!this.dietRecords) return false\r\n\r\n      // 如果已选择特定餐次，只检查该餐次\r\n      if (this.selectedMealType) {\r\n        return Array.isArray(this.dietRecords[this.selectedMealType])\r\n      }\r\n\r\n      // 检查所有餐次是否已定义\r\n      return ['breakfast', 'lunch', 'dinner', 'snacks'].every(\r\n        type => Array.isArray(this.dietRecords[type])\r\n      )\r\n    },\r\n    dateTitle() {\r\n      // 判断是否是今天、昨天或前天\r\n      const today = new Date()\r\n      today.setHours(0, 0, 0, 0)\r\n\r\n      const selectedDateObj = new Date(this.selectedDate)\r\n      selectedDateObj.setHours(0, 0, 0, 0)\r\n\r\n      const diffDays = Math.round((selectedDateObj - today) / (1000 * 60 * 60 * 24))\r\n\r\n      if (diffDays === 0) return '今天'\r\n      if (diffDays === -1) return '昨天'\r\n      if (diffDays === -2) return '前天'\r\n\r\n      return '' // 其他日期不显示特殊标题\r\n    },\r\n    formattedDate() {\r\n      return formatDate(this.selectedDate, 'yyyy年M月d日')\r\n    },\r\n    mealRecords() {\r\n      // 优先使用从store获取的数据，如果没有则使用缓存数据\r\n      const records = this.dietRecords || this.cachedDietRecords || {\r\n        breakfast: [],\r\n        lunch: [],\r\n        dinner: [],\r\n        snacks: []\r\n      };\r\n\r\n      // 确保所有餐次都有值，防止undefined错误\r\n      return {\r\n        breakfast: Array.isArray(records.breakfast) ? records.breakfast : [],\r\n        lunch: Array.isArray(records.lunch) ? records.lunch : [],\r\n        dinner: Array.isArray(records.dinner) ? records.dinner : [],\r\n        snacks: Array.isArray(records.snacks) ? records.snacks : []\r\n      };\r\n    }\r\n  },\r\n  watch: {\r\n    // 监听 dietRecords 变化，更新缓存\r\n    dietRecords: {\r\n      handler(newVal) {\r\n        if (newVal) {\r\n          this.cachedDietRecords = JSON.parse(JSON.stringify(newVal));\r\n        }\r\n      },\r\n      deep: true\r\n    }\r\n  },\r\n  onLoad() {\r\n    this.loadDietRecords()\r\n    this.calcContentHeight()\r\n  },\r\n  onReady() {\r\n    // 页面渲染完成后再次计算内容高度\r\n    this.calcContentHeight()\r\n  },\r\n  onShow() {\r\n    // 每次显示页面时计算内容高度和刷新数据\r\n    this.calcContentHeight()\r\n    this.loadDietRecords()\r\n  },\r\n  // 添加下拉刷新处理方法\r\n  onPullDownRefresh() {\r\n    console.log('饮食记录：下拉刷新')\r\n    // 强制刷新数据\r\n    this.loadDietRecords(true).then(() => {\r\n      // 停止下拉刷新动画\r\n      uni.stopPullDownRefresh()\r\n      // 显示刷新成功提示\r\n      uni.showToast({\r\n        title: '刷新成功',\r\n        icon: 'success',\r\n        duration: 1000\r\n      })\r\n    })\r\n  },\r\n  methods: {\r\n    ...mapActions({\r\n      fetchDietRecords: 'diet/getDietRecords'\r\n    }),\r\n    navigateTo(url) {\r\n      uni.navigateTo({\r\n        url\r\n      })\r\n    },\r\n    getMealTypeName(type) {\r\n      return this.mealTypes[type] || type\r\n    },\r\n    getMealTotalCalorie(meal) {\r\n      return meal.reduce((total, food) => total + food.calorie, 0)\r\n    },\r\n    getMealTotalNutrition(meal, nutrient) {\r\n      let total = 0\r\n      meal.forEach(food => {\r\n        total += food[nutrient]\r\n      })\r\n      return total\r\n    },\r\n    // 计算内容区域高度\r\n    calcContentHeight() {\r\n      const that = this\r\n      const query = uni.createSelectorQuery().in(this)\r\n      query.select('.header').boundingClientRect()\r\n      query.select('.safe-area').boundingClientRect()\r\n      query.exec(function(res) {\r\n        // 获取屏幕高度\r\n        const windowHeight = uni.getSystemInfoSync().windowHeight\r\n        const headerHeight = res[0] ? res[0].height : 88\r\n        const safeAreaHeight = res[1] ? res[1].height : 80\r\n\r\n        // 计算内容区域高度 = 窗口高度 - 顶部安全区域 - 头部高度\r\n        that.contentHeight = windowHeight - safeAreaHeight - headerHeight\r\n      })\r\n    },\r\n    // 日期选择器变化处理\r\n    onDateChange(e) {\r\n      this.datePickerValue = e.detail.value;\r\n      this.selectedDate = new Date(e.detail.value.replace(/-/g, '/'));\r\n      // 强制刷新数据\r\n      this.loadDietRecords(true);\r\n    },\r\n    // 点击切换前后一天\r\n    changeDate(days) {\r\n      const newDate = new Date(this.selectedDate);\r\n      newDate.setDate(newDate.getDate() + days);\r\n\r\n      // 检查是否超过今天\r\n      const today = new Date();\r\n      today.setHours(0, 0, 0, 0);\r\n\r\n      // 规范化newDate，清除时间部分，只保留日期部分\r\n      newDate.setHours(0, 0, 0, 0);\r\n\r\n      if (newDate > today && days > 0) {\r\n        // 如果是向后选择且超过今天，则不执行并提示用户\r\n        uni.showToast({\r\n          title: '不能选择今天之后的日期',\r\n          icon: 'none',\r\n          duration: 2000\r\n        });\r\n        return;\r\n      }\r\n\r\n      this.selectedDate = newDate;\r\n      this.datePickerValue = formatDate(newDate, 'yyyy-MM-dd');\r\n      // 强制刷新数据\r\n      this.loadDietRecords(true);\r\n    },\r\n    // 加载饮食记录\r\n    loadDietRecords(forceRefresh = false) {\r\n      // 如果已经在加载中，返回一个已解决的Promise\r\n      if (this.loading) return Promise.resolve();\r\n\r\n      // 如果没有强制刷新，并且在短时间内已经加载过（5秒内），则不重新加载\r\n      const now = Date.now();\r\n      if (!forceRefresh && (now - this.lastLoadTime < 5000)) {\r\n        return Promise.resolve();\r\n      }\r\n\r\n      // 使用单日模式参数\r\n      const params = {\r\n        date: formatDate(this.selectedDate, 'yyyy-MM-dd'),\r\n        reset: true\r\n      };\r\n\r\n      // 返回Promise以便链式调用\r\n      return this.fetchDietRecords(params)\r\n        .then(response => {\r\n          if (response && response.code === 200) {\r\n            // 更新缓存数据\r\n            this.cachedDietRecords = JSON.parse(JSON.stringify(this.dietRecords));\r\n            // 更新加载时间戳\r\n            this.lastLoadTime = now;\r\n          }\r\n          return response;\r\n        })\r\n        .catch(error => {\r\n          uni.showToast({\r\n            title: error?.message || '加载饮食记录失败',\r\n            icon: 'none',\r\n            duration: 2000\r\n          });\r\n          return Promise.reject(error);\r\n        });\r\n    },\r\n    // 过滤饮食记录\r\n    filterByMealType(type) {\r\n      // 如果已经选中该类型，不做任何操作\r\n      if (this.selectedMealType === type) return;\r\n\r\n      // 只更新选中状态，不重新加载数据\r\n      this.selectedMealType = type;\r\n    },\r\n    // 格式化营养信息\r\n    formatNutrition(value) {\r\n      return value.toFixed(2)\r\n    },\r\n    // 优化格式化食物量的方法\r\n    formatFoodAmount(food) {\r\n      if (!food) return '';\r\n\r\n      // 获取单位和数量\r\n      const unit = food.unit || food.desc || ''; // 尝试获取单位，优先使用unit字段\r\n      const amount = parseFloat(food.amount) || 1; // 确保amount是数字，并处理可能的undefined或0值\r\n\r\n      // 返回格式化的结果\r\n      return `${amount}*${unit}`;\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.diet-record {\r\n  background-color: #f8f8f8;\r\n  min-height: 100vh;\r\n  display: flex;\r\n  flex-direction: column;\r\n  padding: 0 20rpx 20rpx;\r\n}\r\n\r\n.safe-area {\r\n  height: 80rpx; /* 增加顶部安全区域高度 */\r\n  background-color: #f8f8f8;\r\n}\r\n\r\n.header {\r\n  padding: 0 30rpx;\r\n  height: 88rpx; /* 与胶囊按钮高度一致 */\r\n  display: flex;\r\n  align-items: center;\r\n  background-color: #f8f8f8;\r\n\r\n  .title {\r\n    font-size: 36rpx;\r\n    font-weight: bold;\r\n    color: #333;\r\n  }\r\n}\r\n\r\n.page-content {\r\n  flex: 1;\r\n  box-sizing: border-box;\r\n}\r\n\r\n.date-selector {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  padding: 20rpx 30rpx;\r\n  background-color: #ffffff;\r\n  margin-bottom: 20rpx;\r\n  border-radius: 10rpx;\r\n\r\n  .date-actions {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    width: 100%;\r\n    padding: 0;\r\n\r\n    .date-arrow {\r\n      width: 40rpx;\r\n      height: 40rpx;\r\n      display: flex;\r\n      justify-content: center;\r\n      align-items: center;\r\n\r\n      image {\r\n        width: 24rpx;\r\n        height: 24rpx;\r\n      }\r\n    }\r\n\r\n    .date-display {\r\n      flex: 1;\r\n      display: flex;\r\n      flex-direction: column;\r\n      align-items: center;\r\n      padding: 10rpx 0;\r\n      max-width: 300rpx;\r\n\r\n      .date-title {\r\n        font-size: 40rpx;\r\n        font-weight: bold;\r\n        color: #333;\r\n        margin-bottom: 10rpx;\r\n      }\r\n\r\n      .date-value {\r\n        font-size: 28rpx;\r\n        color: #999;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.meal-filter {\r\n  display: flex;\r\n  overflow-x: auto;\r\n  padding: 15rpx 0;\r\n  margin-bottom: 20rpx;\r\n  white-space: nowrap;\r\n\r\n  &::-webkit-scrollbar {\r\n    display: none;\r\n  }\r\n\r\n  .filter-item {\r\n    display: inline-flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    height: 68rpx;\r\n    padding: 0 30rpx;\r\n    background-color: #F5F5F5;\r\n    border-radius: 34rpx;\r\n    margin-right: 15rpx;\r\n    transition: all 0.3s ease;\r\n\r\n    text {\r\n      font-size: 26rpx;\r\n      color: #666666;\r\n      transition: all 0.3s ease;\r\n    }\r\n\r\n    &.active {\r\n      background-color: #4CAF50;\r\n      box-shadow: 0 2rpx 8rpx rgba(76, 175, 80, 0.3);\r\n\r\n      text {\r\n        color: #FFFFFF;\r\n        font-weight: bold;\r\n      }\r\n    }\r\n\r\n    &:active {\r\n      transform: scale(0.96);\r\n      opacity: 0.9;\r\n    }\r\n  }\r\n}\r\n\r\n.add-food-card {\r\n  background-color: #ffffff;\r\n  border-radius: 16rpx;\r\n  margin: 20rpx 0;\r\n  padding: 6rpx;\r\n  box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.08);\r\n  transition: all 0.3s ease;\r\n\r\n  &:active {\r\n    transform: scale(0.98);\r\n    box-shadow: 0 2rpx 5rpx rgba(0, 0, 0, 0.1);\r\n  }\r\n\r\n  .add-food-content {\r\n    display: flex;\r\n    align-items: center;\r\n    padding: 20rpx;\r\n\r\n    .add-food-icon {\r\n      width: 80rpx;\r\n      height: 80rpx;\r\n      margin-right: 20rpx;\r\n\r\n      image {\r\n        width: 100%;\r\n        height: 100%;\r\n      }\r\n    }\r\n\r\n    .add-food-info {\r\n      flex: 1;\r\n\r\n      .add-food-title {\r\n        font-size: 30rpx;\r\n        font-weight: bold;\r\n        color: #333333;\r\n        margin-bottom: 8rpx;\r\n      }\r\n\r\n      .add-food-desc {\r\n\t\tmargin-left: 10rpx;\r\n        font-size: 24rpx;\r\n        color: #999999;\r\n      }\r\n    }\r\n\r\n    .add-food-arrow {\r\n      width: 40rpx;\r\n      height: 40rpx;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n\r\n      image {\r\n        width: 24rpx;\r\n        height: 24rpx;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.diet-list {\r\n  margin-bottom: 20rpx;\r\n\r\n  .loading-state {\r\n    text-align: center;\r\n    padding: 40rpx 0;\r\n    display: flex;\r\n    flex-direction: column;\r\n    align-items: center;\r\n    min-height: 200rpx;\r\n\r\n    .loading-dots {\r\n      display: flex;\r\n      justify-content: center;\r\n      margin-bottom: 15rpx;\r\n\r\n      .dot {\r\n        width: 16rpx;\r\n        height: 16rpx;\r\n        border-radius: 50%;\r\n        background-color: #4CAF50;\r\n        margin: 0 8rpx;\r\n        opacity: 0.6;\r\n        animation: dot-bounce 1.4s infinite ease-in-out both;\r\n\r\n        &:nth-child(1) {\r\n          animation-delay: 0s;\r\n        }\r\n\r\n        &:nth-child(2) {\r\n          animation-delay: 0.2s;\r\n        }\r\n\r\n        &:nth-child(3) {\r\n          animation-delay: 0.4s;\r\n        }\r\n      }\r\n    }\r\n\r\n    text {\r\n      font-size: 28rpx;\r\n      color: #999;\r\n    }\r\n  }\r\n\r\n  .empty-data-state {\r\n    text-align: center;\r\n    padding: 60rpx 0;\r\n    color: #999;\r\n    font-size: 28rpx;\r\n    display: flex;\r\n    flex-direction: column;\r\n    align-items: center;\r\n    justify-content: center;\r\n    background-color: #ffffff;\r\n    border-radius: 12rpx;\r\n    margin: 20rpx 0;\r\n    min-height: 300rpx;\r\n    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);\r\n\r\n    .empty-icon {\r\n      width: 120rpx;\r\n      height: 120rpx;\r\n      margin-bottom: 20rpx;\r\n      opacity: 0.7;\r\n    }\r\n\r\n    text {\r\n      font-size: 28rpx;\r\n      color: #999;\r\n    }\r\n  }\r\n\r\n  .meal-sections-container {\r\n    width: 100%;\r\n\r\n    .meal-section {\r\n      background-color: #ffffff;\r\n      margin-bottom: 30rpx;\r\n      border-radius: 16rpx;\r\n      overflow: hidden;\r\n      padding: 20rpx;\r\n      box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);\r\n\r\n      .meal-header {\r\n        display: flex;\r\n        align-items: center;\r\n        margin-bottom: 20rpx;\r\n        padding: 0 10rpx;\r\n\r\n        .meal-type {\r\n          font-size: 32rpx;\r\n          font-weight: bold;\r\n          color: #333333;\r\n          margin-right: 20rpx;\r\n          padding: 6rpx 0;\r\n        }\r\n\r\n        .meal-info {\r\n          flex: 1;\r\n          display: flex;\r\n          flex-direction: column;\r\n\r\n          .meal-calorie {\r\n            font-size: 28rpx;\r\n            color: #4CAF50;\r\n            font-weight: bold;\r\n            margin-bottom: 4rpx;\r\n          }\r\n\r\n          .meal-nutrition {\r\n            display: flex;\r\n            flex-wrap: wrap;\r\n\r\n            text {\r\n              font-size: 22rpx;\r\n              color: #999999;\r\n              margin-right: 16rpx;\r\n            }\r\n          }\r\n        }\r\n\r\n        .meal-action {\r\n          width: 60rpx;\r\n          height: 60rpx;\r\n          display: flex;\r\n          justify-content: center;\r\n          align-items: center;\r\n          border-radius: 30rpx;\r\n          background-color: #4CAF50;\r\n          margin-left: 10rpx;\r\n          box-shadow: 0 2rpx 6rpx rgba(76, 175, 80, 0.3);\r\n\r\n          image {\r\n            width: 32rpx;\r\n            height: 32rpx;\r\n            filter: brightness(0) invert(1); /* 使图标变为白色 */\r\n          }\r\n\r\n          &:active {\r\n            background-color: #388E3C;\r\n            transform: scale(0.95);\r\n          }\r\n        }\r\n      }\r\n\r\n      .food-list {\r\n        .empty-tip {\r\n          display: flex;\r\n          flex-direction: column;\r\n          justify-content: center;\r\n          align-items: center;\r\n          height: 160rpx;\r\n          background-color: #f9f9f9;\r\n          border-radius: 12rpx;\r\n          margin: 10rpx;\r\n          box-shadow: 0 1rpx 3rpx rgba(0, 0, 0, 0.03);\r\n          transition: all 0.3s ease;\r\n\r\n          .empty-food-icon {\r\n            width: 64rpx;\r\n            height: 64rpx;\r\n            margin-bottom: 12rpx;\r\n            opacity: 0.6;\r\n          }\r\n\r\n          text {\r\n            font-size: 26rpx;\r\n            color: #999999;\r\n          }\r\n        }\r\n\r\n        .food-item {\r\n          display: flex;\r\n          align-items: center;\r\n          padding: 20rpx;\r\n          background-color: #FFFFFF;\r\n          border-radius: 12rpx;\r\n          margin: 10rpx;\r\n          box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);\r\n          transition: all 0.2s ease;\r\n\r\n          &:active {\r\n            transform: scale(0.99);\r\n            box-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.08);\r\n          }\r\n\r\n          .food-info {\r\n            flex: 1;\r\n\r\n            .food-name {\r\n              font-size: 28rpx;\r\n              font-weight: bold;\r\n              color: #333333;\r\n              margin-bottom: 8rpx;\r\n            }\r\n\r\n            .food-desc {\r\n              font-size: 24rpx;\r\n              color: #666666;\r\n              margin-bottom: 10rpx;\r\n            }\r\n\r\n            .food-nutrition {\r\n              display: flex;\r\n              flex-wrap: wrap;\r\n\r\n              .nutrition-item {\r\n                font-size: 22rpx;\r\n                color: #999999;\r\n                margin-right: 16rpx;\r\n                padding: 4rpx 12rpx;\r\n                background-color: #F5F5F5;\r\n                border-radius: 12rpx;\r\n                margin-bottom: 4rpx;\r\n              }\r\n            }\r\n          }\r\n\r\n          .food-calorie {\r\n            font-size: 28rpx;\r\n            font-weight: bold;\r\n            color: #4CAF50;\r\n            padding-left: 20rpx;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n@keyframes dot-bounce {\r\n  0%, 80%, 100% {\r\n    transform: scale(0);\r\n    opacity: 0.3;\r\n  }\r\n  40% {\r\n    transform: scale(1);\r\n    opacity: 1;\r\n  }\r\n}\r\n\r\n/* 添加淡入淡出过渡效果 */\r\n.fade-enter-active, .fade-leave-active {\r\n  transition: opacity 0.3s ease;\r\n}\r\n.fade-enter, .fade-leave-to {\r\n  opacity: 0;\r\n}\r\n</style>", "import mod from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1751160935256\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}