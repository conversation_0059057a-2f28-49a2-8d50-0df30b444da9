@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.health-report {
  padding: 0 20rpx 20rpx;
  background-color: #f8f8f8;
  min-height: 100vh;
}
.safe-area {
  height: 80rpx;
  background-color: #f8f8f8;
}
.header {
  padding: 0 30rpx;
  height: 88rpx;
  display: flex;
  align-items: center;
  position: relative;
}
.header .back-btn {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}
.header .back-btn image {
  width: 36rpx;
  height: 36rpx;
}
.header .title {
  flex: 1;
  text-align: center;
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300rpx;
}
.loading-container .loading-box {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.loading-container .loading-box .loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #4CAF50;
  border-radius: 50%;
  -webkit-animation: spin 1s linear infinite;
          animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}
.loading-container .loading-box .loading-text {
  font-size: 28rpx;
  color: #666;
}
@-webkit-keyframes spin {
0% {
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
}
100% {
    -webkit-transform: rotate(360deg);
            transform: rotate(360deg);
}
}
@keyframes spin {
0% {
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
}
100% {
    -webkit-transform: rotate(360deg);
            transform: rotate(360deg);
}
}
.content-container {
  padding-bottom: 30rpx;
}
.date-selector {
  background-color: #ffffff;
  border-radius: 20rpx;
  padding: 16rpx 24rpx;
  margin: 0 20rpx 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}
.date-selector .date-actions {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.date-selector .date-actions .date-arrow {
  width: 56rpx;
  height: 56rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f8f8f8;
  border-radius: 28rpx;
}
.date-selector .date-actions .date-arrow image {
  width: 30rpx;
  height: 30rpx;
}
.date-selector .date-actions .date-display {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.date-selector .date-actions .date-display .date-title {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 4rpx;
}
.date-selector .date-actions .date-display .date-value {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}
.card {
  background-color: #ffffff;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  -webkit-animation: fadeIn 0.5s ease-in-out;
          animation: fadeIn 0.5s ease-in-out;
}
@-webkit-keyframes fadeIn {
from {
    opacity: 0;
    -webkit-transform: translateY(20rpx);
            transform: translateY(20rpx);
}
to {
    opacity: 1;
    -webkit-transform: translateY(0);
            transform: translateY(0);
}
}
@keyframes fadeIn {
from {
    opacity: 0;
    -webkit-transform: translateY(20rpx);
            transform: translateY(20rpx);
}
to {
    opacity: 1;
    -webkit-transform: translateY(0);
            transform: translateY(0);
}
}
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}
.section-header .section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  position: relative;
  padding-left: 20rpx;
}
.section-header .section-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
  width: 8rpx;
  height: 32rpx;
  background-color: #4CAF50;
  border-radius: 4rpx;
}
.section-header .date-info {
  font-size: 24rpx;
  color: #999;
}
.health-score .score-container {
  display: flex;
  align-items: center;
  justify-content: space-around;
  padding: 20rpx 0;
}
.health-score .score-container .score-circle {
  width: 200rpx;
  height: 200rpx;
  border-radius: 100rpx;
  background: linear-gradient(135deg, #4CAF50, #8BC34A);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  box-shadow: 0 8rpx 16rpx rgba(76, 175, 80, 0.2);
  -webkit-animation: pulse 2s infinite;
          animation: pulse 2s infinite;
}
.health-score .score-container .score-circle .score-value {
  font-size: 60rpx;
  font-weight: bold;
  color: #fff;
  line-height: 1;
  margin-bottom: 10rpx;
}
.health-score .score-container .score-circle .score-desc {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
}
.health-score .score-container .score-change {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.health-score .score-container .score-change .change-value {
  display: flex;
  align-items: center;
  font-size: 48rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}
.health-score .score-container .score-change .change-value.positive {
  color: #4CAF50;
}
.health-score .score-container .score-change .change-value.negative {
  color: #F44336;
}
.health-score .score-container .score-change .change-value .change-icon {
  width: 32rpx;
  height: 32rpx;
  margin-left: 8rpx;
}
.health-score .score-container .score-change .change-value .change-icon image {
  width: 100%;
  height: 100%;
}
.health-score .score-container .score-change .change-label {
  font-size: 24rpx;
  color: #999;
}
@-webkit-keyframes pulse {
0% {
    box-shadow: 0 8rpx 16rpx rgba(76, 175, 80, 0.2);
}
50% {
    box-shadow: 0 8rpx 24rpx rgba(76, 175, 80, 0.4);
}
100% {
    box-shadow: 0 8rpx 16rpx rgba(76, 175, 80, 0.2);
}
}
@keyframes pulse {
0% {
    box-shadow: 0 8rpx 16rpx rgba(76, 175, 80, 0.2);
}
50% {
    box-shadow: 0 8rpx 24rpx rgba(76, 175, 80, 0.4);
}
100% {
    box-shadow: 0 8rpx 16rpx rgba(76, 175, 80, 0.2);
}
}
.nutrition-balance .radar-container {
  height: 500rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}
.nutrition-balance :deep(.qiun-charts) {
  width: 100%;
  height: 500rpx;
}
.nutrition-balance .radar-legend {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-around;
  margin-top: 30rpx;
}
.nutrition-balance .radar-legend .legend-item {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
  width: 30%;
}
.nutrition-balance .radar-legend .legend-item .legend-color {
  width: 20rpx;
  height: 20rpx;
  border-radius: 4rpx;
  margin-right: 10rpx;
}
.nutrition-balance .radar-legend .legend-item .legend-label {
  font-size: 24rpx;
  color: #666;
  margin-right: 10rpx;
}
.nutrition-balance .radar-legend .legend-item .legend-value {
  font-size: 24rpx;
  color: #333;
  font-weight: 500;
}
.nutrition-compare .compare-container {
  display: flex;
  flex-direction: column;
}
.nutrition-compare .compare-container .compare-item {
  margin-bottom: 30rpx;
}
.nutrition-compare .compare-container .compare-item:last-child {
  margin-bottom: 0;
}
.nutrition-compare .compare-container .compare-item .compare-header {
  display: flex;
  align-items: center;
  margin-bottom: 15rpx;
}
.nutrition-compare .compare-container .compare-item .compare-header .compare-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 10rpx;
}
.nutrition-compare .compare-container .compare-item .compare-header .compare-icon image {
  width: 100%;
  height: 100%;
}
.nutrition-compare .compare-container .compare-item .compare-header .compare-label {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}
.nutrition-compare .compare-container .compare-item .compare-data {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #f9f9f9;
  border-radius: 12rpx;
  padding: 20rpx;
}
.nutrition-compare .compare-container .compare-item .compare-data .data-col {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 30%;
}
.nutrition-compare .compare-container .compare-item .compare-data .data-col .data-value {
  font-size: 32rpx;
  color: #666;
  margin-bottom: 5rpx;
}
.nutrition-compare .compare-container .compare-item .compare-data .data-col .data-value.highlight {
  color: #333;
  font-weight: bold;
}
.nutrition-compare .compare-container .compare-item .compare-data .data-col .data-desc {
  font-size: 24rpx;
  color: #999;
}
.nutrition-compare .compare-container .compare-item .compare-data .direction-indicator {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.nutrition-compare .compare-container .compare-item .compare-data .direction-indicator .indicator-arrow {
  width: 40rpx;
  height: 40rpx;
  margin-bottom: 8rpx;
}
.nutrition-compare .compare-container .compare-item .compare-data .direction-indicator .indicator-arrow.increase image {
  width: 100%;
  height: 100%;
  -webkit-transform: rotate(0deg);
          transform: rotate(0deg);
}
.nutrition-compare .compare-container .compare-item .compare-data .direction-indicator .indicator-arrow.decrease image {
  width: 100%;
  height: 100%;
  -webkit-transform: rotate(180deg);
          transform: rotate(180deg);
}
.nutrition-compare .compare-container .compare-item .compare-data .direction-indicator .indicator-arrow.neutral image {
  width: 100%;
  height: 100%;
  -webkit-transform: rotate(0deg);
          transform: rotate(0deg);
}
.nutrition-compare .compare-container .compare-item .compare-data .direction-indicator .indicator-arrow image {
  width: 100%;
  height: 100%;
}
.nutrition-compare .compare-container .compare-item .compare-data .direction-indicator .change-rate {
  font-size: 24rpx;
}
.nutrition-compare .compare-container .compare-item .compare-data .direction-indicator .change-rate.increase {
  color: #4CAF50;
}
.nutrition-compare .compare-container .compare-item .compare-data .direction-indicator .change-rate.decrease {
  color: #F44336;
}
.nutrition-compare .compare-container .compare-item .compare-data .direction-indicator .change-rate.neutral {
  color: #999;
}

