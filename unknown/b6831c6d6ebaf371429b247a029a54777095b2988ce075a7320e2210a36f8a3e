<view class="diet-record"><view class="safe-area"></view><view class="header"><text class="title">饮食记录</text></view><scroll-view class="page-content" style="{{'height:'+(contentHeight+'px')+';'}}" scroll-y="{{true}}"><view class="date-selector"><view class="date-actions"><view data-event-opts="{{[['tap',[['changeDate',[-1]]]]]}}" class="date-arrow" bindtap="__e"><image src="/static/icons/arrow-left.png"></image></view><picker mode="date" value="{{datePickerValue}}" end="{{maxDate}}" data-event-opts="{{[['change',[['onDateChange',['$event']]]]]}}" bindchange="__e"><view class="date-display"><text class="date-title">{{dateTitle}}</text><text class="date-value">{{formattedDate}}</text></view></picker><view data-event-opts="{{[['tap',[['changeDate',[1]]]]]}}" class="date-arrow" bindtap="__e"><image src="/static/icons/arrow-right.png"></image></view></view></view><view class="meal-filter"><view data-event-opts="{{[['tap',[['filterByMealType',['']]]]]}}" class="{{['filter-item',(selectedMealType==='')?'active':'']}}" bindtap="__e"><text>全部</text></view><block wx:for="{{mealTypes}}" wx:for-item="name" wx:for-index="type" wx:key="type"><view data-event-opts="{{[['tap',[['filterByMealType',[type]]]]]}}" class="{{['filter-item',(selectedMealType===type)?'active':'']}}" bindtap="__e"><text>{{name}}</text></view></block></view><view data-event-opts="{{[['tap',[['navigateTo',['/pages/diet-record/add']]]]]}}" class="add-food-card" bindtap="__e"><view class="add-food-content"><view class="add-food-icon"><image src="/static/icons/add-food.png"></image></view><view class="add-food-info"><text class="add-food-title">记录今日饮食</text><text class="add-food-desc">点击添加您的饮食记录</text></view><view class="add-food-arrow"><image src="/static/icons/arrow-right.png"></image></view></view></view><view class="diet-list"><block wx:if="{{loading&&!isDataReady}}"><view class="loading-state"><view class="loading-dots"><view class="dot"></view><view class="dot"></view><view class="dot"></view></view><text>加载中...</text></view></block><block wx:else><block wx:if="{{!loading&&!isDataReady}}"><view class="empty-data-state"><image class="empty-icon" src="/static/icons/empty.png"></image><text>暂无数据</text></view></block></block><view hidden="{{!(isDataReady)}}" class="meal-sections-container"><block wx:for="{{$root.l1}}" wx:for-item="mealType" wx:for-index="index" wx:key="$orig"><transition vue-id="{{'70697062-1-'+index}}" name="fade" bind:__l="__l" vue-slots="{{['default']}}"><view hidden="{{!(selectedMealType===''||selectedMealType===mealType.$orig)}}" class="meal-section"><view class="meal-header"><text class="meal-type">{{mealType.m0}}</text><view class="meal-info"><text class="meal-calorie">{{mealType.m1+" 千卡"}}</text><view class="meal-nutrition"><text>{{"蛋白质 "+mealType.m2+"g"}}</text><text>{{"碳水 "+mealType.m3+"g"}}</text><text>{{"脂肪 "+mealType.m4+"g"}}</text></view></view><view data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" data-event-params="{{({mealType:mealType.$orig})}}" class="meal-action" bindtap="__e"><image src="/static/icons/add-small.png"></image></view></view><view class="food-list"><block wx:if="{{mealType.g0}}"><view class="empty-tip"><image class="empty-food-icon" src="/static/icons/empty.png"></image><text>暂无记录</text></view></block><block wx:for="{{mealType.l0}}" wx:for-item="food" wx:for-index="foodIndex" wx:key="foodIndex"><view class="food-item"><view class="food-info"><text class="food-name">{{food.$orig.name}}</text><text class="food-desc">{{food.m5}}</text><view class="food-nutrition"><text class="nutrition-item">{{"蛋白质: "+food.m6+"g"}}</text><text class="nutrition-item">{{"碳水: "+food.m7+"g"}}</text><text class="nutrition-item">{{"脂肪: "+food.m8+"g"}}</text></view></view><view class="food-calorie"><text>{{food.m9+" 千卡"}}</text></view></view></block></view></view></transition></block></view></view></scroll-view></view>