@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.nutrition-circle {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 10rpx;
}
.nutrition-circle .circle-container {
  position: relative;
  width: 180rpx;
  height: 180rpx;
  margin-bottom: 10rpx;
}
.nutrition-circle .circle-container .circle-canvas {
  width: 100%;
  height: 100%;
}
.nutrition-circle .circle-container .content {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  pointer-events: none;
  /* 允许点击穿透到canvas */
}
.nutrition-circle .circle-container .content .percentage {
  display: flex;
  align-items: flex-start;
}
.nutrition-circle .circle-container .content .percentage .percentage-value {
  font-size: 40rpx;
  font-weight: bold;
  color: #ffffff;
  line-height: 1;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}
.nutrition-circle .circle-container .content .percentage .percentage-symbol {
  font-size: 20rpx;
  color: #ffffff;
  margin-top: 8rpx;
  line-height: 1;
  opacity: 0.9;
}
.nutrition-circle .circle-container .content .label {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.95);
  margin-top: 6rpx;
  text-shadow: 0 1px 1px rgba(0, 0, 0, 0.1);
}
.nutrition-circle .circle-container .content .value-info {
  font-size: 20rpx;
  color: rgba(255, 255, 255, 0.9);
  margin-top: 6rpx;
}
.nutrition-circle .target-info {
  font-size: 20rpx;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 500;
}

