package nutrition

import (
	"gorm.io/gorm"
	"shikeyinxiang/internal/entities"
	"shikeyinxiang/internal/pkg/errs"
)

// INutritionAdviceRepo 定义了营养建议仓库需要实现的所有方法
type INutritionAdviceRepo interface {
	// 基础CRUD操作
	Create(advice *entities.NutritionAdvice) error
	GetByID(id int64) (*entities.NutritionAdvice, error)
	Update(advice *entities.NutritionAdvice) error
	Delete(id int64) error

	// 查询方法
	FindByConditionTypeAndPercentage(conditionType string, percentage int) ([]*entities.NutritionAdvice, error)
	FindDefaultAdvice() (*entities.NutritionAdvice, error)
	FindByConditionType(conditionType string) ([]*entities.NutritionAdvice, error)
	FindAll(offset, limit int) ([]*entities.NutritionAdvice, int64, error)
	FindByStatus(status int8, offset, limit int) ([]*entities.NutritionAdvice, int64, error)
	FindByConditionTypeWithPagination(conditionType string, offset, limit int) ([]*entities.NutritionAdvice, int64, error)
	FindByConditionTypeAndStatus(conditionType string, status int8, offset, limit int) ([]*entities.NutritionAdvice, int64, error)

	// 管理方法
	UpdateStatus(id int64, status int8) error
	ExistsByTitle(title string) (bool, error)
	ExistsByTitleExcludeID(title string, excludeID int64) (bool, error)
}

// nutritionAdviceRepository 营养建议仓储
type nutritionAdviceRepository struct {
	db *gorm.DB
}

// NewNutritionAdviceRepository 创建营养建议仓储实例
func NewNutritionAdviceRepository(db *gorm.DB) INutritionAdviceRepo {
	return &nutritionAdviceRepository{
		db: db,
	}
}

// 确保 nutritionAdviceRepository 实现了 INutritionAdviceRepo 接口
var _ INutritionAdviceRepo = &nutritionAdviceRepository{}

// Create 创建新营养建议
func (r *nutritionAdviceRepository) Create(advice *entities.NutritionAdvice) error {
	if err := r.db.Create(advice).Error; err != nil {
		// 检查是否是重复键错误
		if errs.IsDuplicateKeyError(err) {
			if errs.ContainsField(err.Error(), "title") {
				return errs.New(errs.CodeNutritionAlreadyExists, "nutrition advice title already exists")
			}
		}
		return errs.Wrap(errs.CodeDatabaseError, "failed to create nutrition advice", err)
	}
	return nil
}

// GetByID 根据ID获取营养建议
func (r *nutritionAdviceRepository) GetByID(id int64) (*entities.NutritionAdvice, error) {
	var advice entities.NutritionAdvice
	if err := r.db.First(&advice, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errs.New(errs.CodeNutritionNotFound, "nutrition advice not found")
		}
		return nil, errs.Wrap(errs.CodeDatabaseError, "failed to get nutrition advice by id", err)
	}
	return &advice, nil
}

// Update 更新营养建议
func (r *nutritionAdviceRepository) Update(advice *entities.NutritionAdvice) error {
	if err := r.db.Save(advice).Error; err != nil {
		// 检查是否是重复键错误
		if errs.IsDuplicateKeyError(err) {
			if errs.ContainsField(err.Error(), "title") {
				return errs.New(errs.CodeNutritionAlreadyExists, "nutrition advice title already exists")
			}
		}
		return errs.Wrap(errs.CodeDatabaseError, "failed to update nutrition advice", err)
	}
	return nil
}

// Delete 删除营养建议
func (r *nutritionAdviceRepository) Delete(id int64) error {
	result := r.db.Delete(&entities.NutritionAdvice{}, id)
	if result.Error != nil {
		return errs.Wrap(errs.CodeDatabaseError, "failed to delete nutrition advice", result.Error)
	}
	if result.RowsAffected == 0 {
		return errs.New(errs.CodeNutritionNotFound, "nutrition advice not found")
	}
	return nil
}

// FindByConditionTypeAndPercentage 根据条件类型和百分比查询适用的营养建议
func (r *nutritionAdviceRepository) FindByConditionTypeAndPercentage(conditionType string, percentage int) ([]*entities.NutritionAdvice, error) {
	var advices []*entities.NutritionAdvice
	err := r.db.Where("condition_type = ? AND min_percentage <= ? AND max_percentage >= ? AND status = 1",
		conditionType, percentage, percentage).
		Order("priority DESC").
		Find(&advices).Error

	if err != nil {
		return nil, errs.Wrap(errs.CodeDatabaseError, "failed to find nutrition advice by condition type and percentage", err)
	}
	return advices, nil
}

// FindDefaultAdvice 获取默认建议
func (r *nutritionAdviceRepository) FindDefaultAdvice() (*entities.NutritionAdvice, error) {
	var advice entities.NutritionAdvice
	err := r.db.Where("is_default = 1 AND status = 1").
		Order("priority DESC").
		First(&advice).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errs.New(errs.CodeNutritionNotFound, "default nutrition advice not found")
		}
		return nil, errs.Wrap(errs.CodeDatabaseError, "failed to find default nutrition advice", err)
	}
	return &advice, nil
}

// FindByConditionType 根据条件类型查询所有启用的营养建议
func (r *nutritionAdviceRepository) FindByConditionType(conditionType string) ([]*entities.NutritionAdvice, error) {
	var advices []*entities.NutritionAdvice
	err := r.db.Where("condition_type = ? AND status = 1", conditionType).
		Order("priority DESC").
		Find(&advices).Error

	if err != nil {
		return nil, errs.Wrap(errs.CodeDatabaseError, "failed to find nutrition advice by condition type", err)
	}
	return advices, nil
}

// FindAll 获取所有营养建议（分页）
func (r *nutritionAdviceRepository) FindAll(offset, limit int) ([]*entities.NutritionAdvice, int64, error) {
	var advices []*entities.NutritionAdvice
	var total int64

	// 获取总数
	if err := r.db.Model(&entities.NutritionAdvice{}).Count(&total).Error; err != nil {
		return nil, 0, errs.Wrap(errs.CodeDatabaseError, "failed to count nutrition advice", err)
	}

	// 获取分页数据
	err := r.db.Order("priority DESC, created_at DESC").
		Offset(offset).
		Limit(limit).
		Find(&advices).Error

	if err != nil {
		return nil, 0, errs.Wrap(errs.CodeDatabaseError, "failed to find all nutrition advice", err)
	}

	return advices, total, nil
}

// FindByStatus 根据状态获取营养建议（分页）
func (r *nutritionAdviceRepository) FindByStatus(status int8, offset, limit int) ([]*entities.NutritionAdvice, int64, error) {
	var advices []*entities.NutritionAdvice
	var total int64

	// 获取总数
	if err := r.db.Model(&entities.NutritionAdvice{}).Where("status = ?", status).Count(&total).Error; err != nil {
		return nil, 0, errs.Wrap(errs.CodeDatabaseError, "failed to count nutrition advice by status", err)
	}

	// 获取分页数据
	err := r.db.Where("status = ?", status).
		Order("priority DESC, created_at DESC").
		Offset(offset).
		Limit(limit).
		Find(&advices).Error

	if err != nil {
		return nil, 0, errs.Wrap(errs.CodeDatabaseError, "failed to find nutrition advice by status", err)
	}

	return advices, total, nil
}

// UpdateStatus 更新营养建议状态
func (r *nutritionAdviceRepository) UpdateStatus(id int64, status int8) error {
	result := r.db.Model(&entities.NutritionAdvice{}).
		Where("id = ?", id).
		Update("status", status)

	if result.Error != nil {
		return errs.Wrap(errs.CodeDatabaseError, "failed to update nutrition advice status", result.Error)
	}

	if result.RowsAffected == 0 {
		return errs.New(errs.CodeNutritionNotFound, "nutrition advice not found")
	}

	return nil
}

// ExistsByTitle 检查标题是否已存在
func (r *nutritionAdviceRepository) ExistsByTitle(title string) (bool, error) {
	var count int64
	err := r.db.Model(&entities.NutritionAdvice{}).
		Where("title = ?", title).
		Count(&count).Error

	if err != nil {
		return false, errs.Wrap(errs.CodeDatabaseError, "failed to check nutrition advice title exists", err)
	}

	return count > 0, nil
}

// ExistsByTitleExcludeID 检查标题是否已存在（排除指定ID）
func (r *nutritionAdviceRepository) ExistsByTitleExcludeID(title string, excludeID int64) (bool, error) {
	var count int64
	err := r.db.Model(&entities.NutritionAdvice{}).
		Where("title = ? AND id != ?", title, excludeID).
		Count(&count).Error

	if err != nil {
		return false, errs.Wrap(errs.CodeDatabaseError, "failed to check nutrition advice title exists exclude id", err)
	}

	return count > 0, nil
}

// FindByConditionTypeWithPagination 根据条件类型分页查询营养建议
func (r *nutritionAdviceRepository) FindByConditionTypeWithPagination(conditionType string, offset, limit int) ([]*entities.NutritionAdvice, int64, error) {
	var advices []*entities.NutritionAdvice
	var total int64

	// 构建查询条件
	query := r.db.Where("condition_type = ?", conditionType)

	// 获取总数
	err := query.Model(&entities.NutritionAdvice{}).Count(&total).Error
	if err != nil {
		return nil, 0, errs.Wrap(errs.CodeDatabaseError, "failed to count nutrition advice by condition type", err)
	}

	// 分页查询
	err = query.Order("priority DESC, created_at DESC").
		Offset(offset).Limit(limit).
		Find(&advices).Error
	if err != nil {
		return nil, 0, errs.Wrap(errs.CodeDatabaseError, "failed to find nutrition advice by condition type with pagination", err)
	}

	return advices, total, nil
}

// FindByConditionTypeAndStatus 根据条件类型和状态分页查询营养建议
func (r *nutritionAdviceRepository) FindByConditionTypeAndStatus(conditionType string, status int8, offset, limit int) ([]*entities.NutritionAdvice, int64, error) {
	var advices []*entities.NutritionAdvice
	var total int64

	// 构建查询条件
	query := r.db.Where("condition_type = ? AND status = ?", conditionType, status)

	// 获取总数
	err := query.Model(&entities.NutritionAdvice{}).Count(&total).Error
	if err != nil {
		return nil, 0, errs.Wrap(errs.CodeDatabaseError, "failed to count nutrition advice by condition type and status", err)
	}

	// 分页查询
	err = query.Order("priority DESC, created_at DESC").
		Offset(offset).Limit(limit).
		Find(&advices).Error
	if err != nil {
		return nil, 0, errs.Wrap(errs.CodeDatabaseError, "failed to find nutrition advice by condition type and status", err)
	}

	return advices, total, nil
}
